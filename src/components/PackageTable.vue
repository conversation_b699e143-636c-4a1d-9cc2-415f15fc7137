<template>
  <v-card class="parent-cont mb-0" width="100%">
    <v-container fluid grid-list-lg class="pa-0">
      <v-data-table
        :headers="headers"
        :items="items"
        class="custom-table"
        density="compact"
        items-per-page="-1"
      >
        <template v-slot:item="{ item, index }">
          <tr class="result-elem default">
            <td
              v-for="(header, i) in headers"
              :key="header.key + i"
              class="py-4 text-center"
            >
              <v-text-field
                v-if="header.key == 'quantity'"
                v-model="item[header.key]"
                density="compact"
                type="number"
                variant="outlined"
                hide-details
                color="primary"
                @keydown.up.prevent
                @keydown.down.prevent
                :rules="[rules.require, rules.positive]"
                ><template v-slot:append-inner>
                  {{ unit?.symbol || "" }}
                </template></v-text-field
              >
              <v-text-field
                v-else-if="header.key != 'actions' && header.key != 'unitCost'"
                v-model="item[header.key]"
                density="compact"
                type="text"
                variant="outlined"
                hide-details
                color="primary"
                :rules="header.key == 'packageCode' ? [] : [rules.require]"
              ></v-text-field>
              <currency-input
                v-else-if="header.key == 'unitCost'"
                v-model="item.unitCost"
                @valueChange="(v) => (item.unitCost = v)"
              ></currency-input>
              <div v-else class="d-flex justify-center align-center">
                <v-icon color="error" @click="removeRow(index)">
                  mdi-close
                </v-icon>
              </div>
            </td>
          </tr>
        </template>
        <template v-slot:body.append>
          <tr class="result-elem default">
            <td
              v-for="(header, ind) in headers"
              :key="header.key + ind"
              class="py-4 text-center"
            >
              <v-text-field
                v-if="header.key == 'quantity'"
                v-model="newRow[header.key]"
                density="compact"
                type="number"
                variant="outlined"
                hide-details
                color="primary"
                :rules="[rules.positive]"
              >
                <template v-slot:append-inner>
                  {{ unit?.symbol || "" }}
                </template></v-text-field
              >
              <v-text-field
                v-else-if="header.key != 'actions' && header.key != 'unitCost'"
                v-model="newRow[header.key]"
                density="compact"
                type="text"
                variant="outlined"
                hide-details
                color="primary"
                :ref="ind == 0 ? 'inputRef' : null"
              ></v-text-field>
              <currency-input
                v-else-if="header.key == 'unitCost'"
                v-model="newRow.unitCost"
                @valueChange="(v) => (newRow.unitCost = v)"
                @keypress.enter="addRow(newRow)"
              ></currency-input>
              <div v-else class="d-flex justify-center align-center">
                <v-icon
                  color="green"
                  :disabled="!isRowValid(newRow)"
                  @click="commitNewRow"
                >
                  mdi-plus
                </v-icon>
              </div>
            </td>
          </tr>
        </template>
      </v-data-table></v-container
    ></v-card
  >
</template>
<script setup>
import { ref, nextTick } from "vue";
import { packageHeaders } from "@/helpers/tableHeaders";
import { packageRecord as DEFAULT_PACKAGE } from "@/helpers/defaultRecords";
import CurrencyInput from "@/components/CurrencyInput.vue";
import rules from "@/helpers/rules";

defineProps({
  unit: {
    type: Object,
    default: () => {},
  },
});

const items = defineModel();
const inputRef = ref(null);
const headers = packageHeaders;

const newRow = ref({ ...DEFAULT_PACKAGE });

const isRowValid = (row) => {
  return headers
    .filter((h) => h.key !== "actions" && h.key !== "packageCode")
    .every((h) => {
      if (h.key == "quantity") return row[h.key] > 0;
      return row[h.key];
    });
};

const commitNewRow = () => {
  if (!isRowValid(newRow.value)) return;
  items.value.push({ ...newRow.value });

  newRow.value = { ...DEFAULT_PACKAGE };
  nextTick(() => {
    inputRef.value[0].focus();
  });
};

const addRow = (row) => {
  if (!isRowValid(row)) return;
  commitNewRow();
};
const removeRow = (index) => {
  items.value.splice(index, 1);
};
</script>
<style scoped>
.custom-table >>> .v-data-table-rows-no-data {
  display: none;
}
</style>
