<template>
  <v-dialog v-model="dialog" max-width="800px" persistent>
    <v-card>
      <v-card-title>Prepare Item</v-card-title>
      <v-card-text>
        <v-row>
          <v-col>
            <v-autocomplete
              v-model="selectedItem"
              label="Inventory Item"
              color="primary"
              hide-details="auto"
              variant="outlined"
              density="compact"
              :items="inventoryItems"
              item-title="itemName"
              item-value="id"
              return-object
              clearable
            ></v-autocomplete>
          </v-col>
          <v-col>
            <v-text-field
              v-model.number="desiredQuantity"
              label="Desired Quantity"
              type="number"
              min="1"
              required
              density="compact"
              variant="outlined"
              hide-details
              color="primary"
              @keyup.enter="submit"
            >
              <template v-slot:append-inner>
                {{ selectedItem?.recipeUnit.symbol }}
              </template>
            </v-text-field>
          </v-col>
        </v-row>

        <v-row v-if="details">
          <v-card-title>Ingredients</v-card-title>

          <v-card class="parent-cont ma-3 mb-0" width="100%">
            <v-container fluid class="pa-0 parent-cont">
              <v-data-table
                :headers="prepareHeaders"
                :items="details"
                :hide-default-footer="details && details.length < 11"
              >
                <template v-slot:item.reason="{ item }">
                  <div :class="item.reason ? 'text-red' : 'text-black'">
                    {{ item.reason || "-" }}
                  </div>
                </template>
                <template v-slot:item.required="{ item }">
                  <div>
                    {{ item.required }}
                    <span class="font-weight-medium">{{ item.symbol }}</span>
                  </div>
                </template>
                <template v-slot:item.availableQuantity="{ item }">
                  <div>
                    {{ item.availableQuantity }}
                    <span class="font-weight-medium">{{ item.symbol }}</span>
                  </div>
                </template>
              </v-data-table>
            </v-container>
          </v-card>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-spacer />
        <v-btn @click="close" variant="outlined" color="primary"
          ><v-icon>mdi-close</v-icon>Close</v-btn
        >
        <v-btn text="Prepare" @click="submit" variant="flat" color="primary" />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, onBeforeMount, computed, watch } from "vue";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useStockStore } from "@/stores/stock";
import { prepareHeaders } from "@/helpers/tableHeaders";

const dialog = defineModel();
const props = defineProps({
  modelValue: Boolean,
  location: Object,
});
const emit = defineEmits(["update:modelValue"]);

const desiredQuantity = ref(null);
const selectedItem = ref(null);

const details = ref(null);

const stockStore = useStockStore();

const inventoryItemStore = useInventoryItemStore();
const inventoryItems = computed(() =>
  inventoryItemStore.getInventoryItems.filter((i) => i.itemType == "made")
);

const close = () => {
  emit("update:modelValue", false);
  desiredQuantity.value = null;
  selectedItem.value = null;
  details.value = null;
};

const submit = async () => {
  const payload = {
    location: {
      id: props.location.id,
      name: props.location.name,
    },
    desiredQuantity: desiredQuantity.value,
    itemId: selectedItem.value.id,
  };
  try {
    await stockStore.prepareStocks(payload);
    close();
  } catch (error) {
    details.value = error.details;
  }
};

// watch(
//   () => dialog.value,
//   (val) => {
//     if (val && props.item && inventoryItems.value.length > 0) {
//       console.log("wwwwwwww");
//       selectedItem.value = inventoryItems.value.find((item) => {
//         return item.id === props.item.inventoryItem.id;
//       });
//       console.log(selectedItem.value);
//     }
//   }
// );
onBeforeMount(async () => {
  const inventoryItemPromise = inventoryItemStore.fetchInventoryItems();

  await Promise.all([inventoryItemPromise]);
});
</script>
