<template>
  <v-dialog v-model="model" width="420" persistent>
    <v-card class="custom-card">
      <v-card-title
        class="d-flex justify-space-between align-center dialog-title-background"
      >
        <p>
          {{ isEdit ? "Edit Role" : "Invite User" }}
        </p>
        <v-btn
          variant="text"
          icon="mdi-close"
          color="error"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider />

      <v-card-text class="py-4" style="padding: 15px;">
        <v-form ref="form">
          <v-text-field
            v-if="!isEdit"
            v-model.trim="user.name"
            label="Name*"
            color="primary"
            class="mb-4"
            variant="outlined"
            density="compact"
            hide-details="auto"
            @blur="cleanName"
            :rules="[rules.require, rules.maxLength(100)]"         
          />

          <v-text-field
            v-if="!isEdit"
            v-model.trim="user.emailId"
            label="Email*"
            color="primary"
            class="mb-4"
            variant="outlined"
            density="compact"
            hide-details="auto"
           :rules="[rules.email, rules.require]"
          />

          <v-autocomplete
            v-model="user.roleId"
            :items="roles"
            item-value="id"
            item-title="name"
            label="Role*"
            hide-details
            variant="outlined"
            density="compact"
            color="primary"
            class="mb-4"
            clearable
            :rules="[rules.require]"
          />

          <v-switch
            v-model="allLocationsToggle"
            hide-details
            density="compact"
            :color="allLocationsToggle ? 'green' : 'grey'"
            inset
            class="small-switch mb-4"
            @change="toggleLocations"
          >
            <template #label>
              <span class="switch-text">All Locations</span>
            </template>
          </v-switch>

          <v-autocomplete
            v-if="!allLocationsToggle"
            v-model="user.stores"
            label="Locations"
            color="primary"
            hide-details="auto"
            variant="outlined"
            class="mb-4"
            density="compact"
            :items="stores"
            multiple
            item-title="name"
            item-value="id"
            return-object
            clearable
          >
            <template v-slot:prepend-item>
              <v-list-item ripple @click="selectAllStores">
                <div class="d-flex align-center px-2">
                  <v-icon
                    :icon="allStoresSelected
                      ? 'mdi-checkbox-marked'
                      : 'mdi-checkbox-blank-outline'"
                  />
                  <v-list-item-title class="mx-2">
                    Select All
                  </v-list-item-title>
                </div>
              </v-list-item>
              <v-divider class="mt-2" />
            </template>

            <template v-slot:selection="{ index }">
              <span v-if="index === 0">{{ storesSelectionText }}</span>
            </template>
          </v-autocomplete>

          <v-switch
            v-model="allWorkAreasToggle"
            hide-details
            density="compact"
            :color="allWorkAreasToggle ? 'green' : 'grey'"
            inset
            class="small-switch mb-4"
            @change="toggleWorkAreas"
          >
            <template #label>
              <span class="switch-text">All Work/Storage Area</span>
            </template>
          </v-switch>

          <v-autocomplete
            v-if="!allWorkAreasToggle"
            v-model="user.locations"
            label="Work/Storage Area"
            color="primary"
            hide-details="auto"
            variant="outlined"
            density="compact"
            :items="filteredLocations"
            multiple
            item-title="name"
            item-value="id"
            return-object
            clearable
          >
            <template v-slot:prepend-item>
              <v-list-item ripple @click="selectAllLocations">
                <div class="d-flex align-center px-2">
                  <v-icon
                    :icon="allLocationsSelected
                      ? 'mdi-checkbox-marked'
                      : 'mdi-checkbox-blank-outline'"
                  />
                  <v-list-item-title class="mx-2">
                    Select All
                  </v-list-item-title>
                </div>
              </v-list-item>
              <v-divider class="mt-2" />
            </template>

            <template v-slot:selection="{ index }">
              <span v-if="index === 0">{{ locationsSelectionText }}</span>
            </template>
          </v-autocomplete>  
        </v-form>
      </v-card-text>

      <v-divider />

      <v-card-actions class="d-flex flex-column ma-2">
        <p class="text-primary text-caption text-center">
          * Indicates a Required Field.
        </p>
        <div class="d-flex justify-end ga-2 w-100">
          <v-btn
            color="primary"
            variant="flat"
            :loading="isSubmitLoading"
            @click="submit(true)"
          >
            Save
          </v-btn>
          <v-btn
            v-if="!isEdit"
            color="primary"
            variant="flat"
            @click="submit(false)"
            :loading="isSubmitContinueLoading"
          >
            Save & Continue
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed, defineModel, onBeforeMount, watch } from "vue";
import { getRoles } from "@/services/roleService";
import { formatName } from "@/helpers/formatter";
import rules from "@/helpers/rules";
import { useSnackbarStore } from "@/stores/snackBar";

const snackbarStore = useSnackbarStore();

const props = defineProps({
  isEdit: Boolean,
  userData: Object,
  locations: Array,
  stores: Array,
});

const model = defineModel();
const emit = defineEmits(["createUser", "updateUser"]);
const form = ref(null);

const user = ref({ name: "", roleId: "", emailId: "", stores: [], locations: [] });
const roles = ref([]);
const isSubmitLoading = ref(false);
const isSubmitContinueLoading = ref(false);

const allLocationsToggle = ref(true);
const allWorkAreasToggle = ref(true);

const filteredLocations = computed(() => {
  const storeIds = user.value.stores.map(s => s.id);
  return props.locations.filter(loc => storeIds.includes(loc.storeId));
});

const allStoresSelected = computed(() => user.value.stores.length === props.stores.length);
const allLocationsSelected = computed(() => user.value.locations.length === filteredLocations.value.length);

const toggleLocations = () => {
  if (allLocationsToggle.value) {
    user.value.stores = [...props.stores];
  } else {
    user.value.stores = [...props.stores];
  }
};

const toggleWorkAreas = () => {
  if (allWorkAreasToggle.value) {
    user.value.locations = [...filteredLocations.value];
  } else {
    user.value.locations = [...filteredLocations.value];
  }
};

const selectAllStores = () => {
  if (allStoresSelected.value) {
    user.value.stores = []; 
  } else {
    user.value.stores = [...props.stores]; 
  }
};

const selectAllLocations = () => {
  if (allLocationsSelected.value) {
    user.value.locations = []; 
  } else {
    user.value.locations = [...filteredLocations.value]; 
  }
};

const storesSelectionText = computed(() => selectionText(user.value.stores));
const locationsSelectionText = computed(() => selectionText(user.value.locations));

const selectionText = (items) => {
  return items.length === 1 ? items[0].name : `${items[0].name} (+${items.length - 1} others)`;
};

watch(model, (open) => {
  if (!open) return;

  if (props.isEdit && props.userData) {
    user.value = { ...props.userData };
    allLocationsToggle.value = user.value.stores.length === props.stores.length;
    allWorkAreasToggle.value = user.value.locations.length === filteredLocations.value.length;
  } else resetForm();
});

watch(() => user.value.stores, (newStores) => {
  const storeIds = newStores.map(s => s.id);
  const filtered = props.locations.filter(loc => storeIds.includes(loc.storeId));
  if (allWorkAreasToggle.value) {
    user.value.locations = [...filtered];
  } else {
    user.value.locations = user.value.locations.filter(loc => storeIds.includes(loc.storeId));
  }
});

const resetForm = () => {
  user.value = { name: "", roleId: "", emailId: "", stores: [...props.stores], locations: [...props.locations] };
  allLocationsToggle.value = true;
  allWorkAreasToggle.value = true;
  if (form.value) form.value.resetValidation();
};

const cleanName = () => { user.value.name = formatName(user.value.name); };

const submit = async (closeAfter = true) => {
  const loader = closeAfter ? isSubmitLoading : isSubmitContinueLoading;
  if (loader.value) return;
  loader.value = true;

  try {
    const { valid } = await form.value.validate();
    if (!valid) {
      loader.value = false;
      return;
    }
    const role = roles.value.find((r) => r.id === user.value.roleId);
    const roleName = role?.name || "";

    const storeIds = user.value.stores.map(store => store.id);
    const locationIds = user.value.locations.map(loc => loc.id);

    const { stores, locations, ...rest } = user.value;

    if (!allStoresSelected.value && storeIds.length === 0) {
      snackbarStore.showSnackbar("primary", "Minimum One Location is Required.");
      loader.value = false;
      return;
    }

    if (!allLocationsSelected.value && locationIds.length === 0) {
      snackbarStore.showSnackbar("primary", "Minimum One Storage/Work Area is Required.");
      loader.value = false;
      return;
    }

    const payload = {
      ...rest,
      roleName,
      allLocations: allStoresSelected.value,
      allInventoryLocations: allLocationsSelected.value
    }; 

    if (!allStoresSelected.value) {
      payload.locationIds = storeIds;
    }

    if (!allLocationsSelected.value) {
      payload.inventoryLocationIds = locationIds;
    }

    const done = (success) => {
      loader.value = false;
      if (!success) return;

      if (closeAfter) {
        model.value = false;
      } else {
        resetForm();
      }
    };

    if (props.isEdit) {
      emit("updateUser", payload, done);
    } else {
      emit("createUser", payload, done);
    }
  } catch (error) {
    console.error(error);
    loader.value = false;
  }
};

const closeDialog = () => {
  model.value = false;
};

onBeforeMount(async () => {
  try {
    const res = await getRoles();
    if (res?.status === "success" && res.payload) {
      roles.value = res.payload;
    }
  } catch (error) {
    console.error(error);
  }
});
</script>

<style scoped>
.small-switch {
  transform: scale(0.8);
  transform-origin: left;
}

.switch-text {
  font-size: 20px;
}
</style>
