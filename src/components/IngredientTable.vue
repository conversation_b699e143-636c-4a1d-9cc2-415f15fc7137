<template>
  <v-card class="parent-cont mb-0" width="100%">
    <v-container fluid grid-list-lg class="pa-0">
      <v-data-table
        :headers="headers"
        :items="items"
        class="custom-table"
        density="compact"
        items-per-page="-1"
      >
        <template v-slot:item="{ item, index }">
          <tr class="result-elem default">
            <td
              v-for="(header, i) in headers"
              :key="header.key + i"
              class="py-4 text-center"
            >
              <v-autocomplete
                v-if="header.key == 'name'"
                v-model="item.name"
                :items="inventoryList"
                item-title="itemName"
                item-value="id"
                density="compact"
                variant="outlined"
                single-line
                hide-details
                color="primary"
                :rules="[rules.require]"
                return-object
                @update:model-value="(val) => setValues(val, item)"
                clearable
              ></v-autocomplete>
              <v-text-field
                v-else-if="header.key == 'quantity'"
                v-model="item.quantity"
                type="number"
                density="compact"
                variant="outlined"
                hide-details
                color="primary"
                :rules="[rules.require, rules.positive]"
                @keydown.up.prevent
                @keydown.down.prevent
              >
                <template v-slot:append-inner>
                  {{ item.recipeUnit?.symbol || "" }}
                </template>
              </v-text-field>
              <div v-else class="d-flex justify-center align-center">
                <v-icon color="error" @click="removeRow(index)">
                  mdi-close
                </v-icon>
              </div>
            </td>
          </tr>
        </template>
        <template v-slot:body.append>
          <tr class="result-elem default">
            <td
              v-for="(header, ind) in headers"
              :key="header.key + ind"
              class="py-4 text-center"
            >
              <v-autocomplete
                v-if="header.key == 'name'"
                v-model="newRow.name"
                :items="inventoryList"
                item-title="itemName"
                item-value="id"
                density="compact"
                variant="outlined"
                single-line
                hide-details
                color="primary"
                return-object
                @update:model-value="(val) => setValues(val, null)"
                :ref="ind == 0 ? 'inputRef' : null"
                clearable
              ></v-autocomplete>
              <v-text-field
                v-else-if="header.key == 'quantity'"
                v-model="newRow.quantity"
                type="number"
                density="compact"
                variant="outlined"
                hide-details
                color="primary"
                @keydown.up.prevent
                @keydown.down.prevent
                @keypress.enter="addRow(newRow)"
                :rules="[rules.positive]"
              >
                <template v-slot:append-inner>
                  {{ newRow.recipeUnit?.symbol || "" }}
                </template>
              </v-text-field>
              <div v-else class="d-flex justify-center align-center">
                <v-icon
                  color="green"
                  :disabled="!isRowValid(newRow)"
                  @click="commitNewRow"
                >
                  mdi-plus
                </v-icon>
              </div>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-container>
  </v-card>
</template>
<script setup>
import { ref, nextTick } from "vue";
import rules from "@/helpers/rules";
import { ingredientHeaders } from "@/helpers/tableHeaders";
import { ingredientRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";

defineProps({
  inventoryList: {
    type: Array,
    default: () => [],
  },
});

const items = defineModel();
const inputRef = ref(null);
const headers = ingredientHeaders;

const newRow = ref({ ...DEFAULT_RECORD });

const isRowValid = (row) => {
  return headers
    .filter((h) => h.key !== "actions")
    .every((h) => {
      if (h.key == "quantity") return row[h.key] > 0;
      return row[h.key];
    });
};
const commitNewRow = () => {
  if (!isRowValid(newRow.value)) return;
  items.value.push({ ...newRow.value });

  newRow.value = { ...DEFAULT_RECORD };
  nextTick(() => {
    inputRef.value[0].focus();
  });
};
const addRow = (row) => {
  if (!isRowValid(row)) return;
  commitNewRow();
};
const removeRow = (index) => {
  items.value.splice(index, 1);
};

const setValues = (val, row = null) => {
  const target = row || newRow.value;
  if (!val) return;
  target.name = val.itemName;
  target.id = val.id;
  target.code = val.itemCode;
  target.purchaseUnit = val.purchaseUnit;
  target.countingUnit = val.countingUnit;
  target.recipeUnit = val.recipeUnit;
  target.unitCost = val.unitCost;
};
</script>

<style scoped>
.custom-table >>> .v-data-table-rows-no-data {
  display: none;
}
</style>
