<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12" v-for="filter in filters" :key="filter.key">
        <component
          :is="getComponent(filter.componentId)"
          v-bind="getProps(filter)"
          v-model="internalValues[filter.key]"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { reactive, watch, defineProps, defineEmits, defineAsyncComponent } from "vue";

// Props
const props = defineProps({
  filters: { type: Array, default: () => [] }, // [{ componentId, key, title, items }]
  modelValue: { type: Object, default: () => ({}) },
});

// Emits
const emit = defineEmits(["update:modelValue"]);

// Reactive state for internal v-model
const internalValues = reactive({ ...props.modelValue });

// Sync internal changes to parent
watch(
  internalValues,
  (val) => emit("update:modelValue", val),
  { deep: true }
);

// Update internal state if parent changes
watch(
  () => props.modelValue,
  (val) => Object.assign(internalValues, val),
  { deep: true }
);

// Lazy load component dynamically
const getComponent = (componentId) => {
  // Use defineAsyncComponent for lazy loading
  return defineAsyncComponent(() => import(`./filters/${componentId}.vue`));
};

// Remove unnecessary keys for props
const getProps = (filter) => {
  const { componentId, key, ...rest } = filter;
  return rest;
};
</script>
