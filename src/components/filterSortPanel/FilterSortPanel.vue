<template>
  <div class="filter-drawer">
    <!-- Activator slot outside the drawer -->
    <div v-if="!hideDefaultActivator">
      <slot name="activator" :toggleFilter="toggleFilter">
        <v-btn variant="tonal" color="primary" @click="toggleFilter">
          <v-icon class="mr-2" icon="mdi-filter-variant"></v-icon>
          <span>Filters</span>
        </v-btn>
      </slot>
    </div>

    <!-- filter Drawer -->
    <v-navigation-drawer
      v-model="openFilter"
      location="right"
      :width="width"
      order="-1"
      persistent
      temporary
    >
      <v-card flat tile>
        <v-card-text class="pa-0">
          <!-- Tabs Content -->
          <v-tabs-window v-model="activeTab">
            <!-- Filters Tab -->
            <v-tabs-window-item :value="1" v-if="props.enableFilters">
              <slot name="filters">
                <Filters v-model="internalState.filters" :filters="filterComponents" />
              </slot>
            </v-tabs-window-item>

            <!-- Columns Tab -->
            <v-tabs-window-item :value="2" v-if="props.enableColumns">
              <slot name="columns">
                <column-selection ref="columnRef" v-model="internalState.columns" />
              </slot>
            </v-tabs-window-item>

            <!-- Options Tab -->
            <v-tabs-window-item :value="3" v-if="props.enableOptions">
              <slot name="options" />
            </v-tabs-window-item>
          </v-tabs-window>
        </v-card-text>
      </v-card>

      <!-- Header: Tabs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar class="dialog-title-background">
          <v-tabs v-model="activeTab" slider-color="primary" v-if="visibleTabs.length > 0">
            <v-tab v-for="tab in visibleTabs" :key="tab.key" :value="tab.value">{{ tab.label }}</v-tab>
          </v-tabs>
          <v-spacer></v-spacer>
          <v-btn variant="text" icon="mdi-close" color="error" @click="closeDrawer" />
        </v-toolbar>
      </template>

      <!-- Footer: Apply Button -->
      <template v-slot:append>
        <v-divider></v-divider>
        <div class="pa-2">
          <v-btn block color="primary" @click="apply">{{ applyButtonText }}</v-btn>
        </div>
      </template>
    </v-navigation-drawer>
  </div>
</template>

<script setup>
import { ref, computed, watch, reactive } from "vue";

import Filters from "./Filters.vue";
import ColumnSelection from "./ColumnSelection.vue";

// Props
const props = defineProps({
  width: { type: Number, default: 420 },
  hideDefaultActivator: { type: Boolean, default: false },
  enableFilters: { type: Boolean, default: true },
  filterComponents: { type: Array, default: () => [] },
  enableColumns: { type: Boolean, default: false },
  enableOptions: { type: Boolean, default: false },
  applyButtonText: { type: String, default: "Apply" },
  modelValue: {
    // reactive state
    type: Object,
    default: () => ({
      filters: {},
      columns: [],
      options: {}
    })
  }
});

// Emits
const emit = defineEmits(["apply"]);

// Drawer & Tabs State
const openFilter = ref(false);
const activeTab = ref(1);
const columnRef = ref(null);

// Internal reactive state
const internalState = reactive({
  filters: {},
  columns: [],
  options: {}
});

const resetFilter = () => {
  const val = props.modelValue;
  internalState.filters = JSON.parse(JSON.stringify(val.filters || {}));
  internalState.columns = JSON.parse(JSON.stringify(val.columns || []));
  internalState.options = JSON.parse(JSON.stringify(val.options || {}));
};

// Sync internal state when parent modelValue changes
watch(() => props.modelValue, resetFilter, {
  deep: true,
  immediate: true
});

// Predefined tabs
const allTabs = [
  { label: "Filters", key: "filters", value: 1, enabled: props.enableFilters },
  { label: "Columns", key: "columns", value: 2, enabled: props.enableColumns },
  { label: "Options", key: "options", value: 3, enabled: props.enableOptions }
];

// Only show enabled tabs
const visibleTabs = computed(() => allTabs.filter(tab => tab.enabled));

// Methods
const apply = () => {
  columnRef.value?.commitChanges();
  const val = JSON.parse(JSON.stringify(internalState));
  emit("apply", val);
  closeDrawer();
};

const closeDrawer = () => {
  openFilter.value = false;
};

// Expose toggle function for external control
const toggleFilter = () => {
  openFilter.value = !openFilter.value;
};

defineExpose({ toggleFilter });
</script>

<style scoped></style>
