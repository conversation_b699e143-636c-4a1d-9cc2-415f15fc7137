<template>
  <div>
    <v-data-table
      :items="results"
      item-value="id"
      :headers="headers"
      :items-per-page="pagination.rowsPerPage"
      :page="pagination.page"
      @update:page="pagination.page = $event"
      hide-default-footer
    >
      <template v-slot:item="{ item }">
        <tr>
          <td class="text-left">{{ item.label }}</td>
          <td class="text-center">{{ item.total_count || 0 }}</td>
          <td class="text-right">{{ item.amountFmt || "0.00" }}</td>
        </tr>
      </template>
    </v-data-table>
    <div class="text-center pt-2" v-if="pages > 1">
      <v-pagination v-model="pagination.page" :length="pages"></v-pagination>
    </div>
  </div>
</template>

<script>
import { ref, computed } from "vue";

export default {
  name: "WidgetList",
  props: {
    data: {
      type: Object,
      required: true,
    },
    helper: {
      type: Object,
      default: () => ({
        label: "LABEL",
        qty: "COUNT",
        value: "VALUE",
      }),
    },
  },
  setup(props) {
    const pagination = ref({
      rowsPerPage: 6,
      page: 1,
    });

    const headers = computed(() => [
      {
        title: props.helper.label,
        key: "label",
        align: "start",
      },
      { title: props.helper.qty, key: "total_count", align: "center" },
      { title: props.helper.value, key: "Amount", align: "end" },
    ]);

    const results = computed(() => Object.values(props.data));

    const pages = computed(() =>
      Math.ceil(results.value.length / pagination.value.rowsPerPage)
    );

    return { headers, results, pagination, pages };
  },
};
</script>
