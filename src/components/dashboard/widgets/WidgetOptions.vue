<template>
  <v-row align="center">
    <v-col cols="6">
      <span class="text-primary text-body-2 text-uppercase">
        {{ title }}
      </span>
    </v-col>
    <v-col cols="6" class="text-end">
      <v-menu bottom transition="scale-transition" offset-y>
        <template v-slot:activator="{ props }">
          <v-btn
            v-bind="props"
            color="primary"
            small
            density="comfortable"
            variant="tonal"
          >
            <v-icon small class="pr-2" :icon="selected.icon"></v-icon>
            <span class="text-caption">{{ selected.name }}</span>
          </v-btn>
        </template>
        <v-list density="compact" class="graph-options">
          <v-list-item
            v-for="option in options"
            :key="`graph-option-title--${option.id}`"
            :class="{ selected: option.id === selected.id }"
            @click="onChange(option)"
            v-show="option.enable"
          >
            <v-list-item-title class="text-caption d-flex align-center">
              <v-icon small left :icon="option.icon" class="me-2"></v-icon>
              {{ option.name }}
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-col>
  </v-row>
</template>

<script setup>
import { ref, defineProps, defineEmits, onBeforeMount } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },

  disableList: {
    type: Boolean,
    default: false,
  },
  helper: {
    type: Object,
    default: () => ({
      qty: "COUNT",
      value: "VALUE",
    }),
  },
});

const viewMode = defineModel();

const selected = ref(null);
const options = ref([
  {
    id: 2,
    icon: "mdi-chart-box-outline",
    name: `BY ${props.helper.value}`,
    enable: true,
  },
  {
    id: 3,
    icon: "mdi-chart-box-outline",
    name: `BY ${props.helper.qty}`,
    enable: true,
  },
  {
    id: 1,
    icon: "mdi-view-list",
    name: "LIST VIEW",
    enable: !props.disableList,
  },
]);

const onChange = (option) => {
  viewMode.value = option.id;
  selected.value = option;
};

onBeforeMount(() => {
  selected.value =
    options.value.find((option) => option.id === viewMode.value) ||
    options.value[0];
});
</script>

<style>
.graph-options .selected {
  background-color: #ff5a10;
  color: white;
}
.graph-options .selected .v-icon {
  background-color: #ff5a10;
  color: white;
}
</style>
