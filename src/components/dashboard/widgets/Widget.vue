<template>
  <v-card class="custom-card">
    <v-card-title class="py-2">
      <widget-options
        :title="widget.title"
        v-model="viewMode"
        :helper="helper"
      />
    </v-card-title>
    <v-divider></v-divider>
    <v-card-text
      :class="{
        'd-flex align-center justify-center': graphMode,
        'pt-0 px-0': !graphMode,
      }"
    >
      <!-- graph mode (by value) -->
      <widget-graphs
        key="graph-mode-2"
        v-if="viewMode === 2"
        :graph-data="widget.result"
        :view-mode="viewMode"
        :top-n="widget.topN"
        :title="widget.byValue"
      ></widget-graphs>

      <!-- graph mode (by qty) -->
      <widget-graphs
        key="graph-mode-3"
        v-else-if="viewMode === 3"
        :graph-data="widget.result"
        :view-mode="3"
        :top-n="widget.topN"
        :title="widget.byQty"
      />
      <!-- list view -->
      <widget-list
        v-else
        key="list-mode"
        :data="widget.result"
        :helper="helper"
      ></widget-list>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { ref, computed, onMounted, defineProps } from "vue";
import WidgetOptions from "./WidgetOptions.vue";
import WidgetGraphs from "./WidgetGraphs.vue";
import WidgetList from "./WidgetList.vue";

const props = defineProps({
  widget: {
    type: Object,
    required: true,
  },
});

const viewMode = ref(2);
const helper = ref({
  label: props.widget.title,
  qty: props.widget.byQty,
  value: props.widget.byValue,
});

const graphMode = computed(() => viewMode.value !== 1);

onMounted(() => {
  viewMode.value = props.widget.viewMode || 2;
});
</script>
