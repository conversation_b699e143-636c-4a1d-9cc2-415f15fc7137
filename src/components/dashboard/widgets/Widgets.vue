<template>
  <v-container fluid class="result-child">
    <v-row>
      <v-col
        v-for="widget in widgets"
        :key="widget.id"
        cols="12"
        sm="6"
        md="6"
        xl="4"
        class="category-elem"
      >
        <Widget :widget="widget" />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import Widget from "./Widget.vue";
import { defineProps } from "vue";

defineProps({
  widgets: {
    type: Array,
    required: true,
  },
});
</script>

<style scoped>
.result-child {
  padding-top: 20px !important;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}

/* .chart-cont {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
} */

.chart-cont::before,
.chart-cont::after {
  content: "";
}

.category-elem {
  padding: 12px !important;
}
</style>
