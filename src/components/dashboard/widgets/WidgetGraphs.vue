<template>
  <div class="chart-container">
    <pie-chart
      :chart-data="chartData"
      :chartOptions="chartOptions(viewMode, title)"
    />
  </div>
</template>

<script setup>
import { computed } from "vue";
import { chartOptions } from "@/components/dashboard/charts/helper.js";
import <PERSON><PERSON><PERSON> from "../charts/PieChart.vue";
import { getBgColors } from "../charts/colors";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  graphData: {
    type: Object,
    required: true,
  },
  viewMode: {
    type: Number,
    default: 2,
  },
  topN: {
    type: Number,
  },
});

const getTopN = (graphData, n) => {
  const values = Object.values(graphData).sort((a, b) => {
    return props.viewMode !== 2
      ? b.total_count - a.total_count
      : b.Amount - a.Amount;
  });
  return !n || values.length <= n ? values : values.slice(0, n);
};

// const chartOption = computed(() => chartOptions(props.viewMode, props.title));

const chartData = computed(() => {
  const graphData = getTopN(props.graphData, props.topN);
  let labels = graphData.map((gd) => gd.label);
  let data = graphData.map((gd) =>
    props.viewMode !== 2 ? gd.total_count : gd.Amount
  );
  return {
    labels,
    datasets: [
      {
        data,
        backgroundColor: getBgColors(data.length),
      },
    ],
  };
});
</script>
