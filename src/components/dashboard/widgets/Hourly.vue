<template>
  <v-card class="custom-card" style="min-height: 348px">
    <v-card-title class="py-2">
      <widget-options
        title="Hourly"
        v-model="viewMode"
        :helper="{
          qty: 'SALES COUNT',
          value: 'TOTAL',
        }"
        disable-list
      />
    </v-card-title>
    <v-divider></v-divider>
    <v-card-text class="pa-0">
      <line-chart
        :key="`hourly-graph-mode--${viewMode}`"
        :chart-data="saleChartData"
        :chartOptions="chartOptions()"
      ></line-chart>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { computed, ref, onMounted, watch } from "vue";
import LineChart from "../charts/LineChart.vue";
import WidgetOptions from "./WidgetOptions.vue";
import { tooltips } from "../charts/helper";
import { getBgColors } from "../charts/colors";
import { formatCurrency } from "@/components/utils/money";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const viewMode = ref(2);
const symbol = ref("");

const chartOptions = () => ({
  maintainAspectRatio: true,
  responsive: true,
  borderWidth: 2,
  borderColor: "red",
  plugins: {
    legend: {
      display: false,
      position: "bottom",
      labels: {
        usePointStyle: true,
      },
    },
    tooltip: {
      callbacks: {
        label: (tooltipItem, data) => {
          return tooltips(tooltipItem, data, viewMode.value);
        },
      },
    },
  },
  scales: {
    y: {
      grid: {
        zeroLineColor: "#223345",
        zeroLineWidth: 2,
      },
      ticks: {
        callback: function (value) {
          let val = value.toString().split(".").join("");
          let m = val;
          if (viewMode.value === 2) {
            m = formatCurrency(val);
          }
          // return `${this.symbol} ${m}`;
          return `${m}`;
        },
      },
    },
    x: {
      grid: {},
      stacked: true,
    },
  },
});

const saleChartData = computed(() => {
  if (!props.data) return {};

  const colors = getBgColors(5);
  let labels = [];
  let totalActivity = [];

  for (const hr in props.data.timing) {
    const d = props.data.timing[hr];
    labels.push(hr);
    totalActivity.push(viewMode.value !== 2 ? d.total_count : d.Amount);
  }

  return {
    labels,
    datasets: [
      {
        label: "Total Activity",
        data: totalActivity,
        borderColor: "#ff5a10",
        borderWidth: 2,
        tension: 0.4,
      },
    ],
  };
});

onMounted(() => {
  const userSettings = JSON.parse(localStorage.getItem("ROLO_BO_EMPLOYEE"));
  symbol.value = userSettings?.currencyOption?.symbol || "";
});
</script>

<style scoped>
.chart-container {
  min-height: 348px;
}
</style>
