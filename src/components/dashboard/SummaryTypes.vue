<template>
  <v-tabs
    v-model="tab"
    align-tabs="center"
    slider-color="primary"
    selected-class="text-primary"
    density="comfortable"
    @update:model-value="onChangeTab"
  >
    <v-tab :value="dt.id" v-for="dt in summaryTypes" :key="dt.id">{{
      dt.name
    }}</v-tab>
  </v-tabs>
</template>
<script setup>
import { ref } from "vue";
const emit = defineEmits(["change"]);
const tab = ref(1);
const summaryTypes = [
  { id: 2, name: "YESTERDAY" },
  { id: 1, name: "TODAY" },
  { id: 3, name: "MTD" },
  { id: 4, name: "YTD" },
];

const onChangeTab = (v) => {
  emit("change", v);
};
</script>
