<template>
  <v-container class="summary-container" fluid>
    <v-row class="summary-row query" justify="end">
      <v-col
        cols="12"
        md="6"
        class="text-caption text-end summary-child text-grey"
      >
        <span v-if="summaryType > 2 && summaryType <= 4">
          From: <span class="high-light">{{ summary.fromDate }}</span> - To:
          <span class="high-light">{{ summary.toDate }}</span>
        </span>
        <span v-else>
          Date: <span class="high-light">{{ summary.fromDate }}</span>
        </span>
      </v-col>
    </v-row>
    <v-row class="summary-row">
      <v-col
        v-for="(c, idx) in countInfo"
        :key="idx"
        cols="12"
        sm="6"
        md="4"
        class="summary-child"
      >
        <v-card class="custom-card">
          <v-row no-gutters>
            <v-col
              cols="4"
              class="icon-box d-flex align-center justify-center"
              :class="`bg-${c.color}`"
            >
              <v-icon size="36" color="white">{{ c.icon }}</v-icon>
            </v-col>
            <v-col cols="8" class="text-black py-2 px-3">
              <div class="text-body-2 text-grey text-center text-capitalize">
                {{ c.label }}
              </div>
              <div class="pt-2 text-center text-h6 word-break">
                {{ c.count }}
              </div>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  summary: {
    type: Object,
    required: true,
  },
});

const summaryType = defineModel({ default: 0 });

const countInfo = computed(() => {
  let res = [
    {
      icon: "mdi-cart",
      label: "SALE COUNT",
      count: props.summary.saleCount || 0,
      color: "count",
    },
    {
      icon: "mdi-account-group",
      label: "GUEST COUNT",
      count: props.summary.guestCount || 0,
      color: "blue",
    },
    {
      icon: "mdi-wallet",
      label: "NET SALES",
      count: props.summary.netSales || "0.00",
      color: "teal",
    },
    {
      icon: "mdi-trending-up",
      label: "TOTAL",
      count: props.summary.total || "0.00",
      color: "primary",
    },
    {
      icon: "mdi-shopping",
      label: "APC",
      count: props.summary.apc || "0.00",
      color: "pink darken-3",
    },
  ];

  if (props.summary.apd) {
    res.push({
      icon: "mdi-shopping",
      label: "APD",
      count: props.summary.apd || "0.00",
      color: "orange darken-1",
    });
  }
  return res;
});
</script>

<style scoped>
.summary-container {
  display: block;
  padding-top: 0 !important;
}
.high-light {
  font-weight: 500;
  color: #1a1a1b;
}
.icon-box {
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
}
.summary-row {
  margin: auto -8px !important;
}
@media only screen and (min-width: 959px) {
  .summary-container {
    padding: 20px;
  }
  .summary-child {
    padding: 8px;
  }
}
@media only screen and (max-width: 959px) {
  .summary-container {
    padding: 20px;
  }
  .summary-child {
    padding: 8px;
    margin: 0;
  }
}
</style>
