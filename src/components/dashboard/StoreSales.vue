<template>
  <v-card class="custom-card chart-container" style="min-height: 348px">
    <v-card-title
      class="category-title py-2 d-flex justify-space-between align-center"
    >
      <span class="text-primary text-body-2 text-uppercase">LOCATIONS</span>

      <v-btn-toggle
        v-model="dataFormatToggle"
        mandatory
        class="elevation-0 mx-2"
        color="primary"
        density="compact"
        variant="outlined"
        divided
      >
        <v-btn flat value="1" color="primary">
          <v-icon>mdi-chart-box-outline</v-icon>
        </v-btn>
        <v-btn flat value="2" color="primary">
          <v-icon>mdi-view-list</v-icon>
        </v-btn>
      </v-btn-toggle>
    </v-card-title>
    <v-divider></v-divider>
    <v-card-text :class="{ 'pt-0': dataFormatToggle === '2' }" class="pa-0">
      <bar-chart
        v-if="dataFormatToggle === '1'"
        :chart-data="saleChartData"
        :chartOptions="chartOptions"
      />
      <store-sale-list v-if="dataFormatToggle === '2'" :data="data" />
    </v-card-text>
  </v-card>
</template>
<script setup>
import { ref, computed, onMounted } from "vue";
import BarChart from "./charts/BarChart.vue";
import StoreSaleList from "./StoreSaleList.vue";
import { formatCurrency } from "@/components/utils/money";
import { getBgColors } from "./charts/colors";
import { useIndexStore } from "@/stores";

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
});

const store = useIndexStore();

const symbol = ref("");
const dataFormatToggle = ref("1");

// const saleToolTip = computed(() => ({
//   mode: "single",
//   backgroundColor: "#2c2d2d",
//   titleFontFamily: "Lato-regular",
//   callbacks: {
//     label: (tooltipItem, data) => {
//       let val = data.datasets[0].data[tooltipItem.dataIndex];
//       val = parseFloat(val).toFixed(2);
//       return symbol.value + formatCurrency(val);
//     },
//   },
// }));

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: true,
  plugins: {
    tooltip: {
      position: "nearest",
      bodyFont: {
        family: "Raleway",
      },
    },
    legend: {
      display: false,
      position: "bottom",
      align: "center",
      fullSize: true,
      labels: {
        usePointStyle: false,
        generateLables: function (chart) {
          var data = chart.data;
          if (data.labels.length && data.datasets.length) {
            return data.labels.map(function (label, i) {
              var meta = chart.getDatasetMeta(0);
              var ds = data.datasets[0];
              var arc = meta.data[i];
              var custom = arc.custom || {};
              var getValueAtIndexOrDefault =
                Chart.helpers.getValueAtIndexOrDefault;
              var arcOpts = chart.options.elements.arc;
              var fill = custom.backgroundColor
                ? custom.backgroundColor
                : getValueAtIndexOrDefault(
                    ds.backgroundColor,
                    i,
                    arcOpts.backgroundColor
                  );
              var stroke = custom.borderColor
                ? custom.borderColor
                : getValueAtIndexOrDefault(
                    ds.borderColor,
                    i,
                    arcOpts.borderColor
                  );
              var bw = custom.borderWidth
                ? custom.borderWidth
                : getValueAtIndexOrDefault(
                    ds.borderWidth,
                    i,
                    arcOpts.borderWidth
                  );

              return {
                // here is where we are adding the data values to the legend title
                text:
                  label +
                  " (" +
                  formatCurrency(
                    ds.data[i].toLocaleString(),
                    store.getCurrencyOption
                  ) +
                  ")",
                fillStyle: fill,
                strokeStyle: stroke,
                lineWidth: bw,
                hidden: isNaN(ds.data[i]) || meta.data[i].hidden,
                index: i, // extra data used for toggling the correct item
              };
            });
          } else {
            return [];
          }
        },
      },
    },
  },
  scales: {
    y: {
      ticks: {
        callback: (value) => {
          let val = value.toString().split(".").join("");
          let m = formatCurrency(val, store.getCurrencyOption);
          return m.toString();
        },
        beginAtZero: true,
      },
    },
    x: {
      display: false,
    },
  },
}));

const stores = computed(() => {
  if (!props.data) return [];
  return props.data.sort((a, b) => a.store_id - b.store_id) || [];
});

const saleChartData = computed(() => {
  let labels = stores.value.map(
    (store) => store.store_name || `Location ${store.store_id}`
  );
  let dataSet = stores.value.map((store) => store.totalAmount);

  return {
    labels,
    datasets: [
      {
        backgroundColor: getBgColors(labels.length),
        data: dataSet,
        barPercentage: 0.5,
        barThickness: 60,
      },
    ],
  };
});

onMounted(() => {
  const userSettings = JSON.parse(localStorage.getItem("ROLO_BO_EMPLOYEE"));
  symbol.value = userSettings?.currencyOption?.symbol || "";
});
</script>
<style scoped>
.chart-container {
  min-height: 348px;
}
</style>
