<template>
  <v-container class="count-container pa-0">
    <v-row>
      <v-col v-for="(c, idx) in countInfo" :key="idx" cols="12" sm="6" md="12">
        <v-card class="custom-card">
          <v-row no-gutters>
            <v-col
              cols="4"
              class="pa-1 d-flex justify-center align-center"
              :class="`bg-${c.color}`"
            >
              <v-icon size="36px" dark :icon="c.icon" color="white"></v-icon>
            </v-col>
            <v-col cols="8" class="pa-1 d-flex flex-column">
              <div class="text-grey text-capitalize text-subtitle-2">
                {{ c.label }}
              </div>
              <div class="text-center text-h6 word-break">
                {{ c.count }}
              </div>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>
<script setup>
import { computed } from "vue";
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const countInfo = computed(() => {
  return [
    {
      icon: "mdi-cart-plus",
      label: "SALE COUNT",
      count: props.data.total_checks || 0,
      color: "count",
    },
    {
      icon: "mdi-account-multiple",
      label: "GUEST COUNT",
      count: props.data.total_guests || 0,
      color: "blue",
    },
    {
      icon: "mdi-wallet",
      label: "NET SALES",
      count: props.data.net || "0.00",
      color: "teal",
    },
    {
      icon: "mdi-shopping",
      label: "APC",
      count: props.data.apc || "0.00",
      color: "pink darken-3",
    },
    {
      icon: "mdi-tag-heart",
      label: "TIPS",
      count: props.data.tip || "0.00",
      color: "count",
    },
    {
      icon: "mdi-tag-heart",
      label: "NO CHARGE",
      count: props.data.nc || "0.00",
      color: "orange darken-1",
    },
  ];
});
</script>

<style scoped>
.count-container {
  display: block;
  padding-top: 0 !important;
}

.icon-box {
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
}

@media only screen and (min-width: 959px) {
  .count-container {
    padding: 20px;
  }
  .count-child {
    margin: 8px 0;
  }
  .count-child:first-child {
    margin-top: 0 !important;
  }
}

@media only screen and (max-width: 959px) {
  .count-container {
    padding: 0 0 !important;
  }

  .count-child {
    padding: 8px;
    margin: 0;
  }
}
</style>
