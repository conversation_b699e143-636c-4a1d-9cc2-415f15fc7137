import { formatCurrency } from "@/components/utils/money";

const legends = (chart, viewMode, co) => {
  const { labels, datasets } = chart.data;
  if (!labels.length || !datasets.length) return [];

  return labels.map((label, i) => {
    const meta = chart.getDatasetMeta(0);
    const ds = datasets[0];
    const arc = meta.data[i] || {};
    const custom = arc.custom || {};
    const arcOpts = chart.options.elements.arc;
    const getVal = (arr, index, defaultVal) => arr?.[index] ?? defaultVal;

    const fill =
      custom.backgroundColor ||
      getVal(ds.backgroundColor, i, arcOpts.backgroundColor);
    const stroke =
      custom.borderColor || getVal(ds.borderColor, i, arcOpts.borderColor);
    const bw =
      custom.borderWidth || getVal(ds.borderWidth, i, arcOpts.borderWidth);

    let value = getVal(ds.data, i, "").toLocaleString();
    if (viewMode === 2) {
      value = formatCurrency(value, co);
    }

    return {
      text: `${label} (${value})`,
      fillStyle: fill,
      strokeStyle: stroke,
      lineWidth: bw,
      hidden: isNaN(ds.data[i]) || meta.data[i]?.hidden,
      index: i,
    };
  });
};

const tooltips = (tooltipItem, data, viewMode, co) => {
  const labels = data.labels;
  const ds = data.datasets[0];
  const i = tooltipItem.index;
  const getVal = (arr, index, defaultVal) => arr?.[index] ?? defaultVal;

  let label = getVal(labels, i, "");
  let value = getVal(ds.data, i, "").toLocaleString();
  value = viewMode === 2 ? formatCurrency(value, co) : value;

  return ` ${label} (${value})`;
};

const chartOptions = (viewMode, title) => ({
  maintainAspectRatio: true,
  responsive: true,
  plugins: {
    title: {
      display: false,
      text: `BY ${title}`,
      position: "top",
    },
    legend: {
      display: true,
      position: "bottom",
      lineWidth: 4,
      labels: {
        usePointStyle: true,
        generateLabels: (chart) => legends(chart, viewMode),
      },
    },
    tooltips: {
      callbacks: {
        label: (tooltipItem, data) => tooltips(tooltipItem, data, viewMode),
      },
    },
  },
});

export { chartOptions, legends, tooltips };
