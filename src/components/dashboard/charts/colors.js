let background_colors = [
  "rgba(255,138,128, 0.75)",
  "rgba(12,179, 0,0.75)",
  "rgba(54, 162, 235, 0.75)",
  "rgba(55,159,122,0.75)",
  "rgba(152,115,151,0.75)",
  "rgba(244, 81, 30, 0.75)",
  "rgba(102,187,106, 0.75)",
  "rgba(255, 159, 64, 0.75)",
  "rgba(75, 192, 192, 0.75)",
  "rgba(153, 102, 255, 0.75)",
  "rgba(255, 99, 132, 0.75)",
  "#9C2720",
  "#3f51b5",
  "#fb8c00",
  "#4caf50",
];

export function getBgColors(tl) {
  let finalColors = [];

  let availableLen = background_colors.length;

  if (tl <= availableLen) {
    finalColors = background_colors.slice(0, tl + 1);
    return finalColors;
  } else {
    finalColors = background_colors;
    for (let index = availableLen; index < tl + 1; index++) {
      finalColors.push(
        "#" + (Math.random().toString(16) + "0000000").slice(2, 8)
      );
    }
  }

  return finalColors;
}
