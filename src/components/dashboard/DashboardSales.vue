<template>
  <div class="results-container">
    <v-container fluid class="result-child">
      <v-row>
        <v-col cols="12">
          <sale-summary :data="saleSummary"></sale-summary>
        </v-col>
      </v-row>
    </v-container>
    <v-container fluid class="result-child">
      <v-row>
        <v-col cols="12" md="3" xl="2" class="text-center">
          <sale-counts :data="countInfo"></sale-counts>
        </v-col>
        <v-col cols="12" md="9" xl="10">
          <hourly :data="props.dashboardData"></hourly>
        </v-col>
      </v-row>
    </v-container>
    <v-container fluid class="result-child" v-if="storeData.show">
      <v-row>
        <v-col cols="12">
          <store-sales :data="storeData.res"></store-sales>
        </v-col>
      </v-row>
    </v-container>
    <widgets :widgets="widgets"></widgets>
  </div>
</template>

<script setup>
import { computed, onMounted } from "vue";
import SaleSummary from "./SaleSummary.vue";
import SaleCounts from "./SaleCounts.vue";
import Hourly from "./widgets/Hourly.vue";
import StoreSales from "./StoreSales.vue";
import Widgets from "./widgets/Widgets.vue";
const props = defineProps({
  dashboardData: {
    type: Object,
    required: true,
  },
  storeData: {
    type: Object,
    required: true,
  },
});
const byQty = "SALES COUNT";
const byValue = "TOTAL";

// const results = computed(() => props.dashboardData);
const saleSummary = computed(() => {
  if (!props.dashboardData) return {};
  const {
    baseAmountFmt,
    discountAmountFmt,
    chargeAmountFmt,
    taxAmountFmt,
    totalAmountFmt,
  } = props.dashboardData;
  return {
    baseAmount: baseAmountFmt,
    discount: discountAmountFmt,
    charge: chargeAmountFmt,
    tax: taxAmountFmt,
    refund: 0,
    total: totalAmountFmt,
  };
});
const widgets = computed(() => {
  let results = [];
  const widgets = [
    {
      id: "saleType",
      title: "SALE TYPE",
      byQty: byQty,
      byValue: byValue,
    },
    {
      id: "serviceType",
      title: "SERVICE TYPE",
      byQty: byQty,
      byValue: byValue,
    },
    {
      id: "payments",
      title: "PAYMENT NAME",
      byQty: byQty,
      byValue: byValue,
    },
    {
      id: "paymentType",
      title: "PAYMENT TYPE",
      byQty: byQty,
      byValue: byValue,
    },
    {
      id: "posVsDinner",
      title: "REVENUE MODE",
      topN: 10,
      byQty: byQty,
      byValue: byValue,
    },
  ];
  widgets.forEach((data) => {
    let id = data.id;
    let result = props.dashboardData && props.dashboardData[id];
    if (result) {
      results.push({
        ...data,
        result,
      });
    }
  });
  return results;
});
const countInfo = computed(() => {
  if (!props.dashboardData) return {};
  const { total_checks, total_guests, apcFmt, ncFmt, netAmountFmt, tipFmt } =
    props.dashboardData;
  return {
    total_checks: total_checks || 0,
    total_guests: total_guests || 0,
    apc: apcFmt,
    nc: ncFmt,
    net: netAmountFmt,
    tip: tipFmt,
  };
});
</script>

<style scoped>
.results-container {
  padding: 20px;
  padding-top: 0px;
  margin: 0;
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-columns: 100%;
}

.result-child {
  /* grid-column: 1 / 3; */
  padding-top: 20px !important;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}

/* @media only screen and (max-width: 959px) {
  .count-cont-parent {
    margin-bottom: 20px;
  }
} */
</style>
