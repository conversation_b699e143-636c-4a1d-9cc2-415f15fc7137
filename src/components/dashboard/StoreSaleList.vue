<template>
  <div>
    <v-data-table
      :items="results"
      item-value="store_id"
      :loading="loader"
      :headers="headers"
      hide-default-footer
    >
      <template v-slot:item="{ item }">
        <tr>
          <td class="text-left">{{ item.store_name }}</td>
          <td class="text-end">{{ item.total_checks }}</td>
          <td class="text-end">
            <span>{{ item.total_guests }}</span>
          </td>
          <td class="text-end">
            <span>{{ item.baseAmountFmt }}</span>
          </td>
          <td class="text-end">{{ item.discountAmountFmt }}</td>
          <td class="text-end">{{ item.chargeAmountFmt }}</td>
          <td class="text-end">
            <span>{{ item.taxAmountFmt }}</span>
          </td>
          <td class="text-end">
            <span>{{ item.totalAmountFmt }}</span>
          </td>
          <td class="text-end">{{ item.apcFmt }}</td>
          <td class="text-end">{{ item.ncFmt }}</td>
        </tr>
      </template>
      <template v-slot:bottom v-if="showTotal && results.length">
        <tr>
          <td><strong>Total</strong></td>
          <td class="text-end">{{ total.total_checks }}</td>
          <td class="text-end">
            <span>{{ total.total_guests }}</span>
          </td>
          <td class="text-end">
            <span>{{ total.baseAmountFmt }}</span>
          </td>
          <td class="text-end">{{ total.discountAmountFmt }}</td>
          <td class="text-end">{{ total.chargeAmountFmt }}</td>
          <td class="text-end">
            <span>{{ total.taxAmountFmt }}</span>
          </td>
          <td class="text-end">
            <span>{{ total.totalAmountFmt }}</span>
          </td>
          <td class="text-end">{{ total.apcFmt }}</td>
          <td class="text-end">{{ total.ncFmt }}</td>
        </tr>
      </template>
    </v-data-table>
  </div>
</template>
<script setup>
import { ref, computed } from "vue";

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  total: {
    type: Object,
  },
  showTotal: {
    type: Boolean,
    default: false,
  },
});

const loader = ref(false);

const headers = [
  {
    title: "Name",
    align: "start",
    key: "store_name",
    sortable: true,
  },
  {
    title: "SALE COUNT",
    align: "end",
    key: "total_checks",
    sortable: false,
  },
  { title: "GUEST COUNT", align: "end", key: "total_guests", sortable: false },
  { title: "BASE PRICE", align: "end", key: "baseAmountFmt", sortable: false },
  {
    title: "DISCOUNT",
    align: "end",
    key: "discountAmountFmt",
    sortable: false,
  },
  { title: "CHARGE", align: "end", key: "chargeAmountFmt", sortable: false },
  { title: "TAX", align: "end", key: "taxAmountFmt", sortable: false },
  { title: "TOTAL", align: "end", key: "totalAmountFmt", sortable: false },
  { title: "APC", align: "end", key: "apcFmt", sortable: false },
  { title: "NO CHARGE", align: "end", key: "ncFmt", sortable: false },
];

const results = computed(() => props.data || []);
</script>
