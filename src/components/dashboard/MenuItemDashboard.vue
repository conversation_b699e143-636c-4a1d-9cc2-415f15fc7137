<template>
  <div class="results-container">
    <widgets :widgets="widgets" />
  </div>
</template>
<script setup>
import { computed, onMounted } from "vue";
import Widgets from "./widgets/Widgets.vue";
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const byQty = "QUANTITY";
const byValue = "NET SALES";

const widgets = computed(() => {
  let results = [];
  const response = props.data.itemSummary;
  if (!response) {
    return results;
  }
  const widgets = [
    {
      id: "departments",
      title: "DEPARTMENTS",
      topN: 10,
      byQty: byQty,
      byValue: byValue,
    },
    {
      id: "posVsDinner",
      title: "REVENUE MODE",
      topN: 10,
      byQty: byQty,
      byValue: byValue,
      viewMode: 3,
    },
    {
      id: "categories",
      title: "CATEGORIES",
      topN: 10,
      byQty: byQty,
      byValue: byValue,
      viewMode: 3,
    },
    {
      id: "menuItems",
      title: "MENU ITEMS",
      topN: 10,
      byQty: byQty,
      byValue: byValue,
      viewMode: 3,
    },
    {
      id: "saleType",
      title: "SALE TYPE",
      topN: 10,
      byQty: byQty,
      byValue: byValue,
      viewMode: 3,
    },
    {
      id: "serviceType",
      title: "SERVICE TYPE",
      topN: 10,
      byQty: byQty,
      byValue: byValue,
      viewMode: 3,
    },
  ];
  widgets.forEach((data) => {
    let id = data.id;
    let result = response[id];
    if (result) {
      results.push({
        ...data,
        result,
      });
    }
  });
  return results;
});
</script>
<style scoped>
.results-container {
  padding: 20px;
  padding-top: 0px;
  margin: 0;
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-columns: 100%;
}
</style>
