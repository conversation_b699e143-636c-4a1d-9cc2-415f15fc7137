<template>
  <v-card class="custom-card pa-2">
    <v-row>
      <v-col v-for="(item, index) in items" :key="index">
        <v-row no-gutters>
          <v-col v-if="item.before" class="d-flex align-center justify-center">
            <v-icon class="icon--color" :icon="item.before"></v-icon>
          </v-col>
          <v-col class="pa-3">
            <div class="text-h6 word-break content-center pb-2">
              {{ item.amount }}
            </div>
            <div
              class="pb-2 text-subtitle-2 content-center text-grey text-capitalize"
            >
              {{ item.label }}
            </div>
            <div class="content-center">
              <v-icon
                :color="item.color"
                size="36px"
                :icon="item.icon"
              ></v-icon>
            </div>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-card>
</template>

<script setup>
import { computed } from "vue";
// import { formatCurrency } from "../utils/money";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const formattedValue = (key) => {
  return props.data[key] ? props.data[key] : "0.00";
};

const items = computed(() => [
  {
    label: "BASE PRICE",
    icon: "mdi-wallet",
    before: "",
    amount: formattedValue("baseAmount"),
    color: "teal",
  },
  {
    label: "DISCOUNT",
    icon: "mdi-tag-heart",
    before: "mdi-minus",
    amount: formattedValue("discount"),
    color: "purple",
  },
  {
    label: "CHARGE",
    icon: "mdi-cash-100",
    before: "mdi-plus",
    amount: formattedValue("charge"),
    color: "lime darken-1",
  },
  {
    label: "TAX",
    icon: "mdi-credit-card-outline",
    before: "mdi-plus",
    amount: formattedValue("tax"),
    color: "pink",
  },
  {
    label: "TOTAL",
    icon: "mdi-trending-up",
    before: "mdi-equal",
    amount: formattedValue("total"),
    color: "success",
  },
]);

// const netAmount = computed(() => {
//   let netAmount = props.data.baseAmount - props.data.discount;
//   return {
//     amount: netAmount,
//     fmt: formatCurrency(netAmount.toString(), currencyOptions),
//   };
// });
</script>
<style scoped>
.action-elem {
  display: flex !important;
  justify-content: center !important;
  flex-direction: column;
  position: relative;
}

.push-down {
  position: absolute;
  bottom: 10px;
}

.icon--color {
  color: #0d7e83 !important;
}

.content-center {
  display: flex;
  justify-content: center;
}

.content-center.vertical {
  align-items: center;
}

.sale-title {
  font-size: 18px;
  font-weight: 500;
  opacity: 1;
}

.sale-title.sub {
  font-size: 16px;
}
</style>
