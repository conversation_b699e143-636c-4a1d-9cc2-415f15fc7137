<template>
  <div>
    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="450"
      class="filter-elem"
      temporary
    >
      <v-tabs
        v-model="tab"
        align-tabs="start"
        fixed-tabs
        color="white"
        hide-slider
        selected-class="selected-chip"
      >
        <v-tab :value="1">Filters</v-tab>
        <v-tab :value="2">Columns</v-tab>
      </v-tabs>
      <v-divider></v-divider>
      <v-tabs-window v-model="tab">
        <v-tabs-window-item :value="1">
          <v-container fluid>
            <v-row>
              <v-col cols="12" v-for="filter in filters">
                <h4 class="mb-2">
                  <span>{{ filter.name }}</span>
                </h4>
                <v-autocomplete
                  v-model="filter.value"
                  :items="filter.items"
                  :label="filter.name"
                  item-title="name"
                  item-value="value"
                  density="compact"
                  variant="outlined"
                  single-line
                  hide-details
                  color="primary"
                  return-object
                  clearable
                ></v-autocomplete>
              </v-col>
            </v-row>
          </v-container>
        </v-tabs-window-item>
        <v-tabs-window-item :value="2">
          <v-container class="d-flex flex-column h-100">
            <Draggable
              class="dragArea list-group w-full"
              :list="tableHeaders"
              handle=".drag-handle"
            >
              <v-checkbox
                v-for="header in tableHeaders"
                v-model="header.default"
                color="primary"
                :label="header.title"
                hide-details
                :disabled="header.mandatory"
                density="comfortable"
              >
                <template v-slot:append>
                  <v-icon class="drag-handle" icon="mdi-drag"></v-icon>
                </template>
              </v-checkbox>
            </Draggable>
          </v-container>
        </v-tabs-window-item>
      </v-tabs-window>
      <template v-slot:append>
        <v-divider></v-divider>
        <div class="pa-2">
          <v-btn block color="primary" @click="applyFilter">
            Apply Filter</v-btn
          >
        </div>
      </template>
    </v-navigation-drawer>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { VueDraggableNext as Draggable } from "vue-draggable-next";
const openNav = defineModel();
const tab = ref(1);

const props = defineProps({
  headers: {
    type: Array,
    default: () => [],
  },
  filters: {
    type: Object,
    default: () => {},
  },
});

const tableHeaders = ref(JSON.parse(JSON.stringify(props.headers)));

const emit = defineEmits(["applyFilter"]);

const applyFilter = () => {
  emit("applyFilter", {
    newHeaders: tableHeaders.value,
    filters: props.filters,
  });
};
</script>
<style>
.drag-handle {
  cursor: grab;
}
</style>
