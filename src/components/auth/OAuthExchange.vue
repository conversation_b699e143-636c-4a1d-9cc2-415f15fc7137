<template>
  <v-app>
    <v-container fluid class="fill-height">
      <v-row v-if="isLoading">
        <v-col class="text-center">
          <v-progress-circular color="primary" indeterminate size="70" />
          <p class="text-caption pt-3">Signing you in...</p>
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col class="text-center">
          <v-dialog v-model="dialog" max-width="400" persistent>
            <v-card class="pa-4 text-center align-center">
              <v-icon color="error" size="60">mdi-close-circle-outline</v-icon>
              <p class="mt-4" v-html="errorMessage"></p>
              <v-btn
                variant="flat"
                class="mt-4"
                color="primary"
                @click="goToLogin"
                >Go to login</v-btn
              >
            </v-card>
          </v-dialog>
        </v-col>
      </v-row>
    </v-container>
  </v-app>
</template>

<script setup>
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { setAuthorization, setExpiryTime, setPrivileges, startExpiryWatcher } from "@/helpers/auth";
import axiosInstance from "@/plugin/Axios";

const axios = axiosInstance();

const route = useRoute();
const router = useRouter();
const oAuthCode = route.query.oAuthCode;

const isLoading = ref(true);
const dialog = ref(false);
const errorMessage = ref("");

const exchangeToken = async () => {
  if (!oAuthCode) {
    console.error("No OAuth token in URL!");
    router.replace("/login");
    return;
  }

  try {
    // Call backend to silent login
    const { data } = await axios.post(
      `/silent-login`,
      {
        oAuthCode: oAuthCode
      },
      { skipTenant: true }
    );

    const expiresIn = data.expiresIn;

    const expiryTime = Date.now() + expiresIn * 1000; // convert to ms
    setExpiryTime(expiryTime);
    // startExpiryWatcher(router);

    setAuthorization({
      token: data.token,
      tenantId: data.tenants[0].id,
      tenants: data.tenants
    });

    if (data.tenants && data.tenants.length > 1) {
      router.replace("/select-tenant");
    } else {
      const userPrivileges = data.tenants[0]?.privileges || [];
      setPrivileges(userPrivileges);
      if (userPrivileges.includes("DASH_PUR")) {
        router.replace("/");
      } else {
        router.replace("/welcome");
      }
    }

  } catch (err) {
    console.error("Token exchange failed", err);
    errorMessage.value =
      "Something went wrong with authentication. Please login in again.";
    isLoading.value = false;
    dialog.value = true;
    // router.replace("/login"); // fallback
  }
};

const goToLogin = () => {
  router.push("/login");
};

exchangeToken();
</script>
