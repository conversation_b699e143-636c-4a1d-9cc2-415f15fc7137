<template>
  <v-app>
    <v-container fluid class="fill-height">
      <v-row>
        <v-col class="text-center">
          <v-progress-circular color="primary" indeterminate size="70" />
          <p class="text-caption pt-3">Authenticating...</p>
        </v-col>
      </v-row>
    </v-container>
  </v-app>
</template>

<script setup>
import { getAuthorizationToken } from "@/helpers/auth";
import { useRouter } from "vue-router";
import globalData from "@/composables/global";

const router = useRouter();

const initSession = async () => {
  const token = getAuthorizationToken();
  if (!token) {
    console.log("No token found, redirecting...");
    window.location.href = globalData.$authUrl + `/logout?client_id=${globalData.$clientId}`;
  } else {
    console.log("Token found, proceed...");
    router.push("/");
  }
};

initSession();
</script>
