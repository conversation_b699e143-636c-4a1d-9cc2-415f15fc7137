<template>
  <div>
    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="320"
      class="filter-elem"
      persistent
    >
      <!-- Header: <PERSON>bs + Close Button -->
      <template v-slot:prepend>
        <v-toolbar
          density="compact"
          color="white-smoke"
          title="Add Item"
          border
        >
          <v-spacer></v-spacer>
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="openNav = false"
          />
        </v-toolbar>
      </template>

      <v-container fluid>
        <v-card flat tile>
          <v-form ref="form" @submit.prevent="handleSubmit">
            <v-card-text class="px-0">
              <v-row>
                <v-col cols="12">
                  <v-autocomplete
                    ref="itemNameField"
                    v-model="formData.itemName"
                    :items="inventoryList"
                    item-title="itemName"
                    item-value="id"
                    label="Item Name"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                    @update:model-value="handleInventoryItem"
                  />
                </v-col>
                <v-col cols="12" v-if="packageList.length">
                  <v-autocomplete
                    v-model="formData.pkg"
                    :items="packageList"
                    item-title="name"
                    item-value="id"
                    label="Package"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                  />
                </v-col>
                <v-col cols="12" v-if="!hasVendor">
                  <v-autocomplete
                    v-model="formData.vendor"
                    :items="vendorList"
                    item-title="name"
                    item-value="id"
                    label="Vendor"
                    :rules="[rules.require]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    clearable
                    hide-details="auto"
                    return-object
                  />
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="formData.quantity"
                    label="Quantity"
                    type="number"
                    min="1"
                    :rules="[rules.min1]"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details="auto"
                    @keydown.up.prevent
                    @keydown.down.prevent
                  >
                    <template v-slot:append-inner>{{
                      formData.purchaseUOM
                    }}</template>
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <currency-input
                    label="Unit Cost"
                    v-model="formData.unitCost"
                    @valueChange="(v) => handleCost(v)"
                  />
                </v-col>
                <v-col cols="12">
                  <currency-input
                    label="Discount"
                    v-model="formData.discount"
                    @valueChange="(v) => handleDiscount(v)"
                  />
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model.number="formData.taxRate"
                    label="Tax Rate"
                    type="number"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details
                    suffix="%"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model.number="formData.remarks"
                    label="Remarks"
                    variant="outlined"
                    density="compact"
                    color="primary"
                    hide-details
                    rows="2"
                    @keydown.enter="handleSubmit"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions class="px-0 py-4">
              <v-btn type="submit" color="primary" variant="flat" block
                >Add</v-btn
              >
            </v-card-actions>
          </v-form>
        </v-card>
      </v-container>
    </v-navigation-drawer>
  </div>
</template>

<script setup>
import { nextTick, ref } from "vue";
import rules from "@/helpers/rules";
import { purchaseRequestItemRecord as DEFAULT_PURCHASE_ITEM } from "@/helpers/defaultRecords";
import CurrencyInput from "@/components/CurrencyInput.vue";

const emit = defineEmits(["add-pr"]);

defineProps({
  inventoryList: {
    type: Array,
    default: () => [],
  },
  vendorList: {
    type: Array,
    default: () => [],
  },
  hasVendor: {
    type: Boolean,
    default: false,
  },
});

const form = ref();
const openNav = ref(true);
const toggle = () => {
  openNav.value = !openNav.value;
};

// used to expose function to be called by external actions
defineExpose({
  toggle,
});

const formData = ref(JSON.parse(JSON.stringify(DEFAULT_PURCHASE_ITEM)));
const packageList = ref([]);

const handleInventoryItem = (v) => {
  if (!v) return;
  formData.value.itemName = v.itemName;
  formData.value.itemCode = v.itemCode;
  formData.value.itemId = v.id;
  formData.value.categoryId = v.category?.id;
  formData.value.subcategoryId = v.subCategory?.id;
  formData.value.purchaseUOM = v.purchaseUnit.symbol;
  formData.value.unitCost = v.unitCost;

  if (v.packages.length) {
    packageList.value = [{ name: "default", id: "default" }, ...v.packages];
    formData.value.pkg = { name: "default", id: "default" };
  }
};

const handleCost = (v) => {
  formData.value.unitCost = v;
  //   calculateTotalPrice(formData);
};

const handleDiscount = (v) => {
  formData.value.discount = v;
  //   calculateTotalPrice(formData);
};

const itemNameField = ref(null);
const handleSubmit = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const payload = { ...formData.value };

  emit("add-pr", payload);
  packageList.value = [];

  // ✅ Reset form data
  formData.value = JSON.parse(JSON.stringify(DEFAULT_PURCHASE_ITEM));
  form.value.resetValidation();
  // ✅ Focus back to the first field
  await nextTick();
  itemNameField.value?.focus();
};
</script>
