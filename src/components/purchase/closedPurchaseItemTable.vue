<template>
  <v-card class="parent-cont mb-0" width="100%">
    <v-container fluid grid-list-lg class="pa-0">
      <v-data-table
        :headers="headers"
        :items="items"
        class="custom-table"
        items-per-page="-1"
      >
        <template v-slot:item="{ item }">
          <tr class="result-elem default">
            <td
              v-for="(header, i) in headers"
              :key="header.key + i"
              class="py-4"
            >
              <span
                v-if="header.key == 'quantity'"
                class="d-flex"
                :class="`justify-${header.align}`"
              >
                {{ item.quantity }} {{ item.purchaseUOM }}</span
              >

              <span
                v-else-if="header.key == 'receivedQuantity'"
                class="d-flex"
                :class="`justify-${header.align}`"
              >
                {{ item.receivedQty }} {{ item.purchaseUOM }}</span
              >

              <span
                v-else-if="header.key == 'vendor'"
                class="d-flex"
                :class="`justify-${header.align}`"
              >
                {{ item.vendor.name }}</span
              >

              <span
                v-else-if="header.key == 'taxRate'"
                class="d-flex"
                :class="`justify-${header.align}`"
              >
                {{ item.taxRate }}%</span
              >
              <span v-else class="d-flex" :class="`justify-${header.align}`">{{
                item[header.key]
              }}</span>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-container>
  </v-card>
</template>

<script setup>
const props = defineProps({
  headers: {
    type: Array,
    required: true,
  },
  items: {
    type: Array,
    required: true,
  },
});
</script>
