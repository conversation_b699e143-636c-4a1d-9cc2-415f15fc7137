<template>
  <v-card class="parent-cont mb-0" width="100%">
    <v-container fluid grid-list-lg class="pa-0">
      <v-data-table
        :headers="headers"
        :items="items"
        class="custom-table"
        density="compact"
        items-per-page="-1"
      >
        <template v-slot:item="{ item, index }">
          <tr class="result-elem default">
            <td
              v-for="(header, i) in headers"
              :key="header.key + i"
              class="py-4 text-center"
            >
              <span v-if="header.key == 'itemName'">
                {{ item.itemName || "-" }}
              </span>

              <span v-else-if="header.key == 'itemCode'">
                {{ item.itemCode || "-" }}
              </span>

              <span v-else-if="header.key == 'quantity'">
                {{ item.quantity || "-" }}{{ item.purchaseUOM }}
              </span>

              <v-text-field
                v-else-if="header.key == 'receivedQty'"
                v-model.number="item.receivedQty"
                type="number"
                density="compact"
                variant="outlined"
                hide-details
                color="primary"
                :rules="[rules.require]"
                @update:model-value="calculateTotalPrice(item)"
              >
                <template v-slot:append-inner>
                  {{ item.purchaseUOM }}
                </template>
              </v-text-field>

              <currency-input
                v-else-if="header.key == 'unitCost'"
                v-model="item.unitCost"
                @valueChange="(v) => handleCurrencyInput(v, item)"
              ></currency-input>

              <v-text-field
                v-else-if="header.key == 'taxRate'"
                v-model.number="item.taxRate"
                type="number"
                density="compact"
                variant="outlined"
                hide-details
                color="primary"
                :rules="[rules.price]"
                @update:model-value="calculateTotalPrice(item)"
              >
                <template v-slot:append-inner> % </template>
              </v-text-field>

              <span v-else-if="header.key == 'totalPrice'" class="text-center">
                {{ item.totalPrice }}
              </span>

              <div v-else class="d-flex justify-center align-center">
                <v-icon
                  color="error"
                  @click="removeRow(index)"
                  :disabled="viewOnly"
                >
                  mdi-close
                </v-icon>
              </div>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-container>
  </v-card>
</template>

<script setup>
import rules from "@/helpers/rules";
import { ref, watch } from "vue";
import { purchaseOrderItemRecord as DEFAULT_PURCHASE_ITEM } from "@/helpers/defaultRecords";
import CurrencyInput from "@/components/CurrencyInput.vue";

const props = defineProps({
  headers: {
    type: Array,
    required: true,
  },
  inventoryList: {
    type: Array,
    default: () => [],
  },
  vendorList: {
    type: Array,
    default: () => [],
  },
  selectedVendor: {
    type: Object,
    default: () => null,
  },
  viewOnly: {
    type: Boolean,
    default: () => false,
  },
});

const items = defineModel();
items.value = items.value.map((item) => ({
  ...item,
  quantity: item.quantity - (item.receivedQty || 0),
  receivedQty: item.quantity - (item.receivedQty || 0),
}));
const inputRef = ref(null);

const newRow = ref({ ...DEFAULT_PURCHASE_ITEM });

const calculateTotalPrice = (row) => {
  if (!row.unitCost || !row.receivedQty || !row.taxRate) row.totalPrice = 0;
  const subTotal = Number(row.unitCost) * Number(row.receivedQty);
  const taxAmount = (subTotal * Number(row.taxRate)) / 100;
  row.totalPrice = (subTotal + taxAmount).toFixed(2);
};

const removeRow = (index) => {
  items.value.splice(index, 1);
};

const handleCurrencyInput = (v, row) => {
  row.unitCost = v;
  calculateTotalPrice(row);
};

watch(
  () => props.selectedVendor,
  (val) => {
    if (val) newRow.value.vendor = val;
  },
  { immediate: true }
);
</script>

<style scoped>
.custom-table >>> .v-data-table-rows-no-data {
  display: none;
}
</style>
