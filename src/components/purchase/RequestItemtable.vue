<template>
  <v-card class="parent-cont mb-0" width="100%">
    <v-container fluid grid-list-lg class="pa-0">
      <v-data-table
        :headers="headers"
        :items="items"
        class="custom-table"
        density="compact"
        items-per-page="-1"
      >
        <template v-slot:item="{ item, index }">
          <tr class="result-elem default">
            <td
              v-for="(header, colIndex) in headers"
              :key="header.key + colIndex"
              class="py-4 text-center"
            >
              <!-- Item Name -->
              <v-autocomplete
                v-if="header.key == 'itemName'"
                v-model="item.itemName"
                :items="inventoryList"
                item-title="itemName"
                item-value="id"
                density="compact"
                variant="outlined"
                single-line
                hide-details
                color="primary"
                :rules="[rules.require]"
                return-object
                readonly
                clearable
                :tabindex="getTabIndex(index, colIndex, headers.length)"
                :ref="index === 0 && colIndex === 0 ? 'firstItemRef' : null"
              />

              <!-- Item Code -->
              <span v-else-if="header.key == 'itemCode'" class="text-center">
                {{ item.itemCode || "-" }}
              </span>

              <!-- Vendor (Selected Vendor) -->
              <span
                v-else-if="
                  header.key == 'vendor' &&
                  props.selectedVendor &&
                  props.selectedVendor.id == item.vendor.id
                "
                class="text-center"
              >
                {{ item.vendor.name || "-" }}
              </span>

              <!-- Vendor (Dropdown) -->
              <v-autocomplete
                v-else-if="
                  header.key == 'vendor' &&
                  (!props.selectedVendor ||
                    props.selectedVendor.id != item.vendor.id)
                "
                v-model="item.vendor"
                :items="vendorList"
                item-title="name"
                item-value="id"
                density="compact"
                variant="outlined"
                single-line
                hide-details
                color="primary"
                :rules="[rules.require]"
                return-object
                :readonly="viewOnly"
                clearable
                :tabindex="getTabIndex(index, colIndex, headers.length)"
              />

              <!-- Quantity -->
              <v-text-field
                v-else-if="header.key == 'quantity'"
                v-model="item.quantity"
                type="number"
                density="compact"
                variant="outlined"
                hide-details
                color="primary"
                @update:model-value="calculateTotalPrice(item)"
                :readonly="viewOnly"
                :tabindex="getTabIndex(index, colIndex, headers.length)"
              >
                <template v-slot:append-inner>
                  {{ item.purchaseUOM }}
                </template>
              </v-text-field>

              <!-- Unit Cost -->
              <currency-input
                v-else-if="header.key == 'unitCost'"
                v-model="item.unitCost"
                @valueChange="(v) => handleCurrencyInput(v, item)"
                :tabindex="getTabIndex(index, colIndex, headers.length)"
              />

              <!-- Tax Rate -->
              <v-text-field
                v-else-if="header.key == 'taxRate'"
                v-model="item.taxRate"
                type="number"
                density="compact"
                variant="outlined"
                hide-details
                color="primary"
                :rules="[rules.price]"
                @update:model-value="calculateTotalPrice(item)"
                :readonly="viewOnly"
                :tabindex="getTabIndex(index, colIndex, headers.length)"
              >
                <template v-slot:append-inner> % </template>
              </v-text-field>

              <!-- Total Price -->
              <span v-else-if="header.key == 'totalPrice'" class="text-center">
                {{ item.totalPrice }}
              </span>

              <!-- Delete Row Icon -->
              <div v-else class="d-flex justify-center align-center">
                <v-icon
                  color="error"
                  @click="removeRow(index)"
                  :disabled="viewOnly"
                  :tabindex="getTabIndex(index, colIndex, headers.length)"
                  @keydown.tab.prevent="
                    handleTabNavigation($event, {
                      isLastRow: index === items.length - 1,
                      rowIndex: index,
                      totalCols: headers.length,
                    })
                  "
                >
                  mdi-close
                </v-icon>
              </div>
            </td>
          </tr>
        </template>

        <template v-slot:body.append>
          <tr
            class="result-elem default"
            v-if="!viewOnly"
            @keypress.enter="commitNewRow"
          >
            <td
              v-for="(header, colIndex) in headers"
              :key="header.key + colIndex"
              class="py-4 text-center"
            >
              <!-- Item Name -->
              <v-autocomplete
                v-if="header.key == 'itemName'"
                v-model="newRow.itemName"
                :items="inventoryList"
                item-title="itemName"
                item-value="id"
                density="compact"
                variant="outlined"
                single-line
                hide-details
                color="primary"
                return-object
                @update:model-value="(val) => setValues(val, null)"
                :ref="colIndex === 0 ? 'inputRef' : null"
                clearable
                :tabindex="getTabIndex(items.length, colIndex, headers.length)"
              />

              <!-- Item Code -->
              <span v-else-if="header.key == 'itemCode'" class="text-center">
                {{ newRow.itemCode || "-" }}
              </span>

              <!-- Vendor (Selected) -->
              <span
                v-else-if="header.key == 'vendor' && props.selectedVendor"
                class="text-center"
              >
                {{ newRow.vendor.name || "-" }}
              </span>

              <!-- Vendor (Dropdown) -->
              <v-autocomplete
                v-else-if="header.key == 'vendor' && !props.selectedVendor"
                v-model="newRow.vendor"
                :items="vendorList"
                item-title="name"
                item-value="id"
                density="compact"
                variant="outlined"
                single-line
                hide-details
                color="primary"
                return-object
                clearable
                :tabindex="getTabIndex(items.length, colIndex, headers.length)"
              />

              <!-- Quantity -->
              <v-text-field
                v-else-if="header.key == 'quantity'"
                v-model.number="newRow.quantity"
                type="number"
                density="compact"
                variant="outlined"
                hide-details
                color="primary"
                @update:model-value="
                  ((val) => setValues(null, val), calculateTotalPrice(newRow))
                "
                :tabindex="getTabIndex(items.length, colIndex, headers.length)"
              >
                <template v-slot:append-inner>
                  {{ newRow.purchaseUOM }}
                </template>
              </v-text-field>

              <!-- Unit Cost -->
              <currency-input
                v-else-if="header.key == 'unitCost'"
                v-model="newRow.unitCost"
                @valueChange="(v) => handleCurrencyInput(v, newRow)"
                :tabindex="getTabIndex(items.length, colIndex, headers.length)"
              />

              <!-- Tax Rate -->
              <v-text-field
                v-else-if="header.key == 'taxRate'"
                v-model.number="newRow.taxRate"
                type="number"
                density="compact"
                variant="outlined"
                hide-details
                color="primary"
                @update:model-value="calculateTotalPrice(newRow)"
                :tabindex="getTabIndex(items.length, colIndex, headers.length)"
              >
                <template v-slot:append-inner> % </template>
              </v-text-field>

              <!-- Total Price -->
              <span v-else-if="header.key == 'totalPrice'" class="text-center">
                {{ newRow.totalPrice }}
              </span>

              <!-- Add Row Icon -->
              <div v-else class="d-flex justify-center align-center">
                <v-icon
                  color="green"
                  :disabled="!isRowValid(newRow)"
                  @click="commitNewRow"
                  @keydown.tab.prevent="handleTabFromPlus"
                  :tabindex="
                    getTabIndex(items.length, colIndex, headers.length)
                  "
                >
                  mdi-plus
                </v-icon>
              </div>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-container>
  </v-card>
</template>

<script setup>
import rules from "@/helpers/rules";
import { ref, nextTick, watch } from "vue";
import { purchaseRequestItemRecord as DEFAULT_PURCHASE_ITEM } from "@/helpers/defaultRecords";
import { purchaseRequestItemHeaders } from "@/helpers/tableHeaders";
import CurrencyInput from "@/components/CurrencyInput.vue";
import { useTabFlow } from "@/helpers/tabFlow";

const { getTabIndex, focusNextRow, focusFirstElement, handleTabNavigation } =
  useTabFlow({
    selector: ".custom-table",
    startIndex: 4,
  });

const props = defineProps({
  inventoryList: {
    type: Array,
    default: () => [],
  },
  vendorList: {
    type: Array,
    default: () => [],
  },
  selectedVendor: {
    type: Object,
    default: () => null,
  },
  viewOnly: {
    type: Boolean,
    default: () => false,
  },
});

const items = defineModel();
const inputRef = ref(null);
const headers = purchaseRequestItemHeaders;

const newRow = ref({ ...DEFAULT_PURCHASE_ITEM });

const calculateTotalPrice = (row) => {
  if (!row.unitCost || !row.quantity || !row.taxRate) row.totalPrice = 0;
  const subTotal = Number(row.unitCost) * Number(row.quantity);
  const taxAmount = (subTotal * Number(row.taxRate)) / 100;
  row.totalPrice = (subTotal + taxAmount).toFixed(2);
};

const isRowValid = (row) => {
  return headers
    .filter((h) => h.key !== "actions" && h.key !== "totalPrice")
    .every((h) => {
      if (h.key === "taxRate") {
        return (
          row[h.key] !== null && row[h.key] !== undefined && row[h.key] !== ""
        );
      }
      return !!row[h.key];
    });
};

const commitNewRow = async () => {
  if (!isRowValid(newRow.value)) return;
  items.value.push({ ...newRow.value });
  newRow.value = { ...DEFAULT_PURCHASE_ITEM };

  if (props.selectedVendor) {
    newRow.value.vendor = props.selectedVendor;
  }

  console.log(items.value, "items");

  await nextTick();
  focusItemName();
};

const focusItemName = () => {
  // inputRef might be array or single ref depending on v-for
  const comp = Array.isArray(inputRef.value)
    ? inputRef.value[0]
    : inputRef.value;

  const el = comp?.$el?.querySelector("input");
  if (el) {
    el.focus();
  } else {
    // fallback retry if DOM not yet ready
    setTimeout(() => {
      const el2 = comp?.$el?.querySelector("input");
      el2?.focus();
    }, 50);
  }
};

const addRow = (row) => {
  if (!isRowValid(row)) return;
  commitNewRow();
};

const removeRow = (index) => {
  items.value.splice(index, 1);
};

const setValues = (val, row = null) => {
  const target = row || newRow.value;
  if (!val) return;

  target.itemName = val.itemName;
  target.itemCode = val.itemCode;
  target.itemId = val.id;
  target.categoryId = val.category?.id;
  target.subcategoryId = val.subCategory?.id;
  target.purchaseUOM = val.purchaseUnit.symbol;
  target.unitCost = val.unitCost;
};

const handleCurrencyInput = (v, row) => {
  newRow.value.unitCost = v;
  calculateTotalPrice(row);
};

const handleTabFromPlus = async (event) => {
  if (!isRowValid(newRow.value)) {
    event.preventDefault();
    await nextTick(); // wait for DOM update
    focusItemName();
  }
};

watch(
  () => props.selectedVendor,
  (val) => {
    if (val) newRow.value.vendor = val;
  },
  { immediate: true }
);
</script>

<style scoped>
.custom-table >>> .v-data-table-rows-no-data {
  display: none;
}
</style>
