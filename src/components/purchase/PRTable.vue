<template>
  <v-container fluid class="pa-0">
    <v-row no-gutters>
      <v-card class="border rounded-lg my-2" width="100%">
        <v-data-table
          class="table-bordered"
          :items="prList"
          :headers="headers"
          items-per-page="-1"
          fixed-header
          hide-default-footer
          :sort-by="sortBy"
        >
          <template #item.index="{ index }">{{ index + 1 }}</template>
          <template #item.actions="{ index }">
            <v-icon color="error" @click="$emit('removeItem', index)">
              mdi-close
            </v-icon>
          </template>

          <template #item.itemName="{ item }">
            <div>
              <span>{{ item.itemName }}</span>
              <div v-if="item.pkg && item.pkg.name" class="text-caption">
                <span class="text-grey-darken-1">Package:</span>
                {{ item.pkg.name }}
              </div>
              <div v-if="item.itemCode" class="text-caption">
                <span class="text-grey-darken-1">Item Code:</span>
                {{ item.itemCode }}
              </div>
              <div v-if="item.remarks" class="text-caption">
                <span class="text-grey-darken-1">Remarks:</span>
                {{ item.remarks }}
              </div>
            </div>
          </template>

          <template #item.quantity="{ item, index }">
            <span v-if="isEdit">{{ item.quantity }}</span>
            <v-text-field
              v-else
              v-model="item.quantity"
              type="number"
              min="1"
              :rules="[rules.min1]"
              variant="outlined"
              density="compact"
              color="primary"
              hide-details
              @keydown.up.prevent
              @keydown.down.prevent
              @update:model-value="$emit('edit', item, index)"
            >
            </v-text-field>
          </template>

          <template #item.unitCost="{ item, index }">
            <span v-if="isEdit">{{
              parseFloat(item.unitCost).toFixed(2)
            }}</span>
            <currency-input
              v-else
              v-model="item.unitCost"
              @valueChange="(v) => handleInput(v, 'unitCost', item, index)"
            />
          </template>

          <template #item.discount="{ item }">
            <span v-if="isEdit">{{
              parseFloat(item.discount).toFixed(2)
            }}</span>
            <currency-input
              v-else
              v-model="item.discount"
              @valueChange="(v) => handleInput(v, 'discount', item, index)"
            />
          </template>

          <template #item.taxRate="{ item }">
            <span v-if="isEdit">{{ item.taxRate }}%</span>
            <v-text-field
              v-else
              v-model.number="item.taxRate"
              density="compact"
              color="primary"
              hide-details
              suffix="%"
              type="number"
              variant="outlined"
              @keydown.up.prevent
              @keydown.down.prevent
              @update:model-value="$emit('edit', item, index)"
            >
            </v-text-field>
          </template>

          <template #item.taxAmount="{ item }">
            {{ parseFloat(item.taxAmount).toFixed(2) }}
          </template>

          <template #item.totalPrice="{ item }">
            {{ parseFloat(item.totalPrice).toFixed(2) }}
          </template>

          <!-- 👇 Aligned Footer Totals -->
          <template #tfoot>
            <tr class="totals-row">
              <td></td>
              <!-- index -->
              <td class="font-weight-bold">Totals:</td>
              <td></td>
              <!-- vendor -->
              <td></td>
              <!-- quantity -->
              <td></td>
              <!-- unit cost -->
              <td class="text-end font-weight-bold pr-4">
                {{ totalDiscount }}
              </td>
              <td class="text-end font-weight-bold pr-4">{{ avgTaxRate }}%</td>
              <td class="text-end font-weight-bold pr-4">
                {{ totalTaxAmount }}
              </td>
              <td class="text-end font-weight-bold pr-4">
                {{ totalPrice }}
              </td>
              <td v-if="!isEdit"></td>
              <!-- actions -->
            </tr>
          </template>

          <template #no-data>
            <span
              >No Items added yet. Click
              <v-btn
                density="compact"
                icon="mdi-plus"
                class="mx-1"
                variant="tonal"
                @click="$emit('addItem')"
              ></v-btn>
              to add your item.</span
            >
          </template>
        </v-data-table>
      </v-card>
    </v-row>
  </v-container>
</template>

<script setup>
import { purchaseRequestItemHeaders } from "@/helpers/tableHeaders";
import CurrencyInput from "@/components/CurrencyInput.vue";
import rules from "@/helpers/rules";
import { computed, ref } from "vue";
const props = defineProps({
  prList: {
    type: Array,
    default: () => [],
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const sortBy = ref([{ key: "itemName", order: "asc" }]);

const totalDiscount = computed(() => {
  const result = props.prList.reduce(
    (sum, item) => sum + (Number(item.discount * item.quantity) || 0),
    0
  );
  return result >= 0 ? parseFloat(result).toFixed(2) : 0;
});

const totalTaxAmount = computed(() => {
  const result = props.prList.reduce(
    (sum, item) => sum + (Number(item.taxAmount) || 0),
    0
  );
  return result >= 0 ? parseFloat(result).toFixed(2) : 0;
});

const totalPrice = computed(() => {
  const result = props.prList.reduce(
    (acc, item) => acc + (Number(item.totalPrice) || 0),
    0
  );
  return result >= 0 ? parseFloat(result).toFixed(2) : 0;
});

// average tax rate (weighted or simple average)
const avgTaxRate = computed(() => {
  const rates = props.prList.map((item) => Number(item.taxRate) || 0);
  const result = rates.length
    ? rates.reduce((a, b) => a + b, 0) / rates.length
    : 0;
  return result >= 0 ? parseFloat(result) : 0;
});

const emit = defineEmits(["removeItem", "edit", "addItem"]);
const headers = computed(() => {
  if (props.isEdit) {
    return purchaseRequestItemHeaders.filter((h) => h.key !== "actions");
  } else {
    return purchaseRequestItemHeaders;
  }
});

const handleInput = (v, key, item, index) => {
  item[key] = v;
  emit("edit", item, index);
};
</script>
<style>
.totals-row {
  background-color: #f6f6f6;
  position: sticky;
  bottom: 0;
  z-index: 2;
  border-top: 2px solid #ccc;
}

.totals-row td {
  padding: 12px;
  border-right: 1px solid #ddd;
}

.totals-row td:last-child {
  border-right: none;
}
</style>
