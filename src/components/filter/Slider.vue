<template>
  <div class="f-slider">
    <v-row no-gutters>
      <v-col>
        <v-range-slider
          hide-details
          thumb-color="primary"
          track-color="primary"
          thumb-size="14"
          color="primary"
          :min="min"
          :max="max"
          :step="step"
          v-model="slider"
          @end="apply()"
          density="compact"
        ></v-range-slider>
      </v-col>
    </v-row>
    <v-row class="pt-1">
      <v-col class="">
        <v-text-field
          v-model.number="startText"
          type="Number"
          variant="outlined"
          label="From"
          density="compact"
          hide-details
          color="primary"
          @update:model-value="updateSlider($event, 0)"
        ></v-text-field>
      </v-col>
      <v-col class="pl-1">
        <v-text-field
          v-model.number="endText"
          type="Number"
          variant="outlined"
          label="To"
          density="compact"
          hide-details
          color="primary"
          @update:model-value="updateSlider($event, 1)"
        ></v-text-field>
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

const props = defineProps({
  og: {
    type: Object,
    require: true,
  },
  reload: {
    type: Boolean,
    default: false,
  },
});
const min = ref(0);
const max = ref(0);
const step = ref(0.01);
// const selectedMin = ref(0);
// const selectedMax = ref(0);
const slider = ref([0, 0]);
const startText = ref(null);
const endText = ref(null);

const init = () => {
  min.value = props.og.slider.min;
  max.value = props.og.slider.max;
  slider.value = [props.og.slider.selectedMin, props.og.slider.selectedMax];
  startText.value = slider.value[0];
  endText.value = slider.value[1];
};

const apply = () => {
  startText.value = slider.value[0];
  endText.value = slider.value[1];
  props.og.slider.selectedMin = Number(slider.value[0]);
  props.og.slider.selectedMax = Number(slider.value[1]);
  props.og.slider.selected = true;
};

const updateSlider = (event, index) => {
  slider.value[index] = event;
  apply();
};

onMounted(() => init());
</script>

<style scoped>
.f-slider >>> .v-slider.v-input--horizontal .v-slider-track__fill {
  height: 3px !important;
}
</style>
