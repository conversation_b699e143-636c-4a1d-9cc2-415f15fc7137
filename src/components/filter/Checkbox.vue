<template>
  <div>
    <v-text-field
      v-model="searchTxt"
      clearable
      variant="outlined"
      density="compact"
      hide-details
      placeholder="search"
      color="primary"
      class="screener-input"
      append-inner-icon="mdi-magnify"
      v-if="!og.dynamic"
    ></v-text-field>
    <div
      class="d-flex justify-space-between"
      v-for="cb in checkboxes"
      :key="cb.refId"
    >
      <v-checkbox
        v-model="cb.selected"
        hide-details
        :disabled="cb.disabled"
        color="primary"
        density="comfortable"
        @click="toggleCheckBox(cb)"
      >
        <template v-slot:label>
          {{ cb.name }}
        </template>
      </v-checkbox>
      <span class="d-flex align-center">({{ cb.count }})</span>
    </div>

    <div class="pa-1 text-grey" v-if="enableShowMore">
      <a @click="toggleShowMore()" v-if="showMore"> Show more</a>
      <a @click="toggleShowMore()" v-else> Show less</a>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
const props = defineProps({
  og: {
    type: Object,
    require: true,
  },
});

const searchTxt = ref(null);
const limit = ref(10);
const showMoreCount = ref(10);
const showMore = ref(false);

const fCheckboxes = computed(() => {
  let checkboxes = props.og.checkboxes;
  if (searchTxt.value) {
    checkboxes = checkboxes.filter((cb) => {
      let cbName = cb.name.toUpperCase();
      let searchtxt = searchTxt.value.toUpperCase();
      return cbName.indexOf(searchtxt) > -1;
    });
  }
  return checkboxes;
});

const checkboxes = computed(() => {
  return Array.isArray(fCheckboxes.value)
    ? fCheckboxes.value.slice(0, limit.value)
    : [];
});

const count = computed(() => {
  let c =
    props.og && Array.isArray(fCheckboxes.value)
      ? fCheckboxes.value.filter((cb) => !cb.hide).length
      : 0;
  return c;
});

const enableShowMore = computed(() => count.value > showMoreCount.value);

const toggleShowMore = () => {
  showMore.value = !showMore.value;
  if (showMore.value) {
    limit.value = showMoreCount.value;
    return;
  }
  limit.value = count.value ? count.value : 10;
};

const toggleCheckBox = () => {
  searchTxt.value = "";
  // emit
};

onMounted(() => {
  showMore.value = enableShowMore.value;
});
</script>

<style></style>
