<template>
  <v-card
    class="options-elm custom-card ma-4"
    v-for="og in ogs"
    :key="og.refId"
  >
    <v-card-title class="options-elm__title">
      <v-row no-gutters>
        <v-col cols="12">
          <div class="option-title">
            <span class="text-grey font-weight-bold">{{ og.name }}</span>
            <v-spacer></v-spacer>
            <a v-if="og.optionType === 'Checkbox'" class="px-1 text-caption"
              ><span>Select All</span></a
            >
            <a v-if="og.showClearOption" class="px-1 text-caption">
              <span v-if="og.optionType === 'Checkbox'"> Clear </span>
              <span v-else>Reset</span>
            </a>
          </div>
        </v-col>
      </v-row>
    </v-card-title>
    <v-divider></v-divider>
    <v-card-text class="options-elm__body">
      <component :is="componentMap[og.optionType]" :og="og"></component>
    </v-card-text>
  </v-card>
</template>

<script setup>
import Checkbox from "./Checkbox.vue";
import Slider from "./Slider.vue";
defineProps({
  ogs: {
    type: Array,
    default: () => [],
  },
});

const componentMap = {
  Checkbox: Checkbox,
  Slider: Slider,
};
</script>

<style lang="scss">
.options-elm {
  // margin-bottom: 20px;

  &__mandatory {
    position: absolute;
    right: -2px;
    top: -2px;
    border-radius: 0 !important;
  }

  &__title {
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 400;
    color: #ff5a10 !important;

    .option-title {
      width: 100%;
      display: flex;
    }

    .sub-heading {
      font-size: 11px;
      font-weight: 500;
      line-height: 1.5;
    }
  }
}
</style>
