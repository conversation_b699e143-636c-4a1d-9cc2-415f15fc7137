<template>
  <v-container fluid>
    <v-row justify="center">
      <v-col cols="12" md="6" class="text-center">
        <v-progress-circular
          indeterminate
          color="primary"
          size="50"
        ></v-progress-circular>
        <p>Authenticating...</p>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();

const validateToken = () => {
  const token = route.query.token;
  if (token) {
    router.replace("/products");
  } else {
    localStorage.removeItem("token");
    router.replace("/login"); // Redirect to login if no token is found
  }
};

onMounted(() => {
  setTimeout(validateToken, 3000);
});
</script>

<style></style>
