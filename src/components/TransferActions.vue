<template>
  <v-menu>
    <template #activator="{ props }">
      <v-btn
        density="default"
        variant="text"
        color="primary"
        v-bind="props"
        icon="mdi-dots-vertical-circle-outline"
      ></v-btn>
    </template>

    <v-list class="pa-0" density="compact">
      <div
        v-for="(action, index) in filteredOptions"
        :key="`transfer-options--${action.id}`"
      >
        <v-list-item @click="navigate(action.id)">
          <v-list-item-title>
            <v-icon :icon="action.icon" color="primary" class="me-2" />
            {{ action.label }}
          </v-list-item-title>
        </v-list-item>

        <v-divider v-if="index < filteredOptions.length - 1" />
      </div>
    </v-list>
  </v-menu>
</template>

<script setup>
import { computed } from "vue";
import { useRouter } from "vue-router";

const props = defineProps({
  item: Object
});

const options = [
  {
    id: "dispatch",
    label: "Dispatch",
    icon: "mdi-truck",
    enabled: props.item.dispatchStatus !== "completed"
  },
  {
    id: "receive",
    label: "Receive",
    icon: "mdi-package-variant-closed-check",
    enabled: props.item.receiveStatus !== "completed"
  },
  // {
  //   id: "close",
  //   label: "Close",
  //   icon: "mdi-close-circle-outline",
  //   enabled:
  //     props.item.dispatchStatus === "completed" &&
  //     props.item.receiveStatus === "completed"
  // }
];
const router = useRouter();
const navigate = (type) => {
  const routeName =
    type === "dispatch"
      ? "Dispatch Transfer"
      : type === "receive"
      ? "Receive Transfer"
      : "View Transfer";

  router.push({
    name: routeName,
    params: { id: props.item.id },
    query: { type }
  });
};

const filteredOptions = computed(() => {
  return options.filter((o) => {
    // return o.enabled;
    return o;
  });
});

const enabled = computed(() => {
  return filteredOptions.value.length > 0;
});
</script>
