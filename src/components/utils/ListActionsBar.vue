<template>
  <div>
    <!-- Main Toolbar (App Bar style, elevated on scroll) -->
    <v-app-bar
      :height="xs ? 100 : 80"
      scroll-behavior="elevate"
      scroll-threshold="150"
      color="white-smoke"
    >
      <v-container fluid>
        <v-row class="d-flex align-center" dense>
          <!-- 🔎 Search Field (hidden if hideSearch = true) -->
          <v-col cols="12" sm="auto" v-if="!hideSearch">
            <v-text-field
              min-width="300"
              v-model="modelValue.search"
              :label="searchLabel"
              variant="outlined"
              density="compact"
              append-inner-icon="mdi-magnify"
              hide-details
              clearable
              color="primary"
              @update:modelValue="onSearch"
              @click:clear="onSearch"
            />
          </v-col>
          <v-col cols="12" sm="auto">
            <slot name="filter"></slot>
          </v-col>

          <!-- Spacer pushes actions to the right -->
          <v-spacer />

          <!-- ⚡ Right Side Action Buttons -->
          <v-col cols="12" sm="auto" class="text-center text-sm-end">
            <!-- Slot: prepend-actions (extra custom actions placed before defaults) -->
            <slot name="prepend-actions"></slot>

            <!-- Slot: actions (fully overridable right side actions) -->
            <slot name="actions">
              <import-export-menu
                v-if="!hideImportExport"
                :sheets="sheets"
                @refresh="$emit('refresh')"
              />

              <v-btn
                v-if="!hideFilter"
                variant="tonal"
                color="primary"
                class="ml-2"
                @click="toggleFilterDrawer"
              >
                <v-icon icon="mdi-filter-variant"></v-icon>
                <span class="d-none d-md-flex">Filters</span>
              </v-btn>

              <!-- Default Add Button (hidden if hideAdd = true) -->
              <v-btn
                v-if="!hideAdd"
                variant="tonal"
                color="primary"
                class="ml-2"
                @click="$emit('add')"
              >
                <v-icon icon="mdi-plus"></v-icon>
                <span class="d-none d-md-flex">{{ addLabel }}</span>
              </v-btn>

              <!-- Default Refresh Button (hidden if hideRefresh = true) -->
              <v-btn variant="tonal" color="primary" class="ml-2" @click="$emit('refresh')">
                <v-icon icon="mdi-reload"></v-icon>
                <span class="d-none d-md-flex">{{ refreshLabel }}</span>
              </v-btn>
            </slot>
          </v-col>
        </v-row>
      </v-container>
    </v-app-bar>
    <v-divider />

    <filter-sort-panel
      ref="advancedFilter"
      :model-value="modelValue"
      hide-default-activator
      :enable-columns="modelValue.columns.length > 0"
      :enable-filters="filters.length > 0"
      :filter-components="filters"
      @apply="applyFilters"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useDisplay } from "vuetify";

import ImportExportMenu from "@/components/base/ImportExportMenu.vue";
import FilterSortPanel from "@/components/filterSortPanel/FilterSortPanel.vue";

/**
 * Props
 * - hideSearch:    hides the search field
 * - searchLabel:   label text for search box
 * - refreshLabel:  label for refresh button
 * - addLabel:      label for add button
 * - hideAdd:       hides default add button
 * - hideImportExport:  hides default import/export button
 * - sheets:        list of sheets for import/export menu
 * - hideFilter:    hides default filter button
 */
const props = defineProps({
  hideSearch: { type: Boolean, default: false },
  searchLabel: { type: String, default: "Search" },
  refreshLabel: { type: String, default: "Reload" },
  addLabel: { type: String, default: "Add" },
  hideAdd: { type: Boolean, default: false },
  hideImportExport: { type: Boolean, default: false },
  sheets: { type: String, default: "" },
  hideFilter: { type: Boolean, default: false },
  filters: { type: Array, default: () => [] },
  modelValue: {
    // reactive state
    type: Object,
    default: () => ({
      search: null,
      filters: {},
      columns: [],
      options: {}
    })
  }
});

/**
 * Emits
 * - search:   fires when user types in search (>= 3 chars) or clears field
 * - add:      fires when add button is clicked
 * - refresh:  fires when refresh button is clicked
 */
const emit = defineEmits([
  "update:modelValue",
  "apply-filters",
  "search",
  "add",
  "refresh"
]);

const { xs } = useDisplay();

/**
 * Search handler
 * - If user types <= 2 chars, emit empty search
 * - Otherwise emit current search string
 */
const onSearch = () => {
  const req = props.modelValue.search;
  if (req && req.length <= 2) {
    emit("search", "");
    return;
  }
  emit("search", req);
};

// Filter drawer state
const advancedFilter = ref(false);
function toggleFilterDrawer() {
  advancedFilter.value.toggleFilter();
}

const applyFilters = val => {
  val.search = props.modelValue.search;
  emit("update:modelValue", val);
  emit("apply-filters", val);
};
</script>
