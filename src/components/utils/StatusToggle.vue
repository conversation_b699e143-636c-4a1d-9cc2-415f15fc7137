<template>
    <div>
        <!-- Slot for custom toggle (default is a v-chip) -->
        <slot name="toggle">
            <v-switch density="compact" color="success" base-color="error" ripple inset hide-details :model-value="status"
                @update:model-value="onToggle" />
        </slot>
    </div>
</template>

<script setup>
import { inject } from "vue";

/**
 * Props:
 * - status: current boolean status
 * - module: module name (used in confirm message)
 * - onConfirm: callback when user confirms
 */
const props = defineProps({
    status: { type: Boolean, default: false },
    name: { type: String, required: true },
    entity: { type: String},
    noConfirm: { type: Boolean, default: false },
});

// Inject global confirm dialog (provided via plugin)
const $confirm = inject("confirm");
const emit = defineEmits(["toggle"]);

/**
 * onToggle handler
 */
async function onToggle() {
    if (props.noConfirm) {
        emit("toggle", !props.status); // emit new status
        return
    }

    if (!$confirm) {
        console.error("Global confirm dialog not available");
        return;
    }

    // Generate message based on status and module
    const nextAction = `<b>${props.status ? "deactivate" : "activate"}</b>`;
    const confirmMessage = `Are you sure you want to ${nextAction} ${props.entity ? `the ${props.entity} ` : ""} <b>${props.name}</b>?`;

    // Show confirm dialog
    const ok = await $confirm(confirmMessage);
    if (ok) {
        emit("toggle", !props.status); // emit new status
    }
}
</script>
