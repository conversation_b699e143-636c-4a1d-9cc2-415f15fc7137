import defaults from "@/components/utils/default";

function formatCurrency(input, opt = defaults) {
  if (typeof input === "number") {
    input = input.toFixed(fixed(opt.precision));
  }

  const negative = input && input.includes("-") ? "-" : "";
  const numbers = onlyNumbers(input);
  const currency = numbersToCurrency(numbers, opt.precision);
  const [integer, decimal] = toStr(currency).split(".");
  const formattedInteger = addThousandSeparator(integer, opt.thousands);

  return (
    opt.prefix +
    negative +
    joinIntegerAndDecimal(formattedInteger, decimal, opt.decimal) +
    opt.suffix
  );
}

function unformat(input, precision) {
  const negative = input && input.includes("-") ? -1 : 1;
  const numbers = onlyNumbers(input);
  const currency = numbersToCurrency(numbers, precision);
  return parseFloat(currency) * negative;
}

function onlyNumbers(input) {
  return toStr(input).replace(/\D+/g, "") || "0";
}

function fixed(precision) {
  return between(0, precision, 20);
}

function between(min, n, max) {
  return Math.max(min, Math.min(n, max));
}

function numbersToCurrency(numbers, precision) {
  const exp = Math.pow(10, precision);
  const float = parseFloat(numbers) / exp;
  return float.toFixed(fixed(precision));
}

function addThousandSeparator(integer, separator) {
  return integer.replace(/(\d)(?=(?:\d{3})+\b)/gm, `$1${separator}`);
}

function joinIntegerAndDecimal(integer, decimal, separator) {
  return decimal ? `${integer}${separator}${decimal}` : integer;
}

function toStr(value) {
  return value ? value.toString() : "";
}

function setCursor(el, position) {
  const setSelectionRange = () => {
    el.setSelectionRange(position, position);
  };
  if (el === document.activeElement) {
    setSelectionRange();
    setTimeout(setSelectionRange, 1); // Android Fix
  }
}

function event(name) {
  const evt = new Event(name, { bubbles: true, cancelable: true });
  return evt;
}

export { formatCurrency, unformat, setCursor, event };
