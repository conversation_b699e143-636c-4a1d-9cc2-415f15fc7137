<template>
  <v-dialog v-model="dialog" max-width="1200" persistent scrollable>
    <v-card style="height: 95vh; overflow-y: auto">
      <v-card-title>
        <v-row align="center" justify="space-between">
          <v-col cols="auto mx-6 mt-2">
            <span class="">{{ title }} House Unit</span>
          </v-col>
          <v-col cols="auto">
            <v-btn
              icon="mdi-close"
              variant="plain"
              @click="dialog = false"
              color="primary"
            ></v-btn>
          </v-col>
        </v-row>
      </v-card-title>

      <v-card-text>
        <v-form ref="form">
          <v-row class="mx-2">
            <v-col cols="4" class="pa-2">
              <v-text-field
                v-model.trim="record.name"
                label="Unit Name*"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                @blur="cleanName"
                :rules="[rules.require, rules.maxLength(100)]"
              ></v-text-field>
            </v-col>
            <v-col cols="4" class="pa-2">
              <v-text-field
                v-model="record.symbol"
                label="Unit Symbol*"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                :rules="[rules.require, rules.unitLength]"
              ></v-text-field>
            </v-col>
            <v-col cols="4" class="pa-2">
              <v-autocomplete
                v-model="record.unitType"
                :items="unitTypeOptions"
                label="Unit Type*"
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                :rules="[rules.require]"
                clearable
              ></v-autocomplete>
            </v-col>
          </v-row>

          <v-col cols="12" class="mt-4">
            <h3 class="mb-4">
              <v-icon icon="mdi-scale-balance"></v-icon>
              <span class="mx-2">Conversions</span>
            </h3>

            <!-- Single Row for Adding New Conversion -->
            <v-row class="align-center mb-4" dense>
              <v-col cols="5" class="pa-2">
                <v-text-field
                  v-model="newConversion.quantity"
                  label="Quantity"
                  color="primary"
                  hide-details="auto"
                  variant="outlined"
                  density="compact"
                  type="number"
                  @keydown.up.prevent
                  @keydown.down.prevent
                  :rules="[rules.quantity, rules.positive]"
                ></v-text-field>
              </v-col>

              <v-col cols="5" class="pa-2">
                <v-autocomplete
                  v-model="newConversion.toUnit"
                  :items="filteredAvailableUnits"
                  item-title="name"
                  item-value="symbol"
                  label="Conversion Unit Name"
                  color="primary"
                  hide-details="auto"
                  variant="outlined"
                  density="compact"
                  clearable
                >
                  <template v-slot:item="{ props, item }">
                    <v-list-item
                      v-bind="props"
                      :title="`${item.raw.name} (${item.raw.symbol})`"
                    ></v-list-item>
                  </template>
                  <template v-slot:selection="{ item }">
                    {{ item.raw.name }} ({{ item.raw.symbol }})
                  </template>
                </v-autocomplete>
              </v-col>

              <v-col cols="2" class="d-flex align-center pa-2">
                <v-btn
                  variant="outlined"
                  color="primary"
                  @click="addUnit"
                  class=""
                  :disabled="!canAddConversion"
                  ><v-icon icon="mdi-plus"></v-icon>add</v-btn
                >
              </v-col>
            </v-row>

            <!-- Existing Conversions List -->
            <v-row
              v-for="(unit, index) in unitConversions"
              :key="index"
              class="align-center mb-4"
              dense
            >
              <v-col cols="5" class="pa-2">
                <v-text-field
                  v-model="unit.quantity"
                  label="Quantity"
                  color="primary"
                  hide-details="auto"
                  variant="outlined"
                  density="compact"
                  type="number"
                  :rules="[rules.require, rules.quantity, rules.positive]"
                ></v-text-field>
              </v-col>

              <v-col cols="5" class="pa-2">
                <v-autocomplete
                  v-model="unit.toUnit"
                  :items="availableUnits"
                  item-title="name"
                  item-value="symbol"
                  label="Conversion Unit Name"
                  color="primary"
                  hide-details="auto"
                  variant="outlined"
                  density="compact"
                  readonly
                  clearable
                >
                  <template v-slot:item="{ props, item }">
                    <v-list-item
                      v-bind="props"
                      :title="`${item.raw.name} (${item.raw.symbol})`"
                    ></v-list-item>
                  </template>
                  <template v-slot:selection="{ item }">
                    {{ item.raw.name }} ({{ item.raw.symbol }})
                  </template>
                </v-autocomplete>
              </v-col>

              <v-col cols="2" class="d-flex align-center pa-2">
                <v-btn
                  icon="mdi-delete"
                  color="error"
                  variant="plain"
                  @click="removeUnit(index)"
                ></v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-form>
      </v-card-text>

      <v-divider></v-divider>

      <v-card-actions class="px-4">
        <p class="text-primary">* Indicates a Required Field.</p>
        <v-spacer></v-spacer>
        <v-btn
          text="Save"
          @click="save"
          variant="flat"
          :loading="loading"
          :disabled="loading"
          color="primary"
        ></v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, computed, onMounted } from "vue";
import rules from "@/helpers/rules";
import { useHouseUnitStore } from "@/stores/houseUnit";
import { formatName } from "@/helpers/formatter";

const dialog = defineModel();
const emit = defineEmits(["handleSave"]);
const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  title: {
    type: String,
    required: true,
  },
  unitConversions: {
    type: Array,
    default: () => [],
  },
  loading: { type: Boolean, default: false },
});

const form = ref(null);
const houseUnitStore = useHouseUnitStore();

const unitConversions = ref([]);
const unitTypeOptions = ["Mass", "Volume"];
const availableUnits = ref([]);

// Add computed property for filtered units
const filteredAvailableUnits = computed(() => {
  // Get all selected unit names
  const selectedUnits = unitConversions.value.map((conv) => conv.toUnit);

  // Filter out already selected units and current unit
  return availableUnits.value.filter(
    (unit) =>
      !selectedUnits.includes(unit.name) && unit.name !== props.record.name
  );
});

const newConversion = ref({
  quantity: "",
  toUnit: "",
});

// Load tenant-specific units for the dropdown without mutating global table data
const loadAvailableUnits = async () => {
  try {
    const list = await houseUnitStore.fetchHouseUnitsBytenantList();
    availableUnits.value = Array.isArray(list) ? list : [];
  } catch (error) {
    // handle silently
    availableUnits.value = [];
  }
};

watch(
  dialog,
  (isOpen) => {
    if (isOpen) {
      loadAvailableUnits();
    }
  },
  { immediate: true }
);

// Load once on mount in case dialog opens with delayed data
onMounted(() => {
  if (dialog?.value) {
    loadAvailableUnits();
  }
});

// Initialize unitConversions on prop changes
watch(
  () => props.unitConversions,
  (newVal) => {
    unitConversions.value =
      Array.isArray(newVal) && newVal.length > 0 ? [...newVal] : [];
  },
  { immediate: true }
);

watch(
  () => props.record,
  (newVal) => {
    if (newVal && newVal.conversions) {
      unitConversions.value =
        newVal.conversions.length > 0 ? [...newVal.conversions] : [];
    }
  },
  { immediate: true }
);

// Add conversion from the single input row
const addUnit = () => {
  if (!newConversion.value.quantity || !newConversion.value.toUnit) return;

  // Check if unit is already added
  const isDuplicate = unitConversions.value.some(
    (conv) => conv.toUnit === newConversion.value.toUnit
  );

  if (isDuplicate) {
    alert("This unit conversion has already been added");
    return;
  }

  // Push a copy of newConversion
  unitConversions.value.push({
    quantity: Number(newConversion.value.quantity),
    toUnit: newConversion.value.toUnit,
  });

  // Clear inputs after adding
  newConversion.value.quantity = "";
  newConversion.value.toUnit = "";
};

// Disable Add button if fields are empty or invalid
const canAddConversion = computed(() => {
  const quantity = Number(newConversion.value.quantity);
  const isDuplicate = unitConversions.value.some(
    (conv) => conv.toUnit === newConversion.value.toUnit
  );

  return (
    newConversion.value.quantity !== "" &&
    quantity > 0 &&
    newConversion.value.toUnit !== "" &&
    !isDuplicate
  );
});

const removeUnit = (index) => {
  unitConversions.value.splice(index, 1);
};

const save = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  const formattedConversions = unitConversions.value.map((conv) => ({
    toUnit: conv.toUnit,
    quantity: Number(conv.quantity),
  }));

  emit("handleSave", {
    ...props.record,
    conversions: formattedConversions,
  });
};

const cleanName = () => {
  props.record.name = formatName(props.record.name);
};
</script>

<style scoped></style>
