<template>
  <div style="z-index: 5; position: sticky; top: 0">
    <v-container fluid class="bg-white">
      <v-row class="d-flex align-center">
        <!-- search bar -->
        <v-col cols="12" sm="8" md="4">
          <v-text-field
            color="primary"
            label="Search"
            variant="outlined"
            v-model="searchText"
            density="compact"
            append-inner-icon="mdi-magnify"
            hide-details
            clearable
            @update:modelValue="search"
            @click:clear="search"
          ></v-text-field>
        </v-col>
        <slot name="filter"></slot>
        <v-spacer />
        <v-col cols="12" sm="4" md="4" class="text-center text-sm-end">
          <slot></slot>
        </v-col>
      </v-row>
    </v-container>
    <v-divider />
  </div>
</template>

<script setup>
import { ref } from "vue";

const searchText = ref("");
const emit = defineEmits(["search"]);

const search = () => {
  const req = searchText.value;
  if (req && req.length <= 2) {
    emit("search", "");
    return;
  }
  emit("search", req);
};
</script>

<style></style>
