<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    :max-width="maxWidth"
    persistent
  >
    <v-card class="rounded-lg">
      <!-- FIXED HEADER (Close Button) -->
      <v-card-title class="d-flex justify-end align-center py-2 px-3">
        <v-btn
          icon
          size="30"
          color="primary"
          variant="tonal"
          @click="$emit('update:modelValue', false)"
          :disabled="loading"
        >
          <v-icon icon="mdi-close" size="18"></v-icon>
        </v-btn>
      </v-card-title>

      <!-- MAIN TITLE BELOW HEADER (Centered) -->
      <div class="px-4 pb-2 text-center">
        <span class="text-h6">{{ title }}</span>
      </div>

      <v-divider></v-divider>

      <!-- CONTENT -->
      <v-card-text class="py-4 px-4">
        <slot />
      </v-card-text>

      <v-divider></v-divider>

      <!-- FOOTER -->
      <v-card-actions class="justify-center py-3">
        <slot name="footer" />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
defineProps({
  modelValue: { type: Boolean, required: true },
  title: { type: String, default: "" },
  maxWidth: { type: [String, Number], default: "400" },
  loading: { type: Boolean, default: false },
});

defineEmits(["update:modelValue"]);
</script>
