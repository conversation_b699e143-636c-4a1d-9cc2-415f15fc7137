<template>
  <v-dialog v-model="loader.show" width="auto" persistent>
    <v-card class="custom-card pa-10">
      <v-row justify="center" align="center">
        <v-col justify="center" align="center">
          <v-progress-circular
            indeterminate
            size="50"
            color="primary"
            width="5"
            class="slow-spinner"
          ></v-progress-circular>
          <p class="primary mt-4">{{ loader.text }}</p>
        </v-col>
      </v-row>
    </v-card>
  </v-dialog>
</template>

<script setup>
// const dialog = defineModel();
import { storeToRefs } from "pinia";
import { useLoaderStore } from "@/stores/loader";

const { loader } = storeToRefs(useLoaderStore());
</script>

<style>
.slow-spinner .v-progress-circular__overlay {
  animation-duration: 3s !important;
}
</style>
