<template>
  <v-autocomplete
    v-model="storeIds"
    :clearable="!multipleStore"
    label="Locations"
    :items="storeList"
    item-title="name"
    item-value="id"
    hide-details
    density="compact"
    color="primary"
    variant="outlined"
    :multiple="multipleStore"
  >
    <template v-slot:selection="{ item, index }">
      <selection-view
        :item="item"
        :index="index"
        :data="storeIds"
        :single="!multipleStore"
      ></selection-view>
    </template>
    <template v-slot:prepend-item>
      <v-list-item ripple v-if="multipleStore" @click="toggle">
        <template v-slot:default>
          <div class="d-flex align-center px-2">
            <v-icon :icon="icon"></v-icon>
            <v-list-item-title class="mx-2"> Select All </v-list-item-title>
          </div>
          <v-divider class="mt-2"></v-divider>
        </template>
      </v-list-item>
    </template>
  </v-autocomplete>
</template>
<script setup>
import { computed, ref, onMounted, watch } from "vue";
import { useIndexStore } from "@/stores";
import SelectionView from "./SelectionView.vue";

const props = defineProps({
  multipleStore: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["storeChange"]);

const store = useIndexStore();
const storeList = computed(() => store.getStores);
const storeIds = ref([]);

const selectAllStores = computed(
  () => storeIds.value.length === storeList.value.length
);
const selectSomeStore = computed(
  () => storeIds.value.length > 0 && !selectAllStores.value
);
const icon = computed(() => {
  if (selectAllStores.value) return "mdi-close";
  if (selectSomeStore.value) return "mdi-minus-box-outline";
  return "mdi-checkbox-blank-outline";
});

const toggle = () => {
  if (selectAllStores.value) return (storeIds.value = []);
  selectAll();
};

const selectAll = () => {
  storeIds.value = [];

  if (storeList.value.length) {
    storeList.value.forEach((item) => {
      if (props.multipleStore) storeIds.value.push(item.id);
      else storeIds.value = null;
    });
  }
};
watch(storeIds, (v) => {
  emit("storeChange", v);
});

onMounted(() => selectAll());
</script>
