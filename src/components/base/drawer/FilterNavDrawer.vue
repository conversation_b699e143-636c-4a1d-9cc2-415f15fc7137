<template>
  <div>
    <v-navigation-drawer
      v-model="openNav"
      fixed
      location="right"
      width="450"
      class="filter-elem"
      temporary
    >
      <v-card flat>
        <!-- <v-card-title class="d-flex justify-end">
          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            @click="closeNav"
          >
          </v-btn>
        </v-card-title>
        <v-divider></v-divider> -->
        <v-container fluid class="pa-0">
          <v-card-text class="pa-0">
            <v-tabs v-model="tab" color="primary" fixed-tabs>
              <v-tab v-for="tab in tabs" :key="tab.value" :value="tab.value">{{
                tab.label
              }}</v-tab>
            </v-tabs>
            <v-divider></v-divider>
            <v-tabs-window v-model="tab">
              <v-tabs-window-item
                v-for="tab in tabs"
                :key="tab.value"
                :value="tab.value"
              >
                <component
                  :is="tab.component"
                  :filters="tab.filters"
                ></component>
              </v-tabs-window-item>
            </v-tabs-window>
          </v-card-text>
        </v-container>
      </v-card>
      <template v-slot:append>
        <v-divider></v-divider>
        <div class="pa-2">
          <v-btn block color="primary" @click="applyFilters">
            Apply Filter</v-btn
          >
        </div>
      </template>
    </v-navigation-drawer>
  </div>
</template>
<script setup>
import { ref, computed, provide } from "vue";
import { useRoute } from "vue-router";
import { tabConfig } from "@/helpers/filterTabConfig";

const tab = ref(1);
const openNav = ref(false);
const route = useRoute();

// pick tabs based on current screen/route name
const tabs = computed(() => tabConfig[route.name] || []); // fallback to empty if no config

const emit = defineEmits(["applyFilter"]);

const toggle = () => {
  openNav.value = !openNav.value;
};

// used to expose function to be called by external actions
defineExpose({
  toggle,
});

const filters = ref({});
provide("filters", filters);
const applyFilters = () => {
  closeNav();
  emit("applyFilter", filters.value);
};

const closeNav = () => {
  openNav.value = false;
};
</script>
