<template>
  <v-card class="custom-card">
    <card-header
      title="Location / Work Area"
      @select-all="selectAll"
      @clear-selection="clearSelection"
      :show-select-all-option="true"
    />
    <v-divider></v-divider>
    <v-card-text class="pa-0">
      <v-row>
        <v-col cols="12">
          <tree-view v-model="selectedLocations" :data="treeData"></tree-view>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
<script setup>
import TreeView from "@/components/base/drawer/filters/TreeView.vue";
import CardHeader from "@/components/base/drawer/filters/CardHeader.vue";
import { useStoreStore } from "@/stores/store";
import { useLocationStore } from "@/stores/location";
import { ref, computed, onBeforeMount } from "vue";

const storeStore = useStoreStore();
const locationStore = useLocationStore();

// ✅ API state
const locations = computed(() => storeStore.getStores || []);
const workAreas = computed(() => locationStore.getLocations || []);

// ✅ build tree structure (store → locations)
const treeData = computed(() => {
  return locations.value.map((location) => ({
    id: location.id,
    name: location.name,
    children: workAreas.value
      .filter((wa) => wa.storeId === location.id)
      .map((wa) => ({
        id: wa.id,
        name: wa.name,
      })),
  }));
});

const selectedLocations = ref([]);

// ✅ utility actions
const selectAll = () => {
  selectedLocations.value = treeData.value.flatMap(
    (parent) => parent.children?.map((child) => child.id) || []
  );
};

const clearSelection = () => {
  selectedLocations.value = [];
};

onBeforeMount(() => {
  storeStore.fetchStores();
  locationStore.fetchLocations();
});
</script>
