<template>
  <v-container>
    <v-row>
      <v-col cols="12" class="py-1">
        <p class="text-subtitle-1 text-grey-darken-1">{{ filter.title }}</p>
      </v-col>
      <v-col class="py-1">
        <v-btn-toggle
          v-model="selectedValue"
          color="primary"
          multiple
          rounded="xl"
          density="comfortable"
          @update:model-value="updateStatusValue"
          divided
          variant="outlined"
          class="w-100"
          mandatory
        >
          <v-btn
            v-for="item in filter.items"
            :key="item.value"
            :value="item.value"
            class="text-capitalize flex-grow-1"
          >
            {{ item.name }}
          </v-btn>
        </v-btn-toggle>
      </v-col>
    </v-row>

    <!-- Date Picker -->
    <v-row v-if="selectedValue.includes('completed')">
      <v-col cols="12" class="pb-0 pt-4">
        <v-date-input
          v-model="date"
          label="Date Range"
          variant="outlined"
          density="comfortable"
          prepend-icon=""
          prepend-inner-icon="$calendar"
          multiple="range"
          color="primary"
          :max="new Date()"
          hide-details
          @update:model-value="updateDateValue"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, inject } from "vue";
import { VDateInput } from "vuetify/labs/VDateInput";
import { formatDate } from "date-fns";

const props = defineProps({
  filter: {
    type: Object,
    default: () => {},
  },
});

const date = ref([new Date(), new Date()]);
const selectedValue = ref(["pending"]);
const filters = inject("filters");

const updateDateValue = (val) => {
  filters.value["fromDate"] = formatDate(val[0], "yyyy-MM-dd");
  filters.value["toDate"] = formatDate(val[val.length - 1], "yyyy-MM-dd");
};

const updateStatusValue = (val = selectedValue.value) => {
  filters.value[props.filter.key] = val;
  if (!val.includes("completed")) {
    delete filters.value["fromDate"];
    delete filters.value["toDate"];
  } else {
    updateDateValue(date.value);
  }
};

updateStatusValue();
</script>
