<template>
  <v-card class="custom-card">
    <card-header :title="comp.title" />
    <v-divider></v-divider>
    <v-card-text class="pa-1">
      <v-row>
        <v-col cols="12">
          <v-radio-group
            v-model="selectedValue"
            hide-details="auto"
            color="primary"
            @update:model-value="updateValue"
          >
            <v-radio
              v-for="item in comp.items"
              :key="item.name"
              :label="item.name"
              :value="item.value"
            ></v-radio>
          </v-radio-group>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
<script setup>
import CardHeader from "@/components/base/drawer/filters/CardHeader.vue";
import { ref, inject } from "vue";

const props = defineProps({
  comp: {
    type: Object,
    default: () => {},
  },
});

const selectedValue = ref(null);

const filters = inject("filters");

const updateValue = (val) => {
  filters.value[props.comp.key] = val;
};
</script>
