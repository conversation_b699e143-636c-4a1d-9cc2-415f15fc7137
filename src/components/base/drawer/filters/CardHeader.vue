<template>
  <v-card-title class="text-subtitle-2">
    <div class="d-flex justify-space-between align-center">
      <span class="text-grey-darken-1 font-weight-bold">
        {{ title }}
      </span>
      <v-spacer></v-spacer>
      <div class="text-caption text-primary" v-if="showSelectAllOption">
        <v-btn
          class="cursor-pointer text-none"
          variant="plain"
          size="small"
          @click="$emit('selectAll')"
          >Select All</v-btn
        >
        <v-btn
          class="px-0 cursor-pointer text-none"
          variant="plain"
          size="small"
          @click="$emit('clearSelection')"
        >
          Clear
        </v-btn>
      </div>
    </div>
  </v-card-title>
</template>
<script setup>
defineProps({
  title: String,
  showSelectAllOption: {
    type: Boolean,
    default: false,
  },
});
defineEmits(["selectAll", "clearSelection"]);
</script>
