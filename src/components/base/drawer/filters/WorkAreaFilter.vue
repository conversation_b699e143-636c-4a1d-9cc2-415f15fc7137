<template>
  <v-card class="custom-card">
    <card-header :title="comp.title" />
    <v-divider></v-divider>
    <v-card-text class="pa-4">
      <v-row>
        <v-col cols="12">
          <v-autocomplete
            v-model="selectedValue"
            :items="workAreas"
            item-title="name"
            item-value="id"
            multiple
            clearable
            density="compact"
            color="primary"
            variant="outlined"
            hide-details="auto"
          >
            <template v-slot:selection="{ item, index }">
              <selection-view
                :item="item"
                :index="index"
                :data="selectedValue"
              ></selection-view>
            </template>
            <template v-slot:prepend-item>
              <v-list-item ripple @click="toggle">
                <div class="d-flex align-center px-2">
                  <v-icon :icon="icon"></v-icon>
                  <v-list-item-title class="mx-2">
                    Select All
                  </v-list-item-title>
                </div>
                <v-divider class="mt-2"></v-divider>
              </v-list-item>
            </template>
          </v-autocomplete>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
<script setup>
import CardHeader from "@/components/base/drawer/filters/CardHeader.vue";
import { useLocationStore } from "@/stores/location";
import { ref, computed, inject, watch } from "vue";
import SelectionView from "@/components/base/SelectionView.vue";

const locationStore = useLocationStore();
const workAreas = computed(() => locationStore.getLocations || []);

const props = defineProps({
  comp: {
    type: Object,
    default: () => {},
  },
});

const selectedValue = ref([]);

const filters = inject("filters");

const selectAllData = computed(
  () => selectedValue.value.length === workAreas.value.length
);

const selectSomeData = computed(
  () => selectedValue.value.length > 0 && !selectAllData.value
);

const icon = computed(() => {
  if (selectAllData.value) return "mdi-close";
  if (selectSomeData.value) return "mdi-minus-box-outline";
  return "mdi-checkbox-blank-outline";
});

const toggle = () => {
  if (selectAllData.value) {
    selectedValue.value = [];
  } else {
    selectedValue.value = workAreas.value.map(item => item.id);
  }
};

watch(selectedValue, (val) => {
  filters.value[props.comp.key] = val;
});

watch(workAreas, () => {
  selectedValue.value = [];
  filters.value[props.comp.key] = [];
});
</script>
