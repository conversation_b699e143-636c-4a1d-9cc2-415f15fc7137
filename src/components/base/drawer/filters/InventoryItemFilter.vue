<template>
  <v-card class="custom-card">
    <card-header
      title="Inventory items"
      @select-all="selectAll"
      @clear-selection="clearSelection"
      :show-select-all-option="true"
    />
    <v-divider></v-divider>
    <v-card-text>
      <v-row>
        <v-col cols="12">
          <v-text-field
            v-model="searchTxt"
            clearable
            variant="outlined"
            density="compact"
            hide-details
            placeholder="search"
            color="primary"
            class="screener-input mb-2"
            append-inner-icon="mdi-magnify"
          ></v-text-field>
          <div v-for="item in filteredInventoryItems" :key="item.id">
            <v-checkbox
              v-model="selectedInventoryItems"
              hide-details
              color="primary"
              density="compact"
              :label="item.itemName"
              :value="item.id"
              class="my-0"
            ></v-checkbox>
          </div>
          <div class="text-grey" v-if="enableShowMore">
            <v-btn
              v-if="showMore"
              @click="toggleShowMore"
              class="cursor-pointer text-none px-1"
              variant="plain"
              size="small"
              color="primary"
            >
              Show more</v-btn
            >
            <v-btn
              v-else
              @click="toggleShowMore"
              class="cursor-pointer text-none px-1"
              variant="plain"
              size="small"
              color="primary"
            >
              Show less</v-btn
            >
          </div>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
<script setup>
import CardHeader from "@/components/base/drawer/filters/CardHeader.vue";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { ref, computed, onBeforeMount } from "vue";

const searchTxt = ref("");
const selectedInventoryItems = ref([]);

const inventoryItemStore = useInventoryItemStore();

const inventoryItems = computed(() => {
  let items = inventoryItemStore.getInventoryItems;
  if (searchTxt.value) {
    items = items.filter((item) =>
      item.itemName.toLowerCase().includes(searchTxt.value.toLowerCase())
    );
  }
  return items;
});

const filteredInventoryItems = computed(() =>
  inventoryItems.value.slice(0, limit.value)
);

// show more option
const limit = ref(10);
const showMoreCount = ref(10);
const showMore = ref(false);

const enableShowMore = computed(
  () => inventoryItems.value.length > showMoreCount.value
);

const toggleShowMore = () => {
  showMore.value = !showMore.value;
  if (showMore.value) {
    limit.value = showMoreCount.value;
    return;
  }
  limit.value = inventoryItems.value.length ? inventoryItems.value.length : 10;
};

const selectAll = () => {
  selectedInventoryItems.value = inventoryItems.value.map((item) => item.id);
};

const clearSelection = () => {
  selectedInventoryItems.value = [];
};

onBeforeMount(async () => {
  await inventoryItemStore.fetchInventoryItems();
  showMore.value = enableShowMore.value;
});
</script>
