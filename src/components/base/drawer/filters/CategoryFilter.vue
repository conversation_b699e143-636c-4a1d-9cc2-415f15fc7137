<template>
  <v-card class="custom-card">
    <card-header
      title="Category / Subcategory"
      @select-all="selectAll"
      @clear-selection="clearSelection"
      :show-select-all-option="true"
    />
    <v-divider></v-divider>
    <v-card-text class="pa-0">
      <v-row>
        <v-col cols="12">
          <tree-view v-model="selectedCategories" :data="treeData"></tree-view>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
<script setup>
import TreeView from "@/components/base/drawer/filters/TreeView.vue";
import CardHeader from "@/components/base/drawer/filters/CardHeader.vue";
import { useCategoryStore } from "@/stores/category";
import { ref, computed, onBeforeMount } from "vue";

const categoryStore = useCategoryStore();

// ✅ API state
const categories = computed(() => categoryStore.getCategories || []);

// ✅ build tree structure (store → locations)
const treeData = computed(() => {
  return categories.value.map((category) => ({
    id: category.id,
    name: category.name,
    children: category.subCategories,
  }));
});

const selectedCategories = ref([]);

// ✅ utility actions
const selectAll = () => {
  selectedCategories.value = treeData.value.flatMap(
    (parent) => parent.children?.map((child) => child.id) || []
  );
};

const clearSelection = () => {
  selectedCategories.value = [];
};

onBeforeMount(() => {
  categoryStore.fetchCategories();
});
</script>
