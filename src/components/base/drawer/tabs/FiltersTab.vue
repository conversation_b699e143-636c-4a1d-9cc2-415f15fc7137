<template>
  <v-container fluid>
    <v-row>
      <v-col cols="12" v-for="(filterComponent, i) in filters" :key="i">
        <component
          :is="filterComponent.component || filterComponent"
          :comp="filterComponent"
        ></component>
      </v-col>
    </v-row>
  </v-container>
</template>
<script setup>
const props = defineProps({
  filters: {
    type: Array,
    default: () => [],
  },
});
</script>
