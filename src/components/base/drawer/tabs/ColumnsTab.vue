<template>
  <v-container class="pa-0">
    <v-list density="compact" class="draggable-list">
      <Draggable
        :list="headers"
        handle=".drag-handle"
        item-key="key"
        ghost-class="drag-ghost"
        chosen-class="drag-chosen"
        drag-class="drag-active"
      >
        <div v-for="header in headers" :key="header.key">
          <v-list-item :title="header.title">
            <!-- Drag handle -->
            <template #prepend>
              <v-icon class="drag-handle mr-2" color="grey-darken-1">mdi-drag</v-icon>
            </template>

            <!-- Toggle switch -->
            <template #append>
              <v-switch
                density="compact"
                v-model="header.default"
                inset
                color="success"
                base-color="error"
                hide-details
                :disabled="header.mandatory"
              />
            </template>
          </v-list-item>

          <v-divider />
        </div>
      </Draggable>
    </v-list>
  </v-container>
</template>

<script setup>
import { ref, inject, watch } from "vue";
import { useRoute } from "vue-router";
import { tableHeaders } from "@/helpers/tableHeaders";
import { VueDraggableNext as Draggable } from "vue-draggable-next";

const route = useRoute();
const filters = inject("filters");

// Clone headers safely
const headers = ref(
  JSON.parse(JSON.stringify(tableHeaders[route.name]?.value || []))
);

// Sync changes to parent filters
watch(
  headers,
  newVal => {
    filters.value.headers = newVal;
  },
  { deep: true }
);
</script>

<style scoped>
</style>
