<template>
  <v-navigation-drawer
    v-model="drawer"
    fixed
    class="bg-secondary"
    location="start"
    width="320"
  >
    <template v-slot:prepend>
      <v-container fluid>
        <v-row>
          <v-col cols="9">
            <p class="text-h6 font-weight-black text-capitalize">
              {{ tenant?.name }}
            </p>
            <p class="font-weight-medium text-capitalize">
              <v-icon
                icon="mdi-marker-check"
                size="16px"
                class="me-1"
                color="primary"
              ></v-icon
              >{{ tenant?.userName }}
            </p>
            <v-chip
              density="compact"
              class="mt-1"
              label
              color="primary"
              variant="elevated"
              size="small"
            >
              {{ tenant?.roleName }}
            </v-chip>
          </v-col>
        </v-row>
      </v-container>
    </template>
    <v-divider></v-divider>
    <v-list nav density="compact">
      <template v-for="item in menuItems" :key="item.title">
        <v-list-group v-if="item.subItems">
          <template v-slot:activator="{ props }">
            <v-list-item v-bind="props" :prepend-icon="item.icon"
              ><v-list-item-title class="text-subtitle-1">{{
                item.title
              }}</v-list-item-title>
            </v-list-item>
          </template>
          <v-list-item
            v-for="subItem in item.subItems"
            :key="subItem.title"
            replace
            ripple
            :to="subItem.path"
            active-class="selected-chip"
          >
            <template v-slot:prepend>
              <v-row>
                <v-col class="mr-3">
                  <v-icon>{{ subItem.icon }}</v-icon>
                </v-col>
              </v-row>
            </template>
            <v-list-item-title class="text-subtitle-2"
              >{{ subItem.title }} </v-list-item-title
            ><template v-slot:append>
              <v-row>
                <v-col v-if="subItem.addAction">
                  <v-tooltip location="end">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        v-bind="props"
                        density="comfortable"
                        icon="mdi-plus-circle-outline"
                        :color="
                          $route.name === subItem.addAction
                            ? 'primary'
                            : 'transparent'
                        "
                        variant="flat"
                        :to="{ name: subItem.addAction }"
                      >
                      </v-btn>
                    </template>
                    <span>{{ subItem.addAction }}</span></v-tooltip
                  >
                </v-col>
              </v-row>
            </template>
          </v-list-item>
        </v-list-group>
        <v-list-item
          v-else
          :prepend-icon="item.icon"
          replace
          ripple
          :to="item.path"
          active-class="selected-chip"
        >
          <v-list-item-title class="text-subtitle-1">{{
            item.title
          }}</v-list-item-title>
        </v-list-item>
      </template>
    </v-list>
  </v-navigation-drawer>
</template>

<script setup>
import { ref } from "vue";
import { getTenants, getTenantId, getPrivileges } from "@/helpers/auth";
import { filterMenuByPrivileges } from "@/helpers/menuAccess";
import { PRIV_CODES } from "@/constants/privilegeCodes";

const tenants = getTenants();
const tenantId = ref(getTenantId);
const tenant = ref({});

tenant.value = tenants.find((t) => t.id === tenantId.value);

const emit = defineEmits(["openDialog"]);
const drawer = defineModel();

const privileges = getPrivileges();

const rawMenuItems = [
  {
    icon: "mdi-bell-alert", // recommended — notifications + action
    title: "Action Center",
    path: { name: "action-center" },
    privilege_code: PRIV_CODES.DASH_PUR,
  },
  {
    icon: "mdi-view-dashboard",
    title: "Dashboards",
    subItems: [
      {
        icon: "mdi-warehouse",
        title: "Inventory Dashboard",
        path: { 
          name: "inventory-dashboard" 
        },
        privilege_code: PRIV_CODES.DASH_INV,
      },
      {
        icon: "mdi-cart-arrow-down",
        title: "Purchase Dashboard",
        path: { 
          name: "purchase-dashboard" 
        },
        privilege_code: PRIV_CODES.DASH_PUR,
      },
      {
        icon: "mdi-cash-multiple",
        title: "COGS Dashboard",
        path: { 
          name: "cogs-dashboard" 
        },
        privilege_code: PRIV_CODES.DASH_COGS,
      },
    ]
  },
  {
    icon: "mdi-wrench-cog",
    title: "Product",
    privilege_code: PRIV_CODES.PC_VIEW,
    subItems: [
      {
        icon: "mdi-database-export",
        title: "Import & Export",
        path: { name: "Import Export" },
      },
      {
        icon: "mdi-tag",
        title: "Tags",
        path: { name: "tags" },
      },
      {
        icon: "mdi-store",
        title: "Vendors",
        path: { name: "vendors" },
        addAction: "Create Vendor",
      },
      {
        icon: "mdi-shape",
        title: "Categories",
        path: { name: "categories" },
        addAction: "Create Category",
      },
      {
        icon: "mdi-scale",
        title: "House Units",
        path: { name: "houseUnits" },
      },
      {
        icon: "mdi-package-variant",
        title: "Inventory Items",
        path: { name: "inventoryItems" },
        addAction: "Create Inventory Item",
      },
      {
        icon: "mdi-food-variant",
        title: "Recipes",
        path: { name: "recipes" },
        addAction: "Create Recipe",
      },
    ],
  },
  {
    icon: "mdi-network-pos",
    title: "POS",
    subItems: [
      {
        icon: "mdi-food",
        title: "Menu Items",
        path: { name: "Menu Items" },
      },
      {
        icon: "mdi-food-fork-drink",
        title: "Modifiers",
        path: { name: "Modifiers" },
      },
    ],
  },
  {
    icon: "mdi-cart-arrow-up",
    title: "Purchase Requests (PR)",
    path: { name: "Purchase Requests (PR)" },
    privilege_code: "PUR_PR",
  },
  {
    icon: "mdi-package",
    title: "Purchase Order (PO)",
    path: { name: "Purchase Order (PO)" },
    privilege_code: "PUR_PO",
  },
  {
    icon: "mdi-invoice-text-multiple-outline",
    title: "Goods Received Note (GRN)",
    path: { name: "Goods Received Note (GRN)" },
    privilege_code: "PUR_GRN",
  },
  {
    icon: "mdi-cart",
    title: "Transfers",
    path: { name: "transfers" },
    privilege_code: "PUR_INDENT",
  },
  {
    icon: "mdi-folder-lock",
    title: "Closing",
    path: { name: "closing" },
  },

  {
    icon: "mdi-chart-box-outline",
    title: "Stocks",
    privilege_code: PRIV_CODES.STK_VIEW,
    subItems: [
      {
        icon: "mdi-archive-outline",
        title: "Stocks",
        path: { name: "stocks" },
      },
      {
        icon: "mdi-list-box-outline",
        title: "Stock ledgers",
        path: { name: "Stock Ledgers" },
      },
    ],
  },

  {
    icon: "mdi-chart-box-outline",
    title: "Reports",
    privilege_code: PRIV_CODES.REP_GRN,
    subItems: [
      {
        icon: "mdi-archive-outline",
        title: "General Reports",
        path: { name: "Reports" },
      },
    ],
  },
  {
    icon: "mdi-cog-outline",
    title: "Setup",
    subItems: [
      {
        icon: "mdi-store-marker-outline",
        title: "Locations",
        path: { name: "locations" },
        privilege_code: PRIV_CODES.SET_LOC,
      },
      {
        icon: "mdi-domain",
        title: "Work/Storage Area",
        path: { name: "workArea" },
        privilege_code: PRIV_CODES.SET_LOC,
      },
      {
        icon: "mdi-account-key",
        title: "Roles",
        path: { name: "roles" },
        addAction: "Create Role",
        privilege_code: PRIV_CODES.SET_USER,
      },
      {
        icon: "mdi-account",
        title: "Users",
        path: { name: "users" },
        privilege_code: PRIV_CODES.SET_USER,
        // addAction: "Create User",
      },
    ],
  },
];

const menuItems = filterMenuByPrivileges(rawMenuItems, privileges);
</script>

<style></style>
