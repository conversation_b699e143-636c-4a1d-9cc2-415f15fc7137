<template>
  <v-dialog v-model="dialog" max-width="1200" persistent scrollable>
    <v-card class="import-preview-card">
      <v-card-title class="d-flex align-center justify-space-between py-3 px-4">
        <div class="d-flex align-center ga-2">
          <v-icon icon="mdi-eye"></v-icon>
          <span class="title-text">Import Preview</span>
        </div>
        <v-btn icon="mdi-close" variant="plain" color="primary" @click="dialog = false" />
      </v-card-title>

      <v-card-subtitle class="px-4 pb-0">
        <div class="d-flex flex-wrap align-center ga-3 info-chips">
          <v-chip v-if="fileName" color="primary" variant="tonal" density="comfortable">
            <v-icon start icon="mdi-file-excel"></v-icon>
            {{ fileName }}
          </v-chip>
          <v-chip v-if="displaySheetName" color="primary" variant="tonal" density="comfortable">
            <v-icon start icon="mdi-table"></v-icon>
            Sheet: {{ displaySheetName }}
          </v-chip>
          <v-chip v-if="activeRows && activeRows.length" color="primary" variant="tonal" density="comfortable">
            <v-icon start icon="mdi-format-list-numbered"></v-icon>
            Rows: {{ activeRows.length }}
          </v-chip>
        </div>
      </v-card-subtitle>

      <v-card-text class="pt-3 px-4">
        <div v-if="sheetNames.length > 1" class="d-flex align-center mb-3">
          <v-autocomplete
            v-model="selectedSheetName"
            :items="sheetNames"
            label="Select Sheet"
            variant="outlined"
            density="compact"
            hide-details
            style="max-width: 260px"
            clearable
          ></v-autocomplete>
        </div>
        <div v-if="errorMessage" class="mb-4 text-error">
          {{ errorMessage }}
        </div>
        <v-data-table
          v-if="activeHeaders.length && activeRows && activeRows.length"
          :headers="activeHeaders"
          :items="activeRows"
          density="compact"
          fixed-header
          hover
          :items-per-page="10"
          :hide-default-footer="activeRows.length <= 10"
          class="custom-table preview-table"
        >
        </v-data-table>
        <div v-else class="text-center py-10">No preview data available.</div>
      </v-card-text>

      <v-divider></v-divider>

      <v-card-actions class="px-4 py-3">
        <v-spacer></v-spacer>
        <v-btn variant="outlined" color="primary" @click="dialog = false">
          Cancel
        </v-btn>
        <v-btn variant="flat" color="primary" :loading="loading" :disabled="loading || !activeRows || !activeRows.length" @click="onUpload">
          Upload
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { computed, ref, watch } from "vue";

const dialog = defineModel();
const emit = defineEmits(["upload"]);

const props = defineProps({
  headers: { type: Array, default: () => [] }, // single-sheet headers [{ title, key }]
  rows: { type: Array, default: () => [] },   // single-sheet rows
  sheets: { type: Array, default: () => [] }, // optional: [{ name, headers, rows }]
  fileName: { type: String, default: "" },
  sheetName: { type: String, default: "" },
  loading: { type: Boolean, default: false },
  errorMessage: { type: String, default: "" },
});

// Sheet selection logic (backward compatible)
const selectedSheetName = ref("");

const sheetNames = computed(() => {
  return Array.isArray(props.sheets) && props.sheets.length
    ? props.sheets.map((s) => s.name).filter(Boolean)
    : [];
});

watch(
  () => props.sheets,
  (val) => {
    if (Array.isArray(val) && val.length) {
      const desired = props.sheetName && sheetNames.value.includes(props.sheetName)
        ? props.sheetName
        : sheetNames.value[0];
      selectedSheetName.value = desired;
    } else {
      selectedSheetName.value = props.sheetName || "";
    }
  },
  { immediate: true }
);

const displaySheetName = computed(() => {
  if (sheetNames.value.length) return selectedSheetName.value;
  return props.sheetName;
});

const activeHeaders = computed(() => {
  if (sheetNames.value.length) {
    const sheet = props.sheets.find((s) => s.name === selectedSheetName.value);
    if (!sheet) return [];
    return (sheet.headers || []).map((h) => (typeof h === "string" ? { title: h, key: h } : h));
  }
  if (!Array.isArray(props.headers)) return [];
  return props.headers.map((h) => (typeof h === "string" ? { title: h, key: h } : h));
});

const activeRows = computed(() => {
  if (sheetNames.value.length) {
    const sheet = props.sheets.find((s) => s.name === selectedSheetName.value);
    return sheet?.rows || [];
  }
  return props.rows || [];
});

const onUpload = () => emit("upload");
</script>

<style scoped>
.import-preview-card {
  height: 80vh;
  overflow-y: auto;
  border-radius: 12px;
}

.title-text {
  font-weight: 600;
}

.custom-table >>> .v-table__wrapper {
  max-height: calc(80vh - 230px);
  overflow-y: auto;
}

/* Add visible grid lines */
.custom-table >>> .v-table__wrapper {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 6px;
}
.custom-table >>> .v-table__wrapper table {
  border-collapse: collapse !important;
  width: 100%;
}
.custom-table >>> .v-table__wrapper table thead th,
.custom-table >>> .v-table__wrapper table tbody td {
  border: 1px solid rgba(0, 0, 0, 0.12) !important;
}

/* Header background and sticky effect polish */
.preview-table >>> thead th {
  background: #fafafa;
  position: sticky;
  top: 0;
  z-index: 1;
  border-bottom: 1px solid rgba(0, 0, 0, 0.16) !important;
}

/* Zebra rows and hover */
.preview-table >>> tbody tr:nth-child(even) td {
  background-color: #fcfcfc;
}
.preview-table >>> tbody tr:hover td {
  background-color: #f3f7ff;
}

/* Compact cell padding */
.preview-table >>> td, .preview-table >>> th {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}
</style>
