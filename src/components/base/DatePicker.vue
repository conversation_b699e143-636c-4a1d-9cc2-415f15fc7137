<template>
  <v-menu v-model="menu" :close-on-content-click="false" location="center">
    <template v-slot:activator="{ props }">
      <v-text-field
        density="compact"
        label="Date"
        variant="outlined"
        hide-details="auto"
        color="primary"
        clearable
        prepend-inner-icon="mdi-calendar"
        readonly
        v-bind="props"
      />
    </template>
    <div class="position-relative d-flex">
      <v-date-picker
        id="fromDate"
        v-model="fromDate"
        color="primary"
        :width="xs ? width - 30 : 'auto'"
        height="auto"
        show-adjacent-months
        title=""
      />
      <!-- :min="minDate"
        :max="maxDate" -->
      <!-- <v-date-picker
        id="toDate"
        v-model="toDate"
        color="primary"
        :width="xs ? width - 30 : 'auto'"
        height="auto"
        show-adjacent-months
        title=""
        :min="fromDate"
        :max="maxDate"
      /> -->
      <!-- <v-list v-if="options.length">
        <v-list-item v-for="(option, index) in options" :key="option.name">
          <v-btn :id="'setDate_' + index" color="primary" variant="plain">{{
            option.name
          }}</v-btn>
        </v-list-item>
      </v-list> -->
      <!-- <v-btn
        class="position-absolute top-0 right-0"
        color="white"
        icon="mdi-close"
        size="small"
        variant="text"
        @click="menu = false"
      ></v-btn> -->
    </div></v-menu
  >
</template>
<script setup>
import { defineProps, onMounted, ref } from "vue";
import { format } from "date-fns";
import { useDisplay } from "vuetify";

const emit = defineEmits(["change"]);
const { xs } = useDisplay();

const props = defineProps({
  dateFormat: {
    type: String,
    default: "dd-MM-yyyy",
  },
  minDate: String,
  maxDate: String,
  startDate: String,
  endDate: String,
  formattedDate: String,
  //   options: {
  //     type: Array,
  //     default: () => [
  //       {
  //         name: "Today",
  //         start: (f) => {
  //           return format(new Date(), f);
  //         },
  //         end: (f) => {
  //           return format(new Date(), f);
  //         },
  //       },
  //       {
  //         name: "Yesterday",
  //         start: (f) => {
  //           return format(subDays(new Date(), 1), f);
  //         },
  //         end: (f) => {
  //           return format(subDays(new Date(), 1), f);
  //         },
  //       },
  //       {
  //         name: "This Week",
  //         start: (f) => {
  //           let today = new Date();
  //           let d = getDay(today);
  //           return format(subDays(today, d), f);
  //         },
  //         end: (f, r, l) => {
  //           if (l) {
  //             return format(lastDayOfWeek(new Date()), f);
  //           }

  //           return format(new Date(), f);
  //         },
  //       },
  //       {
  //         name: "Last Week",
  //         start: (f) => {
  //           let today = new Date();
  //           let d = getDay(today) + 7;
  //           return format(subDays(today, d), f);
  //         },
  //         end: (f) => {
  //           let today = new Date();
  //           let d = getDay(today) + 1;
  //           return format(subDays(today, d), f);
  //         },
  //       },
  //       {
  //         name: "This Month",
  //         start: (f) => {
  //           let today = new Date();
  //           let d = getDate(today) - 1;
  //           return format(subDays(new Date(), d), f);
  //         },
  //         end: (f, r) => {
  //           if (r) {
  //             return format(endOfMonth(new Date()), f);
  //           }

  //           return format(new Date(), f);
  //         },
  //       },
  //       {
  //         name: "Last Month",
  //         start: (f) => {
  //           let today = new Date();
  //           let d = getDate(today) - 1;
  //           let date = subDays(new Date(), d);
  //           return format(subMonths(date, 1), f);
  //         },
  //         end: (f) => {
  //           let today = new Date();
  //           let d = getDate(today);
  //           return format(subDays(new Date(), d), f);
  //         },
  //       },
  //     ],
  //   },
});

const menu = ref(false);
const fromDate = ref(null);
const toDate = ref(null);
const pickerFormat = "dd-MM-yyyy";
onMounted(() => {
  init();
});

// function formatDate(inputDate) {
//   if (!inputDate) return "";
//   const parsedDate = new Date(inputDate);
//   if (!parsedDate.getTime()) return "";
//   let date = format(parsedDate, "dd-MM-yyyy");
//   return date;
// }

const init = () => {
  fromDate.value = props.startDate || format(new Date(), pickerFormat);
  toDate.value = props.endDate || format(new Date(), pickerFormat);
};
</script>
<style>
.v-overlay__content:has(> .v-date-picker) {
  min-width: auto !important;
}

.v-picker-title {
  padding: 0 !important;
}

@media only screen and (max-width: 600px) {
  .v-overlay__content:has(> .v-date-picker) {
    left: 0 !important;
  }
}
</style>
