<template>
  <Dialog
    v-model="dialog"
    :title="`Import ${sheetName}`"
    :maxWidth="400"
    :loading="loading"
  >
    <!-- DEFAULT SLOT CONTENT -->
    <template #default>
      <v-card-text class="ma-2 pa-0">
        <v-card
          class="d-flex align-start pa-2"
          min-height="80"
          variant="tonal"
          color="primary"
        >
          <!-- FILE ICON -->
          <v-col cols="auto" class="pa-0">
            <v-icon
              start
              color="green"
              size="40"
              icon="mdi-file-excel"
            />
          </v-col>

          <!-- FILE NAME -->
          <v-col cols="8" class="pa-0">
            <p class="text-black text-subtitle-1">{{ fileName }}</p>
          </v-col>

          <v-spacer />

          <!-- STATUS ICONS -->
          <v-col cols="1" class="pa-0 d-flex justify-end">
            <v-progress-circular
              v-if="loading"
              indeterminate
              color="primary"
            />
            <v-icon
              v-else-if="isSuccess"
              icon="mdi-check-circle"
              color="green"
              size="24"
            />
            <v-icon
              v-else
              icon="mdi-attachment"
              color="red"
              size="28"
              class="cursor-pointer"
              @click="$emit('retry')"
            />
          </v-col>
        </v-card>

        <!-- ERROR MESSAGE -->
        <div v-if="errorMessage" class="my-2 text-error">
          <div>{{ errorMessage }}.</div>
          <div class="my-2">
            Task id: {{ taskId }},
            <span
              class="text-red text-decoration-underline cursor-pointer"
              @click="goToLogs"
            >
              Check Logs
            </span>
          </div>
        </div>
      </v-card-text>
    </template>

    <!-- FOOTER SLOT -->
    <template #footer>
      <v-btn
        v-if="!isSuccess"
        variant="flat"
        color="primary"
        :disabled="loading"
        @click="onUpload"
      >
        Upload
      </v-btn>

      <div v-else class="text-green font-weight-medium">
        {{ successMessage }}
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import Dialog from "@/components/base/Dialog.vue";
import { useRouter } from "vue-router";

const dialog = defineModel();

const emit = defineEmits(["upload", "retry"]);

const props = defineProps({
  fileName: { type: String, default: "" },
  sheetName: { type: String, default: "" },
  errorMessage: { type: String, default: "" },
  successMessage: { type: String, default: "" },
  loading: { type: Boolean, default: false },
  isSuccess: { type: Boolean, default: false },
  taskId: { type: String, default: "" },
});

const router = useRouter();

function goToLogs() {
  if (router.currentRoute.value.path === "/import-export") {
    router.go(); // force reload same page
  } else {
    router.push("/import-export");
  }
}

const onUpload = () => emit("upload");
</script>
