<template>
  <v-dialog v-model="model" width="420" persistent>
    <v-card class="custom-card">
      <v-card-title class="d-flex justify-end">
        <v-btn
          variant="tonal"
          icon="mdi-close"
          color="error"
          size="small"
          @click="model = null"
        >
        </v-btn
      ></v-card-title>
      <v-card-title class="py-2 text-center text-wrap">
        Reason for reject?
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text class="py-4">
        <v-textarea
          label="Reason"
          variant="outlined"
          required
          v-model="reason"
          density="compact"
          :rules="[rules.require]"
          hide-details="auto"
          color="primary"
          rows="2"
        ></v-textarea>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-actions class="d-flex justify-center">
        <div class="text-center">
          <v-btn
            color="error"
            variant="text"
            flat
            border
            :disabled="!reason"
            @click="submit"
            >Submit</v-btn
          >
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { defineModel, ref } from "vue";
import rules from "@/helpers/rules";

const model = defineModel();
const emit = defineEmits(["reject"]);

const reason = ref(null);

const submit = () => {
  emit("reject", reason.value);
  model.value = null;
};
</script>
