<template>
  <v-col cols="auto" class="d-flex justify-center align-center">
    <v-menu v-model="menu" :close-on-content-click="false" location="bottom">
      <template v-slot:activator="{ props }">
        <v-icon icon="mdi-account-circle" color="primary" size="x-large" v-bind="props"></v-icon>
      </template>

      <v-card min-width="250px">
        <v-list>
          <div v-for="(option, index) in options" :key="option.title">
            <v-list-item @click="option.action && option.action()" :title="option.title" :prepend-icon="option.icon">
              <template #prepend>
                <v-icon :icon="option.icon" color="primary" />
              </template>
                <div v-if="option.isChip" class="d-flex align-center">
                  <v-chip
                    density="compact"
                    label
                    color="primary"
                    size="small"
                  >{{ tenant.roleName }}</v-chip>
                </div>
            </v-list-item>

            <!-- divider except for the last item -->
            <v-divider v-if="index < profile.length - 1"></v-divider>
          </div>
        </v-list>
      </v-card>
    </v-menu>
  </v-col>
</template>

<script setup>
import { ref, computed, inject } from "vue";
import { useRoute, useRouter } from "vue-router";

import globalData from "@/composables/global";
import { getTenants, getTenantId, getUser, logout } from "@/helpers/auth";

const tenants = getTenants();
const tenantId = ref(getTenantId);
const tenant = ref({});
const route = useRoute();
const router = useRouter();
// Inject global confirm dialog (provided via plugin)
const $confirm = inject("confirm");

tenant.value = tenants.find(t => t.id === tenantId.value);

const userName = computed(() => getUser().userName);

const exitApp = async () => {
  if (!$confirm) {
    console.error("Global confirm dialog not available");
    return;
  }

  // Show confirm dialog
  const ok = await $confirm("Are you sure want to logout?");
  if (!ok) {
    return;
  }

  logout();
  window.location.href =
    globalData.$authUrl + `/logout?client_id=${globalData.$clientId}`;
};

const menu = ref(false);
const profile = ref([
  {
    action: null,
    title: userName,
    icon: "mdi-account-circle",
    show: true,
    isChip: true
  },
  {
    action: () => router.push("/select-tenant?path=" + route.fullPath),
    title: "Switch Tenant",
    icon: "mdi-home-switch-outline",
    show: tenants.length > 1
  },
  {
    action: exitApp,
    title: "Sign out",
    icon: "mdi-logout",
    show: true
  }
]);
const options = computed(() => {
  return profile.value.filter(item => item.show);
});
</script>
