<template>
  <v-app-bar fixed elevation="1">
    <slot></slot>
    <v-toolbar-title class="ml-0">
      {{ pageTitle }}
    </v-toolbar-title>
    <!-- <v-col cols="4" v-if="showLocationSelector">
      <v-autocomplete
        v-model="selectedLocation"
        :items="locations"
        item-value="id"
        item-title="name"
        label="Work/Storage Area"
        hide-details
        variant="outlined"
        density="compact"
        color="primary"
        return-object
        @update:model-value="handleLocationChange"
        clearable
      ></v-autocomplete>
    </v-col> -->

    <!-- Profile Menu -->
    <profile-menu />
  </v-app-bar>
</template>

<script setup>
import { computed } from "vue";
import { useRoute } from "vue-router";
// import { useLocationStore } from "@/stores/location";
import ProfileMenu from "./profileMenu.vue";

const route = useRoute();

// const locationStore = useLocationStore();

const pageTitle = computed(() => {
  const routeName = route.name;
  if (!routeName) return "";
  return String(routeName[0]).toUpperCase() + String(routeName).slice(1);
});

// const showLocationSelector = computed(() => {
//   return [
//     "Purchase Requests (PR)",
//     "Purchase Order (PO)",
//     "Create Purchase Request (PR)",
//     "Edit Purchase Request (PR)",
//     "Edit Purchase Order (PO)",
//     "Goods Received Note (GRN)",
//   ].includes(route.name);
// });

// const locations = computed(() => locationStore.getLocations);

// const selectedLocation = computed(() => locationStore.getSelectedLocation);

// const handleLocationChange = (v) => {
//   locationStore.setSelectedLocation(v);
// };
</script>

<style scoped></style>
