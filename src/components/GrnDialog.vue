<template>
  <v-dialog v-model="model" width="1000" persistent>
    <v-card class="custom-card">
      <v-card-title
        class="d-flex justify-space-between align-center dialog-title-background"
      >
        <p>GRN Overview</p>
        <v-btn
          variant="text"
          icon="mdi-close"
          color="error"
          @click="closeDialog"
        />
      </v-card-title>

      <v-divider class="mb-4" />

      <v-card-text class="py-4">
        <v-row>
          <v-col cols="4">
            <p>
              <strong>GRN Number:</strong> {{ props.grn?.grnNumber || "-" }}
            </p>
          </v-col>
          <v-col cols="4">
            <p>
              <strong>Invoice Number:</strong>
              {{ props.grn?.invoiceNumber || "-" }}
            </p>
          </v-col>
          <v-col cols="4">
            <p><strong>Vendor:</strong> {{ props.grn?.vendorName || "-" }}</p>
          </v-col>
        </v-row>
      </v-card-text>

      <v-col cols="12">
        <v-data-table
          :headers="filteredHeaders"
          :items="localItems"
          class="custom-table"
          hide-default-footer
          density="compact"
        >
          <template v-slot:item="{ item, index }">
            <tr class="result-elem default">
              <td
                v-for="(header, i) in filteredHeaders"
                :key="header.key + i"
                class="py-4 text-left"
              >
                <span v-if="header.key === 'itemName'">{{
                  item.itemName || "-"
                }}</span>
                <span v-else-if="header.key === 'itemCode'">{{
                  item.itemCode || "-"
                }}</span>
                <span v-else-if="header.key === 'quantity'"
                  >{{ item.quantity || "-" }}{{ item.purchaseUOM }}</span
                >
                <span v-else-if="header.key === 'receivedQty'"
                  >{{ item.receivedQty || "-" }} {{ item.purchaseUOM }}</span
                >
                <span v-else-if="header.key === 'unitCost'">{{
                  item.unitCost || "-"
                }}</span>
                <span v-else-if="header.key === 'taxRate'"
                  >{{ item.taxRate || "0" }} %</span
                >
                <span v-else-if="header.key === 'totalPrice'">{{
                  item.totalPrice || "-"
                }}</span>
              </td>
            </tr>
          </template>

          <!-- Additional row below items -->
          <template v-slot:body.append>
            <tr>
              <td
                v-for="(header, i) in filteredHeaders"
                :key="'grn_row_' + i"
                class="py-2 text-center font-medium"
              >
                <span v-if="header.key === 'totalPrice'">{{
                  props.grn?.totalValue.toFixed(2) || "-"
                }}</span>
                <span v-else-if="header.key === 'taxRate'">Total Price</span>
              </td>
            </tr>
          </template>
        </v-data-table>
      </v-col>

      <v-card-actions class="d-flex justify-center ma-2">
        <div class="d-flex align-center">
          <v-btn color="primary" variant="flat" @click="navigate" class="mx-3">
            Continue to GRN View
          </v-btn>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, computed } from "vue";
import { purchaseOrderItemRecord as DEFAULT_PURCHASE_ITEM } from "@/helpers/defaultRecords";
import { useRouter } from "vue-router";
const router = useRouter();

const props = defineProps({
  grn: { type: Object, default: () => ({}) },
  headers: { type: Array, required: true },
  items: { type: Array, default: () => [] },
  inventoryList: { type: Array, default: () => [] },
  vendorList: { type: Array, default: () => [] },
  selectedVendor: { type: Object, default: null },
  viewOnly: { type: Boolean, default: false },
});

// Filter out the actions column
const filteredHeaders = computed(() =>
  props.headers.filter((h) => h.key !== "actions")
);

const navigate = (id) => {
  router.push({
    name: "Edit Goods Received Note (GRN)",
    params: { id: props.grn.id },
  });
};

const emit = defineEmits(["update:items", "update:modelValue"]);
const model = ref(false);

const localItems = ref(
  props.items.map((item) => ({
    ...item,
    receivedQuantity: item.receivedQuantity || item.quantity,
  }))
);

watch(
  () => props.items,
  (val) => {
    localItems.value = val
      ? val.map((i) => ({
          ...i,
          receivedQuantity: i.receivedQuantity || i.quantity,
        }))
      : [];
  },
  { deep: true, immediate: true }
);

watch(localItems, (val) => emit("update:items", val), { deep: true });

const newRow = ref({ ...DEFAULT_PURCHASE_ITEM });

const calculateTotalPrice = (row) => {
  const unit = Number(row.unitCost) || 0;
  const qty = Number(row.receivedQuantity) || 0;
  const tax = Number(row.taxRate) || 0;
  const subTotal = unit * qty;
  row.totalPrice = (subTotal + (subTotal * tax) / 100).toFixed(2);
};

const handleCurrencyInput = (v, row) => {
  row.unitCost = v;
  calculateTotalPrice(row);
};

watch(
  () => props.selectedVendor,
  (val) => {
    if (val) newRow.value.vendor = val;
  },
  { immediate: true }
);

const closeDialog = () => {
  model.value = false;
  emit("update:modelValue", false);
};
</script>
