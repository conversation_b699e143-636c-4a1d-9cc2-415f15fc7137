<template>
  <v-container fluid>
    <v-row>
      <v-col v-for="card in cardsData" :key="card.title">
        <summary-cards :card="card"></summary-cards>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12">
        <v-card class="parent-cont" width="100%">
          <v-container fluid grid-list-lg class="pa-0">
            <v-data-table
              :headers="headers"
              :items="stocksData.locations"
              class="custom-table"
              :hide-default-footer="stocksData.locations.length < 11"
            >
              <template #item="{ item }">
                <tr>
                  <td
                    v-for="header in headers"
                    :key="header.key"
                    :class="`text-${header.align}`"
                  >
                    <div v-if="header.key === 'locationName'">
                      <v-btn
                        size="x-small"
                        :icon="
                          showWorkAreas[item.locationId]
                            ? 'mdi-chevron-down'
                            : 'mdi-chevron-right'
                        "
                        variant="text"
                        v-if="item.workAreas && item.workAreas.length > 0"
                        @click="toggleExpand(item.locationId)"
                      >
                      </v-btn>
                      <span class="font-weight-medium">{{
                        item.locationName
                      }}</span>
                    </div>
                    <div v-else-if="header.key === 'totalQty'">
                      {{ item[header.key] }} {{ stocksData.unit }}
                    </div>
                    <div v-else-if="header.key === 'avgCost'">
                      {{ item[header.key] }} / {{ stocksData.unit }}
                    </div>
                    <div v-else>
                      {{ item[header.key] ?? "-" }}
                    </div>
                  </td>
                </tr>
                <tr
                  v-if="showWorkAreas[item.locationId]"
                  v-for="work in item.workAreas"
                  :key="work.workAreaId"
                  class="bg-grey-lighten-5"
                >
                  <td class="pl-13">{{ work.workAreaName }}</td>
                  <td>{{ work.qty }} {{ stocksData.unit }}</td>
                  <td>{{ work.value }}</td>
                  <td>{{ work.avgCost }} / {{ stocksData.unit }}</td>
                  <td>{{ work.parLevel }}</td>
                  <td class="text-end">
                    <v-chip
                      :color="work.belowPar ? 'red' : 'green'"
                      size="small"
                      label
                    >
                      {{ work.belowPar ? "Below Par" : "OK" }}
                    </v-chip>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-container>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>
<script setup>
import { ref, computed } from "vue";

import SummaryCards from "@/components/inventoryItem/SummaryCards.vue";

const showWorkAreas = ref({});

const cards = [
  {
    icon: "mdi-finance",
    title: "Total Qty",
    color: "primary",
    key: "totalQty",
  },
  {
    icon: "mdi-currency-inr",
    title: "Total Cost",
    color: "purple",
    key: "totalValue",
  },
  {
    icon: "mdi-cash",
    title: "Average Price",
    color: "blue",
    key: "avgCost",
  },
];

const headers = [
  {
    title: "Location",
    align: "start",
    sortable: false,
    key: "locationName",
    width: "25%",
  },
  { title: "Qty", align: "start", sortable: false, key: "totalQty" },
  { title: "Total Value", align: "start", sortable: false, key: "totalValue" },
  { title: "Avg Cost", align: "start", sortable: false, key: "avgCost" },
  { title: "PAR Level", align: "start", sortable: false, key: "parLevel" },
  { title: "Below PAR", align: "end", sortable: false, key: "belowPar" },
];

// api response
const stocksData = ref({
  itemId: "1",
  itemName: "Coke",
  totalQty: 110,
  totalValue: 3260,
  avgCost: 40.75,
  unit: "btl",
  locations: [
    {
      locationId: "Outlet1",
      locationName: "Outlet 1",
      totalQty: 80,
      totalValue: 3260,
      avgCost: 40.75,
      workAreas: [
        {
          workAreaName: "Store1",
          qty: 50,
          value: 2037.5,
          avgCost: 40.75,
          parLevel: 100,
          belowPar: true,
        },
        {
          workAreaName: "Store2",
          qty: 30,
          value: 1222.5,
          avgCost: 40.75,
          parLevel: 100,
          belowPar: true,
        },
      ],
    },
    {
      locationId: "Outlet2",
      locationName: "Outlet 2",
      totalQty: 100,
      totalValue: 3000,
      avgCost: 30,
      workAreas: [
        {
          workAreaName: "Store1",
          qty: 40,
          value: 1200,
          avgCost: 30,
          parLevel: 50,
          belowPar: true,
        },
        {
          workAreaName: "Store2",
          qty: 30,
          value: 900,
          avgCost: 30,
          parLevel: 50,
          belowPar: true,
        },
        {
          workAreaName: "Store3",
          qty: 20,
          value: 600,
          avgCost: 30,
          parLevel: 50,
          belowPar: true,
        },
        {
          workAreaName: "Store4",
          qty: 10,
          value: 300,
          avgCost: 30,
          parLevel: 10,
          belowPar: false,
        },
      ],
    },
  ],
});

const cardsData = computed(() => {
  return cards.map((card) => {
    return {
      ...card,
      value: stocksData.value[card.key],
    };
  });
});

const toggleExpand = (id) => {
  showWorkAreas.value[id] = !showWorkAreas.value[id];
};
</script>
