<template>
  <v-dialog v-model="model" fullscreen transition="dialog-bottom-transition">
    <v-card>
      <v-toolbar color="white" class="pe-3">
        <v-toolbar-title class="font-weight-bold">
          {{ selectedItem?.itemName }} - Inventory item
        </v-toolbar-title>

        <v-btn
          variant="text"
          icon="mdi-close"
          color="error"
          @click="model = false"
        />
      </v-toolbar>
      <v-divider></v-divider>
      <v-container fluid>
        <v-card class="custom-card">
          <v-tabs
            v-model="tab"
            align-tabs="start"
            color="white"
            hide-slider
            selected-class="selected-chip"
            :fixed-tabs="xs"
          >
            <v-tab :value="1">Stocks</v-tab>
            <v-tab :value="2">GRNS</v-tab>
            <v-tab :value="3">Movements</v-tab>
            <v-tab :value="4">Updates</v-tab>
          </v-tabs>
          <v-divider></v-divider>
          <v-tabs-window v-model="tab">
            <v-tabs-window-item :value="1">
              <stocks-tab></stocks-tab>
            </v-tabs-window-item>
          </v-tabs-window>
        </v-card>
      </v-container>
    </v-card>
  </v-dialog>
</template>
<script setup>
import { ref } from "vue";
import { useDisplay } from "vuetify/lib/framework.mjs";
import StocksTab from "@/components/inventoryItem/StocksTab.vue";

const model = defineModel();
const props = defineProps({
  selectedItem: {
    type: Object,
    default: () => {},
  },
});

const { xs } = useDisplay();
const tab = ref(1);
</script>
