<template>
  <v-card
    class="custom-card"
    rounded="md"
    variant="tonal"
    border="0"
    :color="card.color"
  >
    <v-card-text>
      <div class="d-flex align-center justify-space-between">
        <div>
          <h5 class="text-h5 mb-2">{{ card.title }}</h5>
          <h3 class="text-h3 mb-2">{{ card.value }}</h3>
        </div>
        <v-icon :icon="card.icon" size="89" class="opacity-50"></v-icon>
      </div>
    </v-card-text>
  </v-card>
</template>
<script setup>
defineProps({
  card: {
    type: Object,
    default: () => {},
  },
});
</script>
