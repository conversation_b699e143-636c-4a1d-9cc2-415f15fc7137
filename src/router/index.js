import { createRouter, createWebHashHistory } from "vue-router";
import { getAuthorizationToken, getPrivileges } from "@/helpers/auth";
import { PRIV_CODES } from "@/constants/privilegeCodes";

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/login",
      name: "Login",
      component: () => import("@/components/auth/AppLogin.vue"),
      meta: { auth: false },
    },
    {
      path: "/oAuth/exchange",
      name: "oAuthExchange",
      component: () => import("@/components/auth/OAuthExchange.vue"),
      meta: { auth: false },
    },
    {
      path: "/select-tenant",
      name: "Select Tenant",
      component: () => import("@/views/SelectTenant.vue"),
      meta: { auth: false },
    },
    {
      path: "/not-permitted",
      name: "Access not permitted",
      component: () => import("@/views/NotPermittedView.vue"),
      meta: { auth: false },
    },
    {
      path: "/",
      name: "Home",
      component: () => import("@/views/HomeView.vue"),
      redirect: "/action-center",
      meta: { auth: true },
      children: [
        {
          path: "/action-center",
          name: "action-center",
          component: () => import("@/views/actionCenter/Index.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.DASH_PUR },
        },
        {
          path: "/dashboards/inventory",
          name: "inventory-dashboard",
          component: () => import("@/views/DashboardView.vue"),
          meta: {
            auth: true,
            privilege_code: PRIV_CODES.DASH_INV,
          },
        },
        {
          path: "/dashboards/purchases",
          name: "purchase-dashboard",
          component: () => import("@/views/DashboardView.vue"),
          meta: {
            auth: true,
            privilege_code: PRIV_CODES.DASH_PUR,
          },
        },
        {
          path: "/dashboards/cogs",
          name: "cogs-dashboard",
          component: () => import("@/views/DashboardView.vue"),
          meta: {
            auth: true,
            privilege_code: PRIV_CODES.DASH_COGS,
          },
        },
        {
          path: "/vendors",
          name: "vendors",
          component: () => import("@/views/VendorView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_VIEW },
        },
        {
          path: "/vendors/create",
          name: "Create Vendor",
          component: () => import("@/views/vendor/VendorForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_EDIT },
        },
        {
          path: "/vendors/:id",
          name: "Edit Vendor",
          component: () => import("@/views/vendor/VendorForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_EDIT },
        },
        {
          path: "/categories",
          name: "categories",
          component: () => import("@/views/CategoryView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_VIEW },
        },
        {
          path: "/categories/create",
          name: "Create Category",
          component: () => import("@/views/category/CategoryForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_EDIT },
        },
        {
          path: "/categories/:id",
          name: "Edit Category",
          component: () => import("@/views/category/CategoryForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_EDIT },
        },
        {
          path: "/workArea",
          name: "workArea",
          component: () => import("@/views/WorkArea.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.SET_LOC },
        },
        {
          path: "/house-units",
          name: "houseUnits",
          component: () => import("@/views/HouseUnitView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_VIEW },
        },
        {
          path: "/recipes",
          name: "recipes",
          component: () => import("@/views/ReceipeCreationView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_VIEW },
        },
        {
          path: "/recipes/create",
          name: "Create Recipe",
          component: () => import("@/views/recipe/RecipeForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_EDIT },
        },
        {
          path: "/recipes/:id",
          name: "Edit Recipe",
          component: () => import("@/views/recipe/RecipeForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_EDIT },
        },
        {
          path: "/inventoryItems",
          name: "inventoryItems",
          component: () => import("@/views/InventoryView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_VIEW },
        },
        {
          path: "/inventoryItems/create",
          name: "Create Inventory Item",
          component: () =>
            import("@/views/inventoryItem/inventoryItemForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_EDIT },
        },
        {
          path: "/inventoryItems/:id",
          name: "Edit Inventory Item",
          component: () =>
            import("@/views/inventoryItem/inventoryItemForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_EDIT },
        },
        {
          path: "/import-export",
          name: "Import Export",
          component: () => import("@/views/importExport/ImportExportView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PC_IMPORT },
        },
        {
          path: "/stocks",
          name: "stocks",
          component: () => import("@/views/StockView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.STK_VIEW },
        },
        {
          path: "/stockLog",
          name: "Stock Ledgers",
          component: () => import("@/views/StockLogView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.STK_VIEW },
        },
        {
          path: "/locations",
          name: "locations",
          component: () => import("@/views/LocationView.vue"),
          meta: { auth: true },
        },
        {
          path: "/menuItem",
          name: "Menu Items",
          component: () => import("@/views/MenuView.vue"),
          meta: { auth: true },
        },
        {
          path: "/menuItem/:id",
          name: "Edit Menu Item",
          component: () => import("@/views/menuItem/MenuItemForm.vue"),
          meta: { auth: true },
        },
        {
          path: "/modifier",
          name: "Modifiers",
          component: () => import("@/views/ModifierView.vue"),
          meta: { auth: true },
        },
        {
          path: "/modifier/:id",
          name: "Edit Modifier",
          component: () => import("@/views/modifier/ModifierForm.vue"),
          meta: { auth: true },
        },
        {
          path: "/purchaseRequests",
          name: "Purchase Requests (PR)",
          component: () => import("@/views/PurchaseRequestView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_PR },
        },
        // {
        //   path: "/purchaseRequest/create",
        //   name: "Create Purchase Request (PR)",
        //   component: () =>
        //     import("@/views/purchaseRequest/PurchaseRequestForm.vue"),
        //   meta: { auth: true, privilege_code: PRIV_CODES.PUR_PR },
        // },
        {
          path: "/purchaseRequest/create",
          name: "Create Purchase Request (PR)",
          component: () => import("@/views/purchaseRequest/PRForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_PR },
        },
        {
          path: "/purchaseRequest/:id",
          name: "Edit Purchase Request (PR)",
          component: () => import("@/views/purchaseRequest/PRForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_PR },
        },
        {
          path: "/purchaseOrder",
          name: "Purchase Order (PO)",
          component: () => import("@/views/PurchaseOrderView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_PO },
        },
        {
          path: "/purchaseOrder/:id",
          name: "Edit Purchase Order (PO)",
          component: () =>
            import("@/views/purchaseOrder/PurchaseOrderForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_PO },
        },

        {
          path: "/grn",
          name: "Goods Received Note (GRN)",
          component: () => import("@/views/GrnListView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_GRN },
        },
        {
          path: "/grn/:id",
          name: "Edit Goods Received Note (GRN)",
          component: () => import("@/views/grn/grnForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_GRN },
        },
        {
          path: "/closing",
          name: "closing",
          component: () => import("@/views/closing/ClosingForm.vue")
        },
        {
          path: "/transfers",
          name: "transfers",
          component: () => import("@/views/transfers/TransferList.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_INDENT },
        },
        {
          path: "/transfer/create",
          name: "Create Transfer",
          component: () => import("@/views/transfers/CreateTransferForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_INDENT },
        },
        {
          path: "/transfer/:id/dispatch",
          name: "Dispatch Transfer",
          component: () => import("@/views/transfers/DispatchTransferForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_INDENT },
        },
        {
          path: "/transfer/:id/receive",
          name: "Receive Transfer",
          component: () => import("@/views/transfers/ReceiveTransferForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_INDENT },
        },
        {
          path: "/transfer/:id/view",
          name: "View Transfer",
          component: () => import("@/views/transfers/ViewTransferForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.PUR_INDENT },
        },
        {
          path: "/reports",
          name: "Reports",
          component: () => import("@/views/ReportView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.REP_GRN },
        },
        {
          path: "/roles",
          name: "roles",
          component: () => import("@/views/RoleView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.SET_USER },
        },
        {
          path: "/role-operation",
          name: "Create Role",
          component: () => import("@/views/role/roleForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.SET_USER },
        },
        {
          path: "/role-operation/:id",
          name: "Edit Role",
          component: () => import("@/views/role/roleForm.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.SET_USER },
        },
        {
          path: "/users",
          name: "users",
          component: () => import("@/views/UserView.vue"),
          meta: { auth: true, privilege_code: PRIV_CODES.SET_USER },
        },
        {
          path: "/welcome",
          name: "Welcome",
          component: () => import("@/views/WelcomeView.vue"),
          meta: { auth: true },
        },
        {
          path: "/tags",
          name: "tags",
          component: () => import("@/views/TagView.vue"),
          meta: { auth: true },
        },
      ],
    },
  ],
});

router.beforeEach((to) => {
  const token = getAuthorizationToken();
  const authRequired = to.meta?.auth;
  const privilegeRequired = to.meta?.privilege_code;
  const privileges = getPrivileges();

  if (to.name === "Login" && token) return { name: "dashboard" };

  if (authRequired && !token) return { name: "Login" };

  if (privilegeRequired && !privileges.includes(privilegeRequired)) {
    return { name: "Access not permitted" };
  }

  return true;
});

export default router;
