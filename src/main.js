// import './assets/main.css'

import { createApp } from "vue";
import { createPinia } from "pinia";

import App from "./App.vue";
import router from "./router";
import axiosInstance from "./plugin/Axios";
import Vuetify from "./plugin/Vuetify";

// assets
import "../src/assets/style.css";
import "../src/assets/table.css";
import "@/assets/common-style.scss";
import "@/assets/custom-table.scss";
import "@/assets/draggable-list.scss";

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(Vuetify);

// Register axiosInstance as a global property
app.config.globalProperties.$axios = axiosInstance;
// app.provide('axios', axiosInstance)

app.mount("#app");
