export const unitTypes = [
  { id: "kg", name: "Kilogram (kg)" },
  { id: "g", name: "Gram (g)" },
  { id: "l", name: "<PERSON><PERSON> (l)" },
  { id: "ml", name: "Millilitre (ml)" },
];

export const recipeTypes = [
  { id: "recipe", name: "<PERSON>ci<PERSON>" },
  { id: "subRecipe", name: "Sub Recipe" },
];

export const unitSymbols = [
  { symbol: "kg", quantity: 1 },
  { symbol: "g", quantity: 1000 },
  { symbol: "l", quantity: 1 },
  { symbol: "ml", quantity: 1000 },
];

export const reportType = [
  {
    id: 1,
    title: "Live Stock Store & Workarea",
  },
  {
    id: 2,
    title: "GRN Report",
  },
  {
    id: 3,
    title: "Closing Stock Report",
  },
];

export const reportColumns = [
  {
    id: 1,
    reportType: 1,
    options: [
      "Sl.No",
      "Location",
      "Category",
      "Sub Category",
      "Item Code",
      "Item Name",
      "Unit",
      "Opening Stock",
    ],
  },
  {
    id: 2,
    reportType: 2,
    options: [
      "Sl.No",
      "Category",
      "Sub Category",
      "Item Code",
      "Item Name",
      "Price",
    ],
  },
  {
    id: 3,
    reportType: 3,
    options: [
      "Sl.No",
      "Category",
      "Sub Category",
      "Item Code",
      "Item Name",
      "Count",
    ],
  },
];
