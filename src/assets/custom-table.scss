.table-bordered {
  border-collapse: collapse;
  width: 100%;

  /* Header background */
  th {
    background-color: #fafafa;
  }

  /* Header vertical borders */
  th:not(:last-child) {
    border-right: 1px solid #ededed;
  }

  /* Data cell vertical borders */
  td:not(:last-child) {
    border-right: 1px solid #f5f5f5;
  }

  /* Modifier: striped rows */
  &--striped {
    tbody tr:nth-child(even) {
      background-color: #fbfbfb;

      /* Optional: adjust vertical borders for even rows */
      td:not(:last-child) {
        border-right: 1px solid #ededed;
      }
    }
  }
}
