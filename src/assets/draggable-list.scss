.draggable-list {
  // Handle styling
  .drag-handle {
    cursor: grab;
    color: var(--v-theme-on-surface-variant);

    &:active {
      cursor: grabbing;
    }
  }

  // 🌟 Placeholder while dragging (ghost)
  .drag-ghost {
    opacity: 0.6;
    background-color: rgba(var(--v-theme-primary), 0.1);
    border: 1px dashed var(--v-theme-primary);
    transform: scale(1.02);
  }

  // 🌟 The element being picked up (chosen)
  .drag-chosen {
    background-color: rgba(var(--v-theme-secondary), 0.15);
    border-left: 4px solid var(--v-theme-secondary);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
  }

  // 🌟 While dragging in motion
  .drag-active {
    transition: transform 0.15s ease;
  }
}
