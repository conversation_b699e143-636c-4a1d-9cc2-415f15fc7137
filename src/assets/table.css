.parent-cont {
  border-radius: 5px;
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.2) !important;
}

.parent-cont .v-table thead th {
  background-color: whitesmoke;
  color: #000;
  font-weight: 500;
  /* font-size: 14px !important; */
  text-transform: uppercase !important;
  letter-spacing: 0.5px;

  border: unset !important;
  border-top: 1px solid #f5f5f5 !important;
  height: 50px !important;
}

.parent-cont .v-table tr.group-header {
  background-color: #cfd8dc;
  border: 1px solid #ddd !important;
  color: #000;
  height: 50px;
}

@media (max-width: 1180px) {
  .parent-cont .spacer-elem {
    display: none !important;
  }
}

.parent-cont .v-table tr.group-header td strong {
  font-weight: 500 !important;
  letter-spacing: 1.2px !important;
}

/* header borders */
.parent-cont .v-table thead th:first-child {
  border-left: 1px solid #ede4ed !important;
}

.parent-cont .v-table thead th:not(:last-child) {
  border-right: 1px solid #ede4ed !important;
}

/* group header borders */
.parent-cont .v-table tr.group-header:first-child {
  border-top: 1px solid #ddd !important;
}

.parent-cont .v-table tr.group-header td:not(:last-child) {
  border-right: 1px solid #ddd !important;
}

/* result row borders */
.parent-cont .v-table tr.result-elem td:not(:last-child) {
  border-right: 1px solid #f5f5f5 !important;
}
.parent-cont .v-table tr td:not(:last-child) {
  border-right: 1px solid #f5f5f5 !important;
  /* cursor: pointer !important; */
}

.parent-cont .v-table tr:last-child {
  border-bottom: 1px solid #f5f5f5 !important;
}

/* total row borders */
.parent-cont .v-table tr.total-elem td {
  border-right: 1px solid #f5f5f5 !important;
}

.group-header td:first-child,
.result-elem td:first-child {
  padding: 0 2px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.void-sale td {
  color: red !important;
}

.group-header.default td:first-child,
.result-elem.default td:first-child {
  align-items: left !important;
  justify-content: left !important;
  padding: 0 16px !important;
  display: table-cell !important;
  vertical-align: inherit;
}

.group-header.summary td:first-child {
  padding: 0 24px !important;
  display: table-cell;
  vertical-align: inherit;
}

.parent-cont .v-table tr.group-header.summary {
  background-color: white !important;
  border-bottom: 1px solid #f5f5f5 !important;
}
.parent-cont .v-table tr.group-header.summary:hover {
  background-color: #fff !important;
}

.parent-cont .v-table tr.group-header.summary td {
  border-right: 1px solid #f5f5f5 !important;
  border-left: 1px solid #f5f5f5 !important;
}

.parent-cont .v-table tr.group-header:hover {
  background-color: #cfd8dc !important;
}

.parent-cont .v-table tr.result-elem:hover {
  background-color: white !important;
}

/* modifiers results */
.modifier-row {
  background-color: #d0eaec;
}

.modifier-row td {
  font-size: 12px !important;
  height: 30px !important;
}

.v-table tbody tr.modifier-row:hover:not(.v-datatable__expand-row) {
  background-color: #d0eaec !important;
}
