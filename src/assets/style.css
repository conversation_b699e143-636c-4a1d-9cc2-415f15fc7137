html {
  font-family: "Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Helvetica Neue", Arial, sans-serif;
}
.login-img {
  background-image: url("../assets/img/finalbg.jpg");
}

.custom-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-login-card {
  width: 500px;
  max-width: 90%;
  border-radius: 10px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); */
  background-color: white;
}

.custom-card {
  border-radius: 10px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); */
  background-color: white;
}

.custom-card-title {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.custom-card-text {
  padding: 20px;
}

.custom-card-actions {
  padding-bottom: 20px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .custom-card {
    width: 100%;
  }
}

.clear-btn {
  cursor: pointer;
  color: red;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  text-decoration: underline;
  font-weight: normal !important;
}

.stockswitch {
  position: relative;
  width: 80px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.stockswitch-checkbox {
  display: none;
}

.stockswitch-label {
  display: block;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid #ffffff;
  border-radius: 5px;
  margin-bottom: 0px !important;
}

.stockswitch-inner {
  display: block;
  width: 200%;
  margin-left: -100%;
  transition: margin 0.3s ease-in 0s;
}

.stockswitch-inner:before,
.stockswitch-inner:after {
  display: block;
  float: left;
  width: 50%;
  height: 30px;
  padding: 0;
  line-height: 30px;
  font-size: 12px;
  color: white;
  font-family: Trebuchet, Arial, sans-serif;
  font-weight: bold;
  box-sizing: border-box;
}

.stockswitch-inner:after {
  content: "IN";
  padding-left: 35px;
  background-color: #5da62c;
  color: #ffffff;
}

.stockswitch-inner:before {
  content: "OUT";
  padding-right: 30px;
  background-color: #ed2c29;
  color: #ffffff;
  text-align: right;
}

.stockswitch-switch {
  display: block;
  width: 13px;
  height: 13px;
  margin: 8px;
  background: #ffffff;
  position: absolute;
  top: 2px;
  bottom: 0;
  right: 50px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  transition: all 0.3s ease-in 0s;
}

.stockswitch-checkbox:checked + .stockswitch-label .stockswitch-inner {
  margin-left: 0;
}

.stockswitch-checkbox:checked + .stockswitch-label .stockswitch-switch {
  right: 0px;
}

.filter-title {
  display: flex;
  align-items: center;
  font-weight: 400 !important;
  color: #ff5a10 !important;
}

.shadow {
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2) !important;
  border-radius: 10px !important;
  /* 0 6px 6px -3px rgba(0,0,0,.2),0 10px 14px 1px rgba(0,0,0,.14),0 4px 18px 3px rgba(0,0,0,.12)!important */
}

.dialog-title-background {
  background-color: rgba(255, 90, 16, 0.23);
}
