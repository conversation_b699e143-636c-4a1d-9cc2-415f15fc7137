.login-img {
  background-image: url("../assets/img/finalbg.jpg");
}

.custom-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-login-card {
  width: 500px;
  max-width: 90%;
  border-radius: 10px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); */
  background-color: white;
}

.custom-card {
  border-radius: 10px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); */
  background-color: white;
}

.custom-card-title {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.custom-card-text {
  padding: 20px;
}

.custom-card-actions {
  padding-bottom: 20px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .custom-card {
    width: 100%;
  }
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-fixed {
  position: fixed !important;
}

.position-absolute {
  position: absolute !important;
}

.position-sticky {
  position: sticky !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.btn-radius {
  border-radius: 4px !important;
}
