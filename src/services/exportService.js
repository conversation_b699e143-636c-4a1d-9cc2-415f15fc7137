import axiosInstance from "@/plugin/Axios";
import { getTenantId } from "@/helpers/auth";
import {ref} from "vue"

const tenantId = ref(getTenantId);
const axios = axiosInstance();

const url = `/export-product-configuration`;
const logsUrl = `/import-export-logs`;

import { useLoaderStore } from "../stores/loader";
const loaderStore = useLoaderStore();

export const handleExport = async (isTemplate = false, sheets = []) => {
  loaderStore.showLoader("Downloading... Please wait");
  try {
    const sheetsParam = Array.isArray(sheets) ? sheets.join(",") : sheets;

    const response = await axios.get(
      `${url}?sheets=${sheetsParam}&isTemplate=${isTemplate}`,
      {
        responseType: "blob",
      }
    );

    const blob = new Blob([response.data], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    // Extract filename from headers
    const contentDisposition = response.headers["content-disposition"];
    let filename = "export.xlsx";
    if (contentDisposition) {
      const match = contentDisposition.match(/filename="?([^"]+)"?/);
      if (match && match[1]) {
        filename = match[1];
      }
    }
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = downloadUrl;
    // Get all selected modules
    const selectedModules = Object.keys(sheets).filter((key) => sheets[key]);

    // Determine base name
    let moduleName = "data"; // fallback
    if (selectedModules.length === 1) {
      moduleName = selectedModules[0];
    } else if (selectedModules.length > 1) {
      moduleName = "all";
    }

    // Final filename
    const fileSuffix = isTemplate ? "template" : "export";

    // Add timestamp to filename
    // const now = new Date();
    // const day = String(now.getDate()).padStart(2, "0");
    // const month = String(now.getMonth() + 1).padStart(2, "0");
    // const year = now.getFullYear();
    // const hours = now.getHours();
    // const minutes = String(now.getMinutes()).padStart(2, "0");
    // const ampm = hours >= 12 ? "PM" : "AM";
    // const displayHours = hours % 12 || 12;

    // const timestamp = `${day}-${month}-${year}_${displayHours}_${minutes}_${ampm}`;

    link.download = `${filename}`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error("Export failed:", error);
  } finally {
    loaderStore.hideLoader();
  }
};

export const fetchImportExportLogs = async () => {
  try {
    const response = await axios.get(logsUrl);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch import/export logs:", error);
    return [];
  }
};
