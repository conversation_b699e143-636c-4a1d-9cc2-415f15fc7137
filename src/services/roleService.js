import axiosInstance from "@/plugin/Axios";
import { getTenantId } from "@/helpers/auth";
import { ref } from "vue";

const tenantId = ref(getTenantId);
const axios = axiosInstance();

export const getPrivileges = async () => {
  try {
    const { data } = await axios.get(`/roles/get-privileges`);
    return data;
  } catch (error) {
    console.error("Error fetching privileges:", error);
    throw error;
  }
};

export const createRole = async (data) => {
  return axios
    .post(`/roles`, data)
    .then((res) => res.data)
    .catch((err) => {
      throw err.response?.data || err;
    });
};

export const getRoles = async () => {
  return axios.get(`/roles`).then((res) => res.data);
};

export const getRoleById = async (id) => {
  // If your backend expects /role/:id
  return axios.get(`/roles/get-by-id/${id}`).then((res) => res.data);
};

export const updateRole = async (id, data) => {
  return axios
    .put(`/roles/${id}`, data)
    .then((res) => res.data)
    .catch((err) => {
      throw err.response?.data || err;
    });
};

export const updateRoleActiveStatus = async (id) => {
  try {
    const { data } = await axios.put(`roles/${id}/activateStatus`);
    return data;
  } catch ({ response }) {
    showSnackbar("error", response.data.message);
    throw Error(response.data.message);
  }
};

