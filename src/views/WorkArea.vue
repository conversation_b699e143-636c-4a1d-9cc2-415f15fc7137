<template>
  <div class="work-area-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search WorkAreas"
      add-label="WorkArea"
      hide-import-export
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
      @apply-filters="applyFilters"
    >
      <template #filter>
        <v-autocomplete
          min-width="300"
          v-if="!fromStore"
          v-model="selectedStore"
          :items="stores"
          item-value="id"
          item-title="name"
          label="Location"
          hide-details
          variant="outlined"
          density="compact"
          color="primary"
          clearable
          @update:model-value="handleStoreChange"
        ></v-autocomplete>
      </template>
    </list-actions-bar>

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card class="border rounded-lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No locations found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span v-if="item.isDefault">
                {{ item.name }} &nbsp;
                <v-chip label size="small" color="primary">DEFAULT</v-chip>
              </span>

              <span
                v-else
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
              >{{ item.name }}</span>
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Work/Storage Area"
                :name="item.name"
                @toggle="toggleActivate(item)"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <location-dialog
      v-model="dialog"
      :isEdit="isEdit"
      :fromStore="fromStore"
      :locationData="editLocationData"
      :storeId="selectedStore"
      :stores="stores"
      :tags="tags"
      @createLocation="handleCreateLocation"
      @updateLocation="handleUpdateLocation"
    />
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRoute } from "vue-router";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { tableHeaders } from "@/helpers/tableHeaders";
import { useLocationStore } from "@/stores/location";
import { useStoreStore } from "@/stores/store";
import { filterData } from "@/helpers/searchFilter";
import LocationDialog from "@/components/LocationDialog.vue";
import { useTagStore } from "@/stores/tag";

const locationStore = useLocationStore();
const storeStore = useStoreStore();
const route = useRoute();
const tagStore = useTagStore();

const search = ref("");
const loading = ref(false);
const selectedStore = ref(null);

const headers = ref(tableHeaders["workArea"]);
const sortBy = ref([{ key: "name", order: "asc" }]);

const dialog = ref(false);
const isEdit = ref(false);

const props = defineProps({
  storeId: { type: String, default: null },
  fromStore: { type: Boolean, default: false }
});

const selectedStatus = ref(null);

const selectStatus = status => {
  selectedStatus.value = status ?? null;
};

const stores = computed(() => storeStore.getStores.filter(s => s.activeStatus));

const tags = computed(() =>
  tagStore.getTags
    .filter(tag => tag.activeStatus)
    .map(tag => ({ id: tag.id, name: tag.name }))
);

const items = computed(() => locationStore.getLocations);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  if (selectedStatus.value !== null) {
    result = result.filter(item => item.activeStatus === selectedStatus.value);
  }
  return result;
});

const applyFilters = filters => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders.setWorkArea(filters.headers);
  }
};

const filteredHeaders = computed(() => headers.value.filter(h => h.default));

const handleSearch = query => {
  search.value = query;
};

const editLocationData = ref(null);

const add = () => {
  dialog.value = true;
  isEdit.value = false;
  editLocationData.value = null;
};

const edit = async id => {
  try {
    const location = await locationStore.getLocationById(id);
    editLocationData.value = location;
    dialog.value = true;
    isEdit.value = true;
  } catch (error) {
    console.error(error);
  }
};

const handleCreateLocation = async (location, done) => {
  try {
    await locationStore.createLocation(location);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const handleUpdateLocation = async (location, done) => {
  try {
    await locationStore.updateLocation(location);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const toggleActivate = async ({ id }) => {
  try {
    await locationStore.updateLocationActiveStatus(id, selectedStore.value);
  } catch (error) {
    console.error(error);
  }
};

const handleStoreChange = async v => {
  loading.value = true;
  await locationStore.fetchLocationsByStore(v);
  loading.value = false;
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    handleStoreChange(selectedStore.value);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  await storeStore.fetchStores();
  await tagStore.fetchTags();

  if (props.fromStore && props.storeId) {
    selectedStore.value = props.storeId;
  } else {
    const storeId = route.query.storeId;
    if (storeId && stores.value.some(s => s.id === storeId)) {
      selectedStore.value = storeId;
    } else {
      selectedStore.value = stores.value[0]?.id;
    }
  }

  handleStoreChange(selectedStore.value);
});
</script>

<style scoped>
</style>
