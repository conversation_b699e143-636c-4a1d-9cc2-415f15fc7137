<template>
  <filter-bar searchLabel="reports data" @search="handleSearch">
    <template v-slot:filter>
      <v-autocomplete
        v-model="selectedStore"
        :items="reportType"
        item-value="id"
        item-title="title"
        label="Report Type"
        hide-details
        variant="outlined"
        density="compact"
        color="primary"
        @update:modelValue="handleStoreChange"
        clearable
      ></v-autocomplete>
    </template>

    <v-btn variant="tonal" color="primary" @click="add" class="mx-3">
      Filters
    </v-btn>
    <v-btn color="primary" variant="tonal" @click="refresh">
      <v-icon icon="mdi-reload"></v-icon>
    </v-btn>
  </filter-bar>
  <report-filter-form
    v-if="dialog"
    v-model="dialog"
    :record="{ reportType: selectedStore, unitType: '', conversions: [] }"
    :unit-conversions="unitConversions"
    :title="isEditing ? 'Edit' : 'Create'"
    :tenant-id="tenantId"
    @handle-save="save"
  ></report-filter-form>
</template>

<script setup>
import { ref, watch } from "vue";
import FilterBar from "@/components/base/FilterBar.vue";
import { reportType } from "@/constants/list";
import ReportFilterForm from "@/views/reports/ReportFilterForm.vue";

const selectedStore = ref(null);
const dialog = ref(false);

const handleStoreChange = (value) => {
  console.log("Changed store:", value);
};

const add = () => {
  dialog.value = true;
};

watch(selectedStore, (val) => {
  console.log("v-model changed:", val);
});
</script>
