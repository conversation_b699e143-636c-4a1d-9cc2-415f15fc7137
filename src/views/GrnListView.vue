<template>
  <div>
    <filter-bar searchLabel="GRN No." @search="handleSearch">
      <v-btn variant="tonal" color="primary" @click="toggleFilter" class="mx-3">
        <v-icon icon="mdi-filter-variant" />
      </v-btn>
      <v-btn color="primary" variant="tonal" @click="refresh">
        <v-icon icon="mdi-reload"></v-icon
      ></v-btn>
    </filter-bar>
    <v-container fluid>
      <v-row>
        <v-card class="parent-cont ma-3 mb-0" width="100%">
          <v-container fluid grid-list-lg class="pa-0">
            <v-data-table
              :headers="grnListHeaders"
              :items="filteredItems"
              class="custom-table"
              :loading="loading"
              :hide-default-footer="filteredItems.length < 11"
              :no-data-text="loading ? 'Loading GRN...' : 'No Grn found'"
            >
              <template v-slot:item="{ item }">
                <tr class="result-elem default">
                  <td v-for="(header, ind) in grnListHeaders" :key="header.key">
                    <span
                      v-if="ind == 0"
                      class="text-decoration-underline font-weight-bold cursor-pointer"
                      @click="edit(item.id)"
                      >{{ item[header.key] || "-" }}</span
                    >
                    <!-- <span
                      v-else-if="header.key === 'status'"
                      :class="`d-flex ga-2 justify-${header.align}`"
                    >
                      <v-chip
                        class="text-uppercase"
                        label
                        :color="item[header.key] == 'approved' ? 'green' : ''"
                      >
                        {{ item[header.key] }}
                      </v-chip></span
                    > -->
                    <span
                      v-else-if="header.key === 'goodsReceivedDate'"
                      :class="`d-flex ga-2 justify-${header.align}`"
                      >{{ ConvertIOStoDate(item[header.key]) }}</span
                    >
                    <span
                      v-else-if="header.key === 'totalPrice'"
                      :class="`d-flex ga-2 justify-${header.align}`"
                      >{{ item[header.key] }}</span
                    >
                    <span
                      v-else-if="header.key === 'vendorInvoiceDate'"
                      :class="`d-flex ga-2 justify-${header.align}`"
                      >{{ ConvertIOStoDate(item[header.key]) }}</span
                    >

                    <span
                      v-else
                      :class="`d-flex ga-2 justify-${header.align}`"
                      >{{
                        typeof item[header.key] == "object"
                          ? item[header.key].name ?? "-"
                          : item[header.key] ?? "-"
                      }}</span
                    >
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-container>
        </v-card>
      </v-row>
    </v-container>
    <filter-nav-drawer
      ref="editFilter"
      :tabs="tabs"
      :filtersData="filters"
      @apply-filter="applyFilters"
    ></filter-nav-drawer>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import FilterBar from "@/components/base/FilterBar.vue";
import FilterNavDrawer from "@/components/filters/navDrawer.vue";
import AutoComplete from "@/components/filters/Autocomplete.vue";
import { grnListHeaders } from "@/helpers/tableHeaders";

import { useRouter } from "vue-router";
import { useLocationStore } from "@/stores/location";
import { useStoreStore } from "@/stores/store";
import { useGrnStore } from "@/stores/goodsReceivedNote";
import { ConvertIOStoDate } from "@/helpers/date";
const router = useRouter();
const grnStore = useGrnStore();
const locationStore = useStoreStore();
const workAreaStore = useLocationStore();

const locations = computed(() => locationStore.getStores || []);
const workAreas = computed(() => workAreaStore.getLocations || []);

const search = ref(null);
const loading = ref(false);

const items = computed(() => grnStore.getGrnList || []);

const dataFilters = ref({});
const tabs = [{ value: 1, label: "filters" }];
const filters = computed(() => [
  {
    component: AutoComplete,
    title: "Location",
    key: "locations",
    items: locations.value,
    default: true,
  },
  {
    component: AutoComplete,
    title: "WorkArea/Storage",
    key: "inventoryLocations",
    items: workAreas.value,
  },
]);

const applyFilters = (filters) => {
  dataFilters.value = filters;
  refresh();
};

const filteredItems = computed(() => {
  if (!search.value) return items.value;
  const query = search.value.toLowerCase();
  return items.value.filter((item) =>
    item.grnNumber.toLowerCase().includes(query)
  );
});
const handleSearch = (v) => {
  search.value = v;
};

const edit = (id) => {
  router.push({ name: "Edit Goods Received Note (GRN)", params: { id } });
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await grnStore.fetchGrnList(dataFilters.value);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const editFilter = ref(null);
const toggleFilter = () => {
  editFilter.value.toggle();
};

onBeforeMount(async () => {
  try {
    loading.value = true;
    await Promise.all([
      locationStore.fetchStores(),
      workAreaStore.fetchLocations(),
    ]);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
});
</script>
