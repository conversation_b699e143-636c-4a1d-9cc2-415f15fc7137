<template>
  <v-container
    fluid
    class="d-flex justify-center align-center"
    style="height: 100vh; padding: 0"
  >
    <v-row v-if="isLoading">
      <v-col class="text-center">
        <v-progress-circular color="primary" indeterminate size="70" />
      </v-col>
    </v-row>
    <v-row v-else justify="center">
      <v-col cols="12" sm="8" md="4">
        <v-card variant="flat">
          <v-card-title class="py-2 text-center text-wrap">
            Choose Tenant
          </v-card-title>
          <v-card-text class="pa-6">
            <v-row>
              <template v-for="(tenant, idx) in tenants" :key="tenant.id">
                <v-col cols="12" class="pa-2">
                  <v-card
                    class="cursor-pointer custom-card tenant-card"
                    @click="selectTenant(idx)"
                  >
                    <v-card-text>
                      <div class="d-flex justify-space-between align-center">
                        <div>
                          <div class="font-weight-medium">
                            {{ tenant.name }}
                          </div>
                        </div>
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>
              </template>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  setTenantId,
  getTenants,
  setPrivileges,
  setUser,
} from "@/helpers/auth";

const router = useRouter();
const route = useRoute();
const tenants = getTenants();

const isLoading = ref(false);

// Methods
function selectTenant(idx) {
  isLoading.value = true;
  const tenant = tenants[idx];

  setTenantId(tenant.id);
  setPrivileges(tenant.privileges);
  setUser(tenant);

  const userPrivileges = tenant.privileges || [];
  const path = route.query.path;

  if (path) {
    router.push(path);
  } else if (userPrivileges.includes("DASH_PUR")) {
    router.push("/");
  } else {
    router.push("/welcome");
  }
}
</script>

<style scoped>
.tenant-card:hover {
  background-color: #ff5a10 !important; /* override grey */
  color: white !important; /* optional: change text color */
}
</style>
