<template>
  <div class="recipe-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search Recipes"
      add-label="Recipe"
      sheets="Recipes"
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
      @apply-filters="applyFilters"
    />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card class="border rounded-lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No recipes found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
              >{{ item.name }}</span>
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Recipe"
                :name="item.name"
                @toggle="toggleActivate(item)"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { useReceipeStore } from "@/stores/receipe";
import { tableHeaders } from "@/helpers/tableHeaders";
import { filterData } from "@/helpers/searchFilter";

const recipeStore = useReceipeStore();
const router = useRouter();

const search = ref(null);
const loading = ref(false);
const headers = ref(tableHeaders["recipes"]);
const sortBy = ref([{ key: "name", order: "asc" }]);

const selectedStatus = ref(null);
const selectedRecipeType = ref(null);

const selectStatus = status => {
  selectedStatus.value = status ?? null;
};

const selectRecipeType = recipeType => {
  selectedRecipeType.value = recipeType ?? null;
};

const items = computed(() => recipeStore.getReceipes || []);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  if (selectedStatus.value !== null) {
    result = result.filter(item => item.activeStatus === selectedStatus.value);
  }
  if (selectedRecipeType.value !== null) {
    result = result.filter(
      item => item.recipeType === selectedRecipeType.value
    );
  }
  return result;
});

const applyFilters = filters => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);
  if (filters.recipeType !== undefined)
    selectRecipeType(filters.recipeType ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders["setRecipes"](filters.headers);
  }
};

const filteredHeaders = computed(() => headers.value.filter(h => h.default));

const add = () => {
  router.push({ name: "Create Recipe" });
};

const edit = id => {
  router.push({ name: "Edit Recipe", params: { id } });
};

const toggleActivate = async ({ id }) => {
  try {
    await recipeStore.updateRecipeActiveStatus(id);
  } catch (error) {
    console.error(error);
  }
};

const handleSearch = v => {
  search.value = v;
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await recipeStore.fetchReceipes();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(() => {
  refresh();
});
</script>

<style scoped>
</style>
