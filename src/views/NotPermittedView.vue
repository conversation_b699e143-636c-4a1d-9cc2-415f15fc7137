<template>
  <div class="d-flex justify-center align-center full-height">
    <div class="text-center">
      <img
        :src="logo"
        alt="Digitory Logo"
        class="img-fluid mb-1"
        style="max-width: 300px;"
      />

      <h1 class="fw-bold mb-4">
        You don’t have access to this page
      </h1>

      <v-btn
        :loading="loading"
        :disabled="loading"
        color="primary"
        @click="goHome"
      >
        Go Back to Home Page
      </v-btn>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import logo from "@/assets/digitory/Light_Transparent.png";

const router = useRouter();
const loading = ref(false);

const goHome = async () => {
  loading.value = true;
  try {
    await router.push("/welcome");
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.full-height {
  height: 100vh;
}
</style>
