<template>
  <filter-bar @search="handleSearch">
    <v-btn variant="tonal" color="primary" @click="toggleFilter" class="mx-3">
      <v-icon icon="mdi-filter-variant" />
    </v-btn>
    <v-btn variant="tonal" color="primary" @click="refresh">
      <v-icon icon="mdi-reload" />
    </v-btn>
  </filter-bar>

  <v-container fluid>
    <v-row>
      <v-card class="parent-cont ma-3" width="100%">
        <v-container fluid class="pa-0">
          <v-data-table
            :headers="stockHeaders"
            :items="items"
            class="custom-table"
            :loading="loading"
            :hide-default-footer="items.length < 11"
            :no-data-text="loading ? 'Loading items...' : 'No stocks found'"
          >
          </v-data-table>
        </v-container>
      </v-card>
    </v-row>
  </v-container>
  <!-- <prepare-dialog v-model="prepareDialog" :location="selectedLocation" /> -->
  <filter-nav-drawer
    ref="editFilter"
    :tabs="tabs"
    :filtersData="filters"
    @apply-filter="applyFilters"
  ></filter-nav-drawer>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useStockStore } from "@/stores/stock";
import { useLocationStore } from "@/stores/location";
import { useStoreStore } from "@/stores/store";
import { useCategoryStore } from "@/stores/category";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { stockHeaders } from "@/helpers/tableHeaders";

// import PrepareDialog from "@/components/PrepareDialog.vue";
import FilterBar from "@/components/base/FilterBar.vue";
import FilterNavDrawer from "@/components/filters/navDrawer.vue";
import AutoComplete from "@/components/filters/Autocomplete.vue";

const stockStore = useStockStore();
const workAreaStore = useLocationStore();
const locationStore = useStoreStore();
const categoryStore = useCategoryStore();
const inventoryItemStore = useInventoryItemStore();

const locations = computed(() => locationStore.getStores || []);
const workAreas = computed(() => workAreaStore.getLocations || []);
const categories = computed(() => categoryStore.getCategories || []);
const subCategories = computed(() =>
  categories.value.flatMap((c) => c.subCategories)
);
const inventoryItems = computed(
  () =>
    inventoryItemStore.getInventoryItems.map((i) => ({
      id: i.id,
      name: i.itemName,
      code: i.itemCode,
    })) || []
);

const loading = ref(false);
const dataFilters = ref({});

const tabs = [{ value: 1, label: "filters" }];

const filters = computed(() => [
  {
    component: AutoComplete,
    title: "Location",
    key: "locations",
    items: locations.value,
    default: true,
  },
  {
    component: AutoComplete,
    title: "WorkArea/Storage",
    key: "inventoryLocations",
    items: workAreas.value,
  },
  {
    component: AutoComplete,
    title: "Categories",
    key: "categories",
    items: categories.value,
  },
  {
    component: AutoComplete,
    title: "Subcategories",
    key: "subCategories",
    items: subCategories.value,
  },
  {
    component: AutoComplete,
    title: "Inventory Items",
    key: "items",
    items: inventoryItems.value,
  },
]);

// const prepareDialog = ref(false);

// const openPrepareDialog = () => {
//   prepareDialog.value = true;
// };

// const handleSubmit = (data) => {
//   // perform credit/debit action here
//   const tenantId = localStorage.getItem("_tenantId");
//   const payload = {
//     tenantId,
//     inventoryLocation: {
//       id: selectedLocation.value.id,
//       name: selectedLocation.value.name,
//     },
//     inventoryItem: {
//       id: data.itemId,
//       name: data.itemName,
//       code: data.itemCode,
//       purchaseUnit: data.purchaseUnit,
//       countingUnit: data.countingUnit,
//       recipeUnit: data.recipeUnit,
//     },
//     quantity: data.quantity,
//     uom: data.uom,
//   };

//   if (data.type == "Receive") stockStore.purchaseStocks(payload);
//   else stockStore.consumeStocks(payload);
// };

const items = computed(() => stockStore.getStocks || []);

const refresh = async () => {
  try {
    loading.value = true;
    await stockStore.fetchStocks(dataFilters.value);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const applyFilters = (filters) => {
  dataFilters.value = filters;
  refresh();
};

const handleSearch = (v) => {
  search.value = v;
};

const editFilter = ref(null);
const toggleFilter = () => {
  editFilter.value.toggle();
};

onBeforeMount(async () => {
  try {
    loading.value = true;
    await Promise.all([
      locationStore.fetchStores(),
      workAreaStore.fetchLocations(),
      categoryStore.fetchCategories(),
      inventoryItemStore.fetchInventoryItems(),
    ]);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
});
</script>
