<template>
  <MenuItemTable
    :record="record"
    :flattenedItems="flattenedItems"
    :inventoryList="inventoryList"
    :recipeList="recipeList"
    :saveHandler="save"
    :loader="loader"
    routeName="Menu Items"
  />
</template>

<script setup>
import { ref, computed, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMenuItemStore } from '@/stores/menuItem';
import { useInventoryItemStore } from '@/stores/inventoryItem';
import { useReceipeStore } from '@/stores/receipe';
import { menuItemRecord as DEFAULT_RECORD } from '@/helpers/defaultRecords';
import MenuItemTable from '@/components/MenuItemTable.vue';

const route = useRoute();
const router = useRouter();
const menuItemStore = useMenuItemStore();
const inventoryItemStore = useInventoryItemStore();
const receipeStore = useReceipeStore();

const menuItemId = route.params.id;
const accountId = route.query.accountId;
const isEdit = !!menuItemId && !!accountId;

const loader = ref(false);
const record = ref({ ...DEFAULT_RECORD });
const flattenedItems = ref([]);

const inventoryList = computed(() => inventoryItemStore.getInventoryItems || []);
const recipeList = computed(() => receipeStore.getReceipes || []);

onBeforeMount(async () => {
  loader.value = true;
  try {
    await Promise.all([
      inventoryItemStore.fetchInventoryItems(),
      receipeStore.fetchReceipes(),
    ]);

    if (isEdit) {
      const result = await menuItemStore.fetchMenuItemById(menuItemId,accountId);
      record.value = { ...result };

      const type = route.query.createdItemType;
      const id = route.query.createdItemId;

      if (type === 'Inventory' && id) {
        const exists = inventoryList.value.find(i => i.id === id);
        if (!exists) {
          const newItem = await inventoryItemStore.fetchInventoryItemById(id);
          inventoryList.value.push(newItem);
        }
      } else if (type === 'Recipe' && id) {
        const exists = recipeList.value.find(r => r.id === id);
        if (!exists) {
          const newRecipe = await receipeStore.fetchReceipeById(id);
          recipeList.value.push(newRecipe);
        }
      }

      flattenedItems.value = [{
        itemName: result.itemName,
        itemType: result.itemType,
        selectedItemType: type,
        selectedItemName: id,
        servingLevels: result.servingLevels?.map(l => ({
          servingSizeId: l.servingSizeId,
          servingSizeName: l.servingSizeName,
        })) || [],
      }];
      if (type && id) {
        menuItemStore.updateRecipeUnit(flattenedItems.value[0], inventoryList.value, recipeList.value);
      }
    }
  } catch (err) {
    console.error(err);
  } finally {
    loader.value = false;
  }
});

const save = async () => {
  const item = flattenedItems.value[0];
  let name = '', code = '';

  if (item.selectedItemType === 'Inventory') {
    const i = inventoryList.value.find(it => it.id === item.selectedItemName);
    name = i?.itemName;
    code = i?.itemCode;
  } else if (item.selectedItemType === 'Recipe') {
    const r = recipeList.value.find(it => it.id === item.selectedItemName);
    name = r?.name;
    code = r?.code;
  }

  const payload = {
    id: record.value.id,
    activeStatus: record.value.activeStatus,
    account: record.value.account,
    itemName: record.value.itemName,
    itemCode: record.value.itemCode,
    item: name,
    code: code,
    itemType: item.selectedItemType,
    servingLevels: item.servingLevels?.map(v => ({
      servingSizeId: v.servingSizeId,
      servingSizeName: v.servingSizeName,
      qty: v.qty || '',
      recipeUnit: v.recipeUnit || '',
    })),
  };

  if (isEdit) await menuItemStore.updateMenuItem(payload);
  router.push({ name: 'Menu Items' });
};
</script>
