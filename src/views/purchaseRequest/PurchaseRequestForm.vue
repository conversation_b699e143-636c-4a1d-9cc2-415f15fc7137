<template>
  <v-container v-if="!loader" fluid class="pt-2">
    <v-card-actions class="px-4 pb-4">
      <v-spacer />
      <v-btn @click="navigatePrevious" variant="outlined" color="primary"
        ><v-icon>mdi-close</v-icon>Close</v-btn
      >
      <v-btn
        v-if="!isApproved && !isClosed"
        :text="`${isEdit ? 'Update' : 'Create'} PR`"
        @click="submit('PR')"
        variant="flat"
        color="primary"
      />
      <v-btn
        v-if="isEdit && !isApproved && !isClosed"
        :text="'Approve PR'"
        @click="submitApproval"
        variant="flat"
        color="primary"
      />
      <v-btn
        v-if="isEdit && !isApproved && !isClosed"
        :text="'Reject PR'"
        variant="flat"
        color="primary"
        @click="reasonDialog = true"
      />
      <v-btn
        v-if="!isClosed && (!isApprovalNeeded || isApproved)"
        text="Create PO"
        @click="submit('PO')"
        variant="flat"
        color="primary"
      />
    </v-card-actions>
    <v-divider />

    <v-card-text class="pt-4 scrollable-content">
      <v-form ref="form">
        <v-row class="d-flex align-center">
          <v-col v-if="isEdit" cols="12" sm="6" md="3">
            <v-text-field
              v-model="record.prNumber"
              label="PR Number"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              readonly
              tabindex="1"
            ></v-text-field>
          </v-col>

          <v-col cols="12" sm="6" md="3">
            <v-autocomplete
              ref="requesterRef"
              v-model="record.location"
              :items="locations"
              item-value="id"
              item-title="name"
              label="Requester"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              return-object
              :rules="[rules.require]"
              :readonly="isApproved"
              clearable
              :clear-icon-props="{ tabindex: -1 }"
              tabindex="1"
            ></v-autocomplete>
          </v-col>

          <v-col cols="12" sm="6" md="3">
            <v-autocomplete
              ref="vendorTypeRef"
              v-model="record.vendorType"
              :items="[
                { id: 1, name: 'All vendors' },
                { id: 2, name: 'Specific vendor' }
              ]"
              item-value="id"
              item-title="name"
              label="Vendor Type"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
              :readonly="isApproved"
              @update:model-value="onVendorTypeChange"
              clearable
              :clear-icon-props="{ tabindex: -1 }"
              tabindex="2"
            ></v-autocomplete>
          </v-col>
          <v-col v-if="record.vendorType == 2" cols="12" sm="6" md="3">
            <v-autocomplete
              v-model="record.vendor"
              :items="vendors"
              item-value="id"
              item-title="name"
              label="Vendor"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              return-object
              :rules="[rules.require]"
              :readonly="isApproved"
              clearable
              tabindex="2"
            ></v-autocomplete>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-date-input
              v-model="record.deliveryDate"
              label="Delivery Date"
              color="primary"
              :readonly="isApproved"
              :min="today"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon=""
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              tabindex="3"
              hide-actions
            ></v-date-input>
          </v-col>
          <v-divider></v-divider>

          <v-col v-if="!isClosed" cols="12">
            <RequestItemtable
              v-model="record.items"
              :inventory-list="filteredInventoryItems"
              :vendor-list="vendors"
              :selectedVendor="record.vendor"
              :view-only="isApproved"
            >
            </RequestItemtable>
          </v-col>
          <v-col v-else>
            <ClosedPurchaseItemTable
              :items="record.items"
              :headers="closedRequestItemHeaders"
            ></ClosedPurchaseItemTable>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>

    <ReasonDialog
      v-if="reasonDialog"
      v-model="reasonDialog"
      @reject="reject"
    ></ReasonDialog>
  </v-container>
  <v-container
    v-else
    class="d-flex justify-center align-center"
    style="height: 100%"
  >
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate></v-progress-circular>
    </div>
  </v-container>
</template>

<script setup>
import {
  onBeforeMount,
  ref,
  computed,
  onMounted,
  nextTick,
  watch,
  onBeforeUnmount
} from "vue";
import { closedRequestItemHeaders } from "@/helpers/tableHeaders";
import { purchaseRequestRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";

import { useLocationStore } from "@/stores/location";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useVendorStore } from "@/stores/vendor";
import { usePurchaseRequestStore } from "@/stores/purchaseRequest";
import RequestItemtable from "@/components/purchase/RequestItemtable.vue";
import ReasonDialog from "@/components/base/ReasonDialog.vue";
import ClosedPurchaseItemTable from "@/components/purchase/closedPurchaseItemTable.vue";
import { handleGlobalTab } from "@/helpers/tabFlow";
import { getUser } from "@/helpers/auth";

const requesterRef = ref(null);

const focusRequester = () => {
  requesterRef.value?.focus();
};

onMounted(async () => {
  await nextTick();
  focusRequester(); // Focus on mount
  window.addEventListener("keydown", handleGlobalTab);
});

const locationStore = useLocationStore();
const inventoryStore = useInventoryItemStore();
const vendorStore = useVendorStore();
const purchaseRequestStore = usePurchaseRequestStore();

const router = useRouter();
const route = useRoute();
const form = ref(null);
const reasonDialog = ref(false);

const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

const purchaseRequestId = route.params.id;
const isEdit = purchaseRequestId !== undefined;
const loader = ref(false);
const today = new Date().toISOString().split("T")[0];
const user = ref(getUser());

const isApprovalNeeded = ref(
  JSON.parse(localStorage.getItem("approvalNeeded"))
);

const vendors = computed(() => vendorStore.getVendors || []);
const inventoryItems = computed(() => inventoryStore.getInventoryItems || []);
const isApproved = computed(() =>
  record.value.statusTimeline?.some((item) => item.name === "approved")
);

const isClosed = computed(() =>
  record.value.statusTimeline?.some(
    (item) => item.name === "rejected" || item.name == "completed"
  )
);

const filteredInventoryItems = computed(() => {
  const selectedIds = record.value.items.map((i) => i.itemId);
  return inventoryItems.value.filter((item) => !selectedIds.includes(item.id));
});

const selectedLocation = computed(() => locationStore.getSelectedLocation);
const locations = computed(() => locationStore.getLocations);

const onVendorTypeChange = (value) => {
  record.value.vendorType = value;
  if (value === 1) record.value.vendor = null;
};

const submit = async (key) => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  let location;
  let inventoryLocation;

  if (isEdit) {
    location = {
      id: record.value.location.id,
      name: record.value.location.name
    };
    inventoryLocation = {
      id: record.value.inventoryLocation.id,
      name: record.value.inventoryLocation.name
    };
  } else {
    const store = record.value.location?.store || record.value.location;

    location = {
      id: store.id,
      name: store.name
    };

    inventoryLocation = {
      id: record.value.location?.id,
      name: record.value.location?.name
    };
  }

  const payload = {
    ...record.value,
    location: location,
    inventoryLocation: inventoryLocation,
    items: record.value.items.map((val) => ({
      ...val,
      vendor: { id: val.vendor.id, name: val.vendor.name }
    }))
  };
  delete payload.prNumber;
  delete payload.vendor;
  delete payload.updatedTime;

  if (record.value.id) {
    payload.id = record.value.id;
    payload.prNumber = record.value.prNumber;
  }

  if (key == "PR") {
    if (isEdit) await purchaseRequestStore.updatePurchaseRequest(payload);
    else await purchaseRequestStore.createPurchaseRequest(payload);
    navigatePrevious();
  } else {
    await purchaseRequestStore.createPurchaseRequestAndOrder(payload);
    router.push({ name: "Purchase Order (PO)" });
  }
};

const submitApproval = async () => {
  const payload = {
    approvedBy: {
      name: user.value.userName,
      id: user.value.userId
    },
    id: record.value.id
  };
  await purchaseRequestStore.approvePurchaseRequest(payload);
  navigatePrevious();
};

const handleClear = () => {
  if (!isApproved) record.value.deliveryDate = null;
};

const navigatePrevious = () => {
  router.push({ name: "Purchase Requests (PR)" });
};

const reject = async (reason) => {
  const payload = {
    id: record.value.id,
    rejectedReason: reason.value,
    approvedBy: {
      name: user.value.userName,
      id: user.value.userId
    }
  };
  await purchaseRequestStore.rejectPurchaseRequest(payload);
  navigatePrevious();
};
onBeforeUnmount(() => {
  window.removeEventListener("keydown", handleGlobalTab);
});
onBeforeMount(async () => {
  loader.value = true;

  try {
    await Promise.all([
      locationStore.fetchLocations(),
      vendorStore.fetchVendors(),
      inventoryStore.fetchInventoryItems()
    ]);

    if (isEdit) {
      const result = await purchaseRequestStore.fetchPurchaseRequestById(
        purchaseRequestId
      );
      record.value = result;
      record.value.deliveryDate = new Date(result.deliveryDate);
      onVendorTypeChange(record.value.vendorType);
      if (record.value.vendorType == 2)
        record.value.vendor = record.value.items[0].vendor;
    } else {
      if (selectedLocation.value) {
        record.value.location = selectedLocation.value;
      }
    }

    loader.value = false;
  } catch (err) {
    console.error(err);
  } finally {
  }
});
watch(loader, async (val) => {
  if (!val) {
    await nextTick();
    requesterRef.value?.focus();
  }
});
</script>

<style>
.scrollable-content {
  overflow-y: auto;
  max-height: calc(100vh - 150px); /* Adjust if needed */
  padding-right: 16px; /* avoid scrollbar overlap */
}
</style>
