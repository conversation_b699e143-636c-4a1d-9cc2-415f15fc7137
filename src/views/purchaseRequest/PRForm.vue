<template>
  <div style="height: 100%">
    <!-- Form Actions Bar -->
    <form-actions-bar
      v-if="!loading"
      @close="navigatePrevious"
      :hide-submit="true"
      :loading="loading"
    >
      <template #actions>
        <v-btn
          v-if="!isApproved && !isClosed"
          :text="`${isEdit ? 'Update' : 'Create'} PR`"
          @click="submitPR"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />
        <v-btn
          v-if="isEdit && !isApproved && !isClosed"
          :text="'Approve PR'"
          @click="submitApproval"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />
        <v-btn
          v-if="isEdit && !isApproved && !isClosed"
          :text="'Reject PR'"
          variant="flat"
          color="primary"
          @click="reasonDialog = true"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />
        <v-btn
          v-if="!isClosed && (!isApprovalNeeded || isApproved)"
          text="Create PO"
          @click="submitPO"
          variant="flat"
          color="primary"
          class="ml-2"
          :loading="loading"
          :disabled="loading"
        />
      </template>
    </form-actions-bar>
    <v-container v-if="!loading" fluid>
      <v-card flat>
        <v-card-text class="my-2 pa-0 scrollable-content">
          <v-form ref="form">
            <v-row>
              <v-col v-if="isEdit" cols="12" sm="6" md="3">
                <v-text-field
                  v-model="record.prNumber"
                  label="PR Number"
                  hide-details="auto"
                  variant="outlined"
                  density="compact"
                  color="primary"
                  readonly
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-autocomplete
                  v-model="record.location"
                  :items="locations"
                  item-value="id"
                  item-title="name"
                  label="Requester"
                  hide-details="auto"
                  variant="outlined"
                  density="compact"
                  color="primary"
                  return-object
                  :rules="[rules.require]"
                  :readonly="isApproved"
                  clearable
                  hint="Location"
                  persistent-hint=""
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-autocomplete
                  v-model="record.vendorType"
                  :items="[
                    { id: 1, name: 'All vendors' },
                    { id: 2, name: 'Specific vendor' },
                  ]"
                  item-value="id"
                  item-title="name"
                  label="Vendor Type"
                  hide-details="auto"
                  variant="outlined"
                  density="compact"
                  color="primary"
                  :rules="[rules.require]"
                  :readonly="isApproved"
                  @update:model-value="onVendorTypeChange"
                  clearable
                ></v-autocomplete>
              </v-col>
              <v-col v-if="record.vendorType == 2" cols="12" sm="6" md="3">
                <v-autocomplete
                  v-model="record.vendor"
                  :items="vendors"
                  item-value="id"
                  item-title="name"
                  label="Vendor"
                  hide-details="auto"
                  variant="outlined"
                  density="compact"
                  color="primary"
                  return-object
                  :rules="[rules.require]"
                  :readonly="isApproved"
                  clearable
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-date-input
                  v-model="record.deliveryDate"
                  label="Delivery Date"
                  color="primary"
                  :readonly="isApproved"
                  :min="today"
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  prepend-icon
                  prepend-inner-icon="$calendar"
                  :rules="[rules.require]"
                  hide-actions
                ></v-date-input>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </v-card>

      <v-fab
        v-if="!isEdit"
        color="primary"
        app
        extended
        prepend-icon="mdi-plus"
        text="Add Item"
        location="bottom right"
        @click="toggleFilter"
      ></v-fab>

      <ReasonDialog
        v-if="reasonDialog"
        v-model="reasonDialog"
        @reject="reject"
      ></ReasonDialog>
      <!-- Table -->
      <PRTable
        :pr-list="prList"
        @removeItem="removePr"
        :isEdit="isEdit"
        @edit="updatePr"
        @addItem="toggleFilter"
      />

      <!-- Drawer Form -->
      <PRItemForm
        v-if="!isEdit"
        ref="editFilter"
        @add-pr="addPR"
        :inventoryList="inventoryItems"
        :vendorList="vendors"
        :hasVendor="!!record.vendor"
      />
    </v-container>
    <FormLoader v-else />
  </div>
</template>

<script setup>
import { ref, computed, onBeforeMount } from "vue";
import PRItemForm from "@/components/purchase/PRItemForm.vue";
import PRTable from "@/components/purchase/PRTable.vue";
import ReasonDialog from "@/components/base/ReasonDialog.vue";
import FormActionsBar from "@/components/utils/FormActionsBar.vue";
import FormLoader from "@/components/utils/FormLoader.vue";

import { useStoreStore } from "@/stores/store";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useVendorStore } from "@/stores/vendor";
import { usePurchaseRequestStore } from "@/stores/purchaseRequest";
import { usePurchaseOrderStore } from "@/stores/purchaseOrder";

import { purchaseRequestRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";

const locationStore = useStoreStore();
const inventoryStore = useInventoryItemStore();
const vendorStore = useVendorStore();
const purchaseRequestStore = usePurchaseRequestStore();
const purchaseOrderStore = usePurchaseOrderStore();

const locations = computed(() => locationStore.getStores);
const inventoryItems = computed(() => inventoryStore.getInventoryItems);
const vendors = computed(() => vendorStore.getVendors);

const prList = ref([]);

const loading = ref(false);
const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

const route = useRoute();
const purchaseRequestId = route.params.id;
const isEdit = purchaseRequestId !== undefined;
const today = new Date().toISOString().split("T")[0];

const reasonDialog = ref(false);

const isApproved = computed(() =>
  record.value.statusTimeline?.some((item) => item.name === "approved")
);

const isClosed = computed(() =>
  record.value.statusTimeline?.some(
    (item) => item.name === "rejected" || item.name == "completed"
  )
);

const isApprovalNeeded = ref(
  JSON.parse(localStorage.getItem("approvalNeeded"))
);

const editFilter = ref(null);
const toggleFilter = () => {
  editFilter.value.toggle();
};

const calculateTotalPrice = (row) => {
  if (!row.unitCost || !row.quantity) return 0;

  // subtract discount from unit cost
  const effectiveUnitCost = Number(row.unitCost) - Number(row.discount || 0);

  const subTotal = effectiveUnitCost * Number(row.quantity);
  const taxAmount = (subTotal * Number(row.taxRate)) / 100;
  return { taxAmount, totalPrice: (subTotal + taxAmount).toFixed(2) };
};

const updatePr = (row) => {
  const { taxAmount, totalPrice } = calculateTotalPrice(row);
  row.taxAmount = taxAmount >= 0 ? taxAmount : 0;
  row.totalPrice = totalPrice >= 0 ? totalPrice : 0;
};

const addPR = (pr) => {
  const { taxAmount, totalPrice } = calculateTotalPrice(pr);
  pr.taxAmount = taxAmount;
  pr.totalPrice = totalPrice;
  prList.value.unshift(pr);
};

const removePr = (index) => {
  prList.value.splice(index, 1);
};

const onVendorTypeChange = (value) => {
  record.value.vendorType = value;
  if (value === 1) record.value.vendor = null;
};

const router = useRouter();

const navigatePrevious = () => {
  router.push({ name: "Purchase Requests (PR)" });
};

const form = ref(null);
const submitPR = async () => {
  if (loading.value) return;
  loading.value = true;

  const { valid } = await form.value.validate();
  if (!valid) {
    loading.value = false;
    return;
  }

  const payload = {
    location: {
      id: record.value.location?.id,
      name: record.value.location?.name,
    },
    deliveryDate: record.value.deliveryDate,
    vendorType: record.value.vendorType,
    items: prList.value.map((item) => {
      const vendor =
        record.value.vendorType === 2 ? record.value.vendor : item.vendor;
      return { ...item, vendor: { id: vendor.id, name: vendor.name } };
    }),
  };

  if (isEdit) {
    payload.id = record.value.id;
    payload.prNumber = record.value.prNumber;
    payload.inventoryLocation = record.value.inventoryLocation;
    await purchaseRequestStore.updatePurchaseRequest(payload);
  } else {
    await purchaseRequestStore.createPurchaseRequest(payload);
  }
  navigatePrevious();
  loading.value = false;
};

const submitPO = async () => {
  if (loading.value) return;
  loading.value = true;

  const { valid } = await form.value.validate();
  if (!valid) {
    loading.value = false;
    return;
  }

  const payload = {
    location: {
      id: record.value.location?.id,
      name: record.value.location?.name,
    },
    deliveryDate: new Date(record.value.deliveryDate),
    vendorType: record.value.vendorType,
    status: record.value.status,
    items: prList.value.map((item) => {
      const vendor =
        record.value.vendorType === 2 ? record.value.vendor : item.vendor;
      return { ...item, vendor: { id: vendor.id, name: vendor.name } };
    }),
  };

  if (record.value.inventoryLocation) {
    payload.inventoryLocation = record.value.inventoryLocation;
  }

  if (isEdit) {
    payload.id = record.value.id;
    payload.prNumber = record.value.prNumber;
  }

  await purchaseOrderStore.createPurchaseOrder(payload);
  router.push({ name: "Purchase Order (PO)" });
  loading.value = false;
};

const submitApproval = async () => {
  await purchaseRequestStore.approvePurchaseRequest(record.value.id);
  navigatePrevious();
};

const reject = async (reason) => {
  await purchaseRequestStore.rejectPurchaseRequest(record.value.id, {
    rejectedReason: reason,
  });
  navigatePrevious();
};

onBeforeMount(async () => {
  loading.value = true;

  try {
    await Promise.all([
      locationStore.fetchStores(),
      vendorStore.fetchVendors(),
      inventoryStore.fetchInventoryItems(),
    ]);

    if (isEdit) {
      const { items, ...result } =
        await purchaseRequestStore.fetchPurchaseRequestById(purchaseRequestId);
      record.value = result;
      prList.value = items;
      onVendorTypeChange(record.value.vendorType);
      record.value.vendor = items[0].vendor;
    }

    loading.value = false;
  } catch (err) {
    console.error(err);
  }
});
</script>
