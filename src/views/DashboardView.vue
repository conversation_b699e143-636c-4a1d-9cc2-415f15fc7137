<template>
  <v-container fluid class="px-6 py-5">
    <v-card fluid class="custom-card">
      <v-tabs
        v-model="tab"
        align-tabs="center"
        slider-color="primary"
        selected-class="text-primary"
        density="compact"
        @update:model-value="onChangeTab"
      >
        <v-tab :value="1">SALES</v-tab>
        <v-tab :value="2">MENUITEMS</v-tab>
        <v-tab :value="3">SUMMARY</v-tab>
      </v-tabs>
      <v-row class="pa-5 d-flex align-center" no-gutters>
        <v-col cols="12" sm="auto" md="auto" lg="3" xl="2" class="mr-4">
          <store-select
            :multipleStore="true"
            @store-change="storeChange"
          ></store-select>
        </v-col>
        <v-spacer></v-spacer>
        <v-col
          cols="12"
          sm="6"
          md="auto"
          lg="auto"
          class="mr-4"
          :class="{ 'mt-4': smAndDown }"
        >
          <v-menu>
            <template v-slot:activator="{ props }">
              <v-btn color="primary" v-bind="props" variant="tonal">
                <span>{{ selectedDateFormat.title }}</span>
              </v-btn>
            </template>
            <v-list selectable @update:selected="updateDateFormat">
              <v-list-item
                v-for="item in dateFormatList"
                :key="item.title"
                :value="item"
              >
                <v-list-item-title>{{ item.title }} </v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          md="3"
          lg="2"
          class="mr-4"
          :class="{ 'mt-4': smAndDown }"
        >
          <v-date-input
            v-if="report.dt !== 3"
            v-model="date"
            label="Date"
            variant="outlined"
            density="compact"
            prepend-icon=""
            prepend-inner-icon="$calendar"
            multiple="range"
            color="primary"
            :max="new Date()"
            hide-details
            @update:model-value="dateChange"
          >
          </v-date-input>
        </v-col>
        <v-col cols="auto" class="mr-4" :class="{ 'mt-4': smAndDown }">
          <v-btn
            variant="tonal"
            color="primary"
            inline
            offset-y="60"
            offset-x="60"
            tile
            class="btn-radius"
            @click="toggleFilter"
          >
            <v-icon icon="mdi-filter-variant"></v-icon
          ></v-btn>
        </v-col>
        <v-col cols="auto" :class="{ 'mt-4': smAndDown }">
          <v-btn
            color="primary"
            variant="flat"
            flat
            @click="list(true)"
            :loading="submitLoader"
          >
            <span v-if="lgAndUp">Search</span>
            <v-icon
              icon="mdi-magnify"
              :class="{ 'ml-2': lgAndUp }"
            ></v-icon> </v-btn
        ></v-col>
      </v-row>
      <v-divider></v-divider>
      <v-row
        v-if="report.dt !== 3"
        v-show="stores.length > 1"
        style="margin-top: -22px; z-index: 2"
        justify="center"
        class="px-5"
      >
        <v-col cols="12" sm="6" md="4">
          <v-autocomplete
            v-model="selectedStoreId"
            :items="stores"
            item-title="name"
            item-value="id"
            density="compact"
            color="primary"
            variant="solo"
            hide-details
            clearable
          >
          </v-autocomplete>
        </v-col>
      </v-row>
      <v-row class="px-5">
        <v-col>
          <v-btn
            variant="tonal"
            color="primary"
            inline
            offset-y="60"
            offset-x="60"
            tile
            large
            class="btn-radius"
            @click="toggleFilter"
            >Filter <v-icon icon="mdi-filter-variant" class="ml-2"></v-icon
          ></v-btn>
        </v-col>
      </v-row>
      <v-container v-if="report.dt === 3" fluid>
        <summary-types @change="fetchSummary"></summary-types>
      </v-container>
      <dashboard-sales
        :dashboardData="dashboardData"
        :store-data="storeData"
        v-if="report.dt === 1"
      ></dashboard-sales>
      <menu-item-dashboard
        :data="dashboardData"
        v-if="report.dt == 2"
      ></menu-item-dashboard>
      <dashboard-summary
        v-if="report.dt == 3"
        :summary="summaryInfo"
        v-model="report.st"
      ></dashboard-summary>
    </v-card>
  </v-container>
  <filter-nav-drawer ref="editFilter"></filter-nav-drawer>
</template>
<script setup>
import { ref, computed, onMounted, onBeforeMount } from "vue";
import { useIndexStore } from "@/stores";
import StoreSelect from "@/components/base/StoreSelect.vue";
import SummaryTypes from "@/components/dashboard/SummaryTypes.vue";
import DashboardSummary from "@/components/dashboard/DashboardSummary.vue";
import MenuItemDashboard from "@/components/dashboard/MenuItemDashboard.vue";
import DashboardSales from "@/components/dashboard/DashboardSales.vue";
import FilterNavDrawer from "@/components/base/drawer/FilterNavDrawer.vue";
// import DatePicker from "@/components/base/DatePicker.vue";
import { format } from "date-fns";
import { VDateInput } from "vuetify/labs/VDateInput";
import { useDisplay } from "vuetify/lib/framework.mjs";

const { lgAndUp, smAndDown } = useDisplay();

const store = useIndexStore();

const tab = ref(1);
const date = ref([new Date(), new Date()]);
const selectedDateFormat = ref(null);
const dateFormatList = ref([
  { title: "Invoice Date", val: "invoice_date" },
  { title: "GRN Date", val: "grn_date" },
  { title: "Entry Date", val: "entry_date" },
]);

const report = ref({
  fromDate: format(new Date(), "dd-MM-yyyy"),
  toDate: format(new Date(), "dd-MM-yyyy"),
  storeIDs: [],
  dt: 1,
  st: 1,
});
const results = ref({
  stores: [],
  total: {},
});
const selectedStoreId = ref(null);
const submitLoader = ref(false);
const summaryInfo = ref({});
const editFilter = ref(null);

const toggleFilter = () => {
  editFilter.value.toggle();
};

// const formattedDate = ref(format(new Date(), "dd-MM-yyyy"));
// const maxDate = ref(new Date().toISOString().substr(0, 10));

const stores = computed(() => {
  let res = [];
  if (!results.value) {
    return res;
  }
  const stores = results.value.stores;
  stores.forEach((store) => {
    res.push({ id: store.store_id, name: store.store_name });
  });
  if (stores.length > 1) {
    return [{ id: null, name: "-ALL LOCATIONS-" }, ...res];
  }
  return [];
});

const storeData = computed(() => {
  if (!results.value) {
    return {
      show: false,
    };
  }
  return {
    res: results.value.stores,
    show: selectedStoreId.value == null && stores.value.length > 0,
  };
});

const dashboardData = computed(() => {
  if (!results.value) return {};
  if (selectedStoreId.value) {
    const storeData = results.value.stores.find(
      (store) => store.store_id === selectedStoreId.value
    );
    if (storeData) return storeData;
  }
  return results.value.total;
});

const list = async (reset = false) => {
  if (report.value.dt === 3) return fetchSummary();
  if (reset) selectedStoreId.value = null;
  submitLoader.value = true;
  const data = await store.fetchDashboardSale(231, report.value);
  results.value = data || {};
  submitLoader.value = false;
};

const fetchSummary = async (st) => {
  if (st) report.value.st = st;
  submitLoader.value = true;
  summaryInfo.value = await store.fetchDashboardSummary(231, report.value);
  submitLoader.value = false;
};

const onChangeTab = (v) => {
  report.value.dt = v;
  if (v === 2 && results.value.dt != 2) {
    list();
    return;
  }
  if (v === 3) fetchSummary();
};

const storeChange = (v) => {
  report.value.storeIDs = v;
};

const dateChange = (v) => {
  if (v.length == 1) {
    report.value.fromDate = formatDate(v[0]);
    report.value.toDateDate = formatDate(v[0]);
  } else {
    report.value.fromDate = formatDate(v[0]);
    report.value.toDate = formatDate(v[v.length - 1]);
  }
};

const formatDate = (date) => {
  const options = { day: "2-digit", month: "short", year: "numeric" };
  return new Date(date)
    .toLocaleDateString("en-GB", options)
    .trim()
    .replaceAll(" ", "-");
};

const updateDateFormat = (item) => {
  selectedDateFormat.value = item[0];
};

onBeforeMount(() => {
  selectedDateFormat.value = dateFormatList.value[0];
});
onMounted(() => {
  dateChange(date.value);
  list(true);
});
</script>
<style scoped></style>
