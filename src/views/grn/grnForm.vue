<template>
  <v-container v-if="!loader" fluid class="pt-2">
    <v-card-actions class="px-4 pb-4">
      <v-spacer />
      <v-btn @click="navigatePrevious" variant="outlined" color="primary"
        ><v-icon>mdi-close</v-icon>Close</v-btn
      >
      <!-- <v-btn text="Create GRN" @click="submit" variant="flat" color="primary" /> -->
    </v-card-actions>

    <v-divider class="mx-4" />

    <v-card-text class="pt-4 scrollable-content">
      <v-form ref="form">
        <v-sheet class="pa-4" elevation="0" border rounded>
          <v-row>
            <v-col
              v-for="(item, idx) in details"
              :key="idx"
              class="text-subtitle-1 font-weight-medium"
              cols="3"
            >
              {{ item.label }}:
              <span class="text-subtitle-1 font-weight-bold">{{
                item.value
              }}</span>
            </v-col>
          </v-row>
        </v-sheet>

        <v-divider class="my-4"></v-divider>
        <v-row>
          <v-col cols="12">
            <ClosedPurchaseItemTable
              :items="purchaseItems"
              :headers="GRNHeaders"
            ></ClosedPurchaseItemTable>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-container>
  <v-container
    v-else
    class="d-flex justify-center align-center"
    style="height: 100%"
  >
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate></v-progress-circular>
    </div>
  </v-container>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { GRNHeaders } from "@/helpers/tableHeaders";
import {
  purchaseOrderItemRecord as DEFAULT_PURCHASE_ITEM,
  purchaseRequestRecord as DEFAULT_RECORD,
} from "@/helpers/defaultRecords";
import { useRoute, useRouter } from "vue-router";

import { useLocationStore } from "@/stores/location";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useGrnStore } from "@/stores/goodsReceivedNote";

import { ConvertIOStoDate } from "@/helpers/date";
import ClosedPurchaseItemTable from "@/components/purchase/closedPurchaseItemTable.vue";

const locationStore = useLocationStore();
const inventoryStore = useInventoryItemStore();
const grnStore = useGrnStore();

const router = useRouter();
const route = useRoute();
const form = ref(null);

const vendor = ref(null);
const purchaseItems = ref([]);
const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

const grnId = route.params.id;
const isEdit = grnId !== undefined;
const loader = ref(false);

const details = ref([]);

const inventoryItems = computed(() => inventoryStore.getInventoryItems || []);

const filteredInventoryItems = computed(() => {
  const selectedIds = purchaseItems.value.map((i) => i.itemId);
  return inventoryItems.value.filter((item) => !selectedIds.includes(item.id));
});

const selectedLocations = computed(() => locationStore.getSelectedLocation);

const navigatePrevious = () => {
  router.push({ name: "Goods Received Note (GRN)" });
};

onBeforeMount(async () => {
  loader.value = true;

  try {
    await Promise.all([
      locationStore.fetchLocations(),
      inventoryStore.fetchInventoryItems(),
    ]);

    if (isEdit) {
      const result = await grnStore.fetchGrnById(grnId);
      record.value = result;
      purchaseItems.value = result.items.map((item) => ({
        ...item,
        receivedQuantity: item.qty,
        purchaseUOM: item.uom,
        totalPrice: item.totalValue,
      }));
      vendor.value = result.vendor;
      details.value = [
        {
          label: "Work/Storage Area",
          value: record.value.locationName,
        },
        {
          label: "GRN No",
          value: record.value.grnNumber,
        },
        {
          label: "Goods Received Date",
          value: ConvertIOStoDate(record.value.grnDate),
        },
        {
          label: "Vendor Invoice Date ",
          value: ConvertIOStoDate(record.value.invoiceDate),
        },
        {
          label: "Vendor",
          value: record.value.vendorName,
        },
        {
          label: "PO No",
          value: record.value.poNumber,
        },
        {
          label: "Invoice No",
          value: record.value.invoiceNumber,
        },
      ];
    }

    loader.value = false;
  } catch (err) {
    console.error(err);
  } finally {
  }
});
</script>

<style>
.scrollable-content {
  overflow-y: auto;
  max-height: calc(100vh - 150px); /* Adjust if needed */
  padding-right: 16px; /* avoid scrollbar overlap */
}
</style>
