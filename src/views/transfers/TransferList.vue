<template>
  <div>
    <filter-bar @search="handleSearch">
      <v-btn variant="tonal" color="primary" @click="toggleFilter">
        <v-icon icon="mdi-filter-variant" />
      </v-btn>

      <v-btn variant="tonal" color="primary" class="mx-3" @click="add">
        <v-icon icon="mdi-plus" />Create
      </v-btn>

      <v-btn variant="tonal" color="primary" @click="refresh">
        <v-icon icon="mdi-reload" />
      </v-btn>
    </filter-bar>

    <v-container fluid>
      <v-row no-gutters>
        <v-card class="border rounded-lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No transfers found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.transferNumber="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="view(item.id)"
              >{{ item.transferNumber }}</span>
            </template>

            <template #item.dispatchStatus="{ item }">
              <v-chip
                class="text-uppercase"
                label
                :color="getStatusColor(item.dispatchStatus)"
              >
                {{ item.dispatchStatus }}
              </v-chip>
            </template>
            <template #item.receiveStatus="{ item }">
              <v-chip
                class="text-uppercase"
                label
                :color="getStatusColor(item.receiveStatus)"
              >
                {{ item.receiveStatus }}
              </v-chip>
            </template>
            <template #item.requestedDate="{ item }">
              {{ item.requestedDate }}
            </template>
            <template #item.action="{ item }">
              <transfer-actions :item="item" />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <filter-nav-drawer
      ref="editFilter"
      :tabs="tabs"
      :filtersData="filters"
      @apply-filter="applyFilters"
    ></filter-nav-drawer>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";
import FilterBar from "@/components/base/FilterBar.vue";
import { indentHeaders } from "@/helpers/tableHeaders";
import { getStatusColor } from "@/helpers/status";
import { dateTimeFormat } from "@/helpers/date";
import { useLocationStore } from "@/stores/location";
import { useTransferStore } from "@/stores/transfer";
import { filterData } from "@/helpers/searchFilter";
import { useSnackbarStore } from "@/stores/snackBar";
import TransferActions from "@/components/TransferActions.vue";

import FilterNavDrawer from "@/components/filters/navDrawer.vue";
import ToggleFilter from "@/components/base/drawer/filters/ToggleFilter.vue";
import AutoComplete from "@/components/filters/Autocomplete.vue";

const router = useRouter();
const transferStore = useTransferStore();
const workAreaStore = useLocationStore();

const workAreas = computed(() => workAreaStore.getLocations || []);

const { showSnackbar } = useSnackbarStore();

const search = ref(null);
const loading = ref(false);
const editFilter = ref(null);
const record = ref({});

// const closeTransfer = async (id) => {
//   try {
//     const result = await transferStore.fetchTransferById(id, "close");
//     record.value = { ...result };

//     const payload = {
//       ...record.value,
//       dispatchStatus: "completed",
//       receiveStatus: "completed",
//     };

//     await transferStore.closeTransfer(payload);
//     refresh();
//   } catch (err) {
//     console.error(err);
//     showSnackbar("error", "Failed to close transfer");
//   }
// };

const headers = ref(indentHeaders);

const items = computed(() => transferStore.getTransfers || []);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  return result;
});

const filteredHeaders = computed(() => headers.value.filter((h) => h.default));

const dataFilters = ref({
  status: ["pending"],
});
const tabs = [{ value: 1, label: "filters" }];
const filters = computed(() => [
  {
    component: ToggleFilter,
    title: "Status",
    key: "status",
    items: [
      { name: "In progress", value: "pending" },
      { name: "Completed", value: "completed" },
    ],
  },
  {
    component: AutoComplete,
    title: "Requester",
    key: "requester",
    items: workAreas.value,
    default: true,
  },
  {
    component: AutoComplete,
    title: "Issuer",
    key: "issuer",
    items: workAreas.value,
  },
]);

const applyFilters = (filters) => {
  dataFilters.value = filters;
  refresh();
};

const handleSearch = (v) => {
  search.value = v;
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    // await transferStore.fetchTransfers(dataFilters.value);
    await transferStore.fetchTransfers(    { status: ["pending"]});
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const add = () => {
  router.push({
    name: "Create Transfer",
  });
};

const view = (id) => {
  router.push({ name: "View Transfer", params: { id } });
};

const toggleFilter = () => {
  editFilter.value.toggle();
};

onBeforeMount(async () => {
  await workAreaStore.fetchLocations();
  await refresh();
});
</script>
