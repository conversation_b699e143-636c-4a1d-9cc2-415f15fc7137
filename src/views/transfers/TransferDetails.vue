<template>
  <v-expansion-panels v-model="panel">
    <v-expansion-panel>
      <v-expansion-panel-title class="text-primary font-weight-bold px-3">
        Transfer Details
      </v-expansion-panel-title>
      <v-expansion-panel-text>
        <v-row class="pa-1">
          <v-col cols="12" md="6" class="pa-0">
            <v-row
              v-for="(field, index) in leftFields"
              :key="`left-${index}`"
              no-gutters
              class="py-1"
            >
              <v-col cols="4" class="field-label">
                {{ field.label }}
              </v-col>
              <v-col cols="8" class="field-value">
                {{ field.value }}
              </v-col>
            </v-row>
          </v-col>

          <v-col cols="12" md="6" class="pa-0">
            <v-row
              v-for="(field, index) in rightFields"
              :key="`right-${index}`"
              no-gutters
              class="py-1"
            >
              <v-col cols="4" class="field-label">
                {{ field.label }}
              </v-col>
              <v-col cols="8" class="field-value">
                {{ field.value }}
              </v-col>
            </v-row>

            <template v-if="showDetails">
              <v-row
                v-for="(field, index) in detailFields"
                :key="`detail-${index}`"
                no-gutters
                class="py-1"
              >
                <v-col cols="4" class="field-label">
                  {{ field.label }}
                </v-col>
                <v-col cols="8" class="field-value">
                  {{ field.value }}
                </v-col>
              </v-row>
            </template>
          </v-col>
        </v-row>
      </v-expansion-panel-text>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script setup>
import { ref, computed } from "vue";
import { dateTimeFormat } from "@/helpers/date";

const { record, showDetails, dispatchedBy, dispatchedAt } = defineProps({
  record: { type: Object, required: true },
  showDetails: { type: Boolean, default: false },
  dispatchedBy: { type: String },
  dispatchedAt: { type: Object },
});

const panel = ref(0);

const leftFields = computed(() => [
  { label: "Transfer No.", value: record.transferNumber },
  { label: "Requester", value: record.requester?.name },
  { label: "Issuer", value: record.issuer?.name },
]);

const rightFields = computed(() => [
  { label: "Requested By", value: record.requestedBy?.name },
  {
    label: "Requested At",
    value: record.requestedBy?.time,
  },
]);

const detailFields = computed(() => [
  { label: "Dispatched By", value: dispatchedBy || '-' },
  {
    label: "Dispatched At",
    value: dateTimeFormat(dispatchedAt),
  },
]);
</script>

<style scoped>
.field-label {
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 400;
}

.field-value {
  font-size: 1rem;
  font-weight: 500;
  color: #000000;
}
</style>
