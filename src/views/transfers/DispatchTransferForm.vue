<template>
  <v-container v-if="!loader" fluid class="pt-2">
    <v-card-actions class="pb-4 px-1">
      <v-spacer />
      <v-btn @click="navigatePrevious" variant="outlined" color="primary">
        <v-icon>mdi-close</v-icon>Close
      </v-btn>

      <v-btn
        text="Print"
        @click="printDispatch"
        variant="flat"
        :loading="loadingPrint"
        :disabled="loadingPrint"
        color="primary"
      />

      <v-btn
        v-if="canDispatch"
        text="Dispatch"
        @click="dispatchTransfer"
        variant="flat"
        color="primary"
        :disabled="!enableDispatch || dispatching"
        :loading="dispatching"
      />
    </v-card-actions>

    <v-divider class="mb-3"/>

    <v-card-text class="pt-4 scrollable-content">
      <v-form>
        <v-row class="d-flex">
        
          <transfer-details :record="record" />
          
          <v-divider class="mt-3 mb-3" />

          <v-col>
            <v-row>
              <v-card class="parent-cont ma-0" width="100%">
                <v-container fluid class="pa-0">
                  <v-data-table
                    :headers="dispatchIndentHeaders"
                    :items="record.items || []"
                    class="custom-table"
                    :loading="loading"
                    :hide-default-footer="(record.items || []).length < 11"
                    :no-data-text="loading ? 'Loading indent data request...' : 'No indent data found'"
                  >
                    <template #item.dispatchedQuantity="{ item }" v-if="canDispatch">
                        <v-text-field
                            v-model.number="item.dispatchedQuantity"
                            type="number"
                            density="compact"
                            variant="outlined"
                            hide-details
                            color="primary"
                            :min="0"
                            :max="
                              Math.min(
                                item.disPendingQuantity,
                                item.availableQuantity
                              )
                            "
                            @focus="onFocus(item, 'dispatchedQuantity')"
                            @blur="onBlur(item, 'dispatchedQuantity')"
                            @update:model-value="
                              (val) => {
                                const minValue = Math.min(
                                  item.disPendingQuantity,
                                  item.availableQuantity
                                );

                                if (val > minValue)
                                  item.dispatchedQuantity = minValue;
                                if (val < 0) item.dispatchedQuantity = 0;
                              }
                            "
                          >
                            <template v-slot:append-inner>
                              {{ item.countingUOM }}
                            </template>
                          </v-text-field>
                    </template>
                  </v-data-table>
                </v-container>
              </v-card>
            </v-row>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-container>

  <v-container v-else class="d-flex justify-center align-center" style="height: 100%">
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate />
    </div>
  </v-container>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useTransferStore } from "@/stores/transfer";
import { dispatchIndentHeaders } from "@/helpers/tableHeaders";
import { useSnackbarStore } from "@/stores/snackBar";
import TransferDetails from "./TransferDetails.vue";
import { getUser } from "@/helpers/auth";
import { usePrintStore } from "@/stores/print";

const transferStore = useTransferStore();
const printStore = usePrintStore();
const { showSnackbar } = useSnackbarStore();

const router = useRouter();
const route = useRoute();
const transferId = route.params.id;

const record = ref({});
const loader = ref(false);
const loading = ref(false);

const user = ref(getUser());

const enableDispatch = computed(() =>
  (record.value.items).some((item) => (item.dispatchedQuantity) > 0)
);

const canDispatch = computed(() => record.value.dispatchStatus !== "completed");

const navigatePrevious = () => {
  router.push({ name: "transfers" });
};

const onFocus = (item, value) => {
  if (Number(item[value]) === 0) {
    item[value] = null;
  }
};

const onBlur = (item, value) => {
  if (item[value] === null || item[value] === "") {
    item[value] = 0;
  }
};

const dispatching = ref(false);

const dispatchTransfer = async () => {      
  dispatching.value = true;
  try {
    const payload = {
      ...record.value,
      items: record.value.items.map((item) => {
        const copy = { ...item };
        delete copy.availableQuantity;
        delete copy.disPendingQuantity;
        return copy;
      })
    };

    const entry = {
      items: payload.items.map((item) => ({
        itemId: item.itemId,
        dispatchedQuantity: item.dispatchedQuantity,
        receivedQuantity: 0,
        shortageQuantity: 0
      })),
      dispatchedBy: {
        name: user.value.userName,
        id: user.value.userId,
      },
      status:'pending'
    };

    payload.timeLine = [
      entry,
      ...(record.value.timeLine || [])
    ];
    await transferStore.dispatchTransfer(payload);
    navigatePrevious();
  } catch (err) {
    console.error(err);
    showSnackbar("error", "Failed to dispatch indent");
  } finally {
    dispatching.value = false;
  }
};

const loadingPrint = ref(false);

const printDispatch = async () => {
  try {
    loadingPrint.value = true;
    const transferNumber = record.value.transferNumber;    
    await printStore.print('dispatch',transferNumber);
  } finally {
    loadingPrint.value = false;
  }
};

onBeforeMount(async () => {
  loader.value = true;
  try {
    const result = await transferStore.fetchTransferById(transferId, "dispatch");
    record.value = result;

    record.value.items = record.value.items.map((item) => {

      const stockQty = item.availableQuantity;
      const disQty = item.dispatchedQuantity ? item.dispatchedQuantity : 0;
      const pendingQty = item.requestedQuantity - disQty;
      const dispatchQty = Math.min(pendingQty, stockQty);

      return {
        ...item,
        dispatchedQuantity: !canDispatch.value ? item.dispatchedQuantity : dispatchQty,
        disPendingQuantity: pendingQty,
      };
    });
  } catch (err) {
    console.error(err);
  } finally {
    loader.value = false;
  }
});
</script>

<style scoped>
</style>
