<template>
  <v-container v-if="!loader" fluid class="pt-2">
    <v-card-actions class="pb-4 px-1">
      <v-spacer />
      <v-btn @click="navigatePrevious" variant="outlined" color="primary">
        <v-icon>mdi-close</v-icon>Close
      </v-btn>
      <transfer-actions :item="record" />
    </v-card-actions>

    <v-divider class="mb-3"/>

    <v-card-text class="pt-4 scrollable-content">
      <v-row class="d-flex">

        <transfer-details :record="record" />

        <v-divider class="mt-3 mb-3" />

        <v-col>
          <v-row>
            <v-card class="parent-cont ma-0" width="100%">
              <v-container fluid class="pa-0">
                <v-data-table
                  :headers="tableHeaders"
                  :items="record.items || []"
                  class="custom-table"
                  :loading="loading"
                  :hide-default-footer="(record.items || []).length < 11"
                  :no-data-text="
                    loading
                      ? 'Loading transfer items...'
                      : 'No items found'
                  "
                >
                  <template v-slot:item="{ item }">
                    <tr class="result-elem default">
                      <td
                        v-for="header in tableHeaders"
                        :key="header.key"
                        :title="header.title"
                        :class="{
                          'text-left': header.align === 'start',
                          'text-center': header.align === 'center',
                          'text-right': header.align === 'end',
                        }"
                      >
                        {{
                          typeof item[header.key] === "object"
                            ? item[header.key]?.name
                            : item[header.key]
                        }}
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-container>
            </v-card>
          </v-row>
        </v-col>
      </v-row>
    </v-card-text>
  </v-container>

  <v-container
    v-else
    class="d-flex justify-center align-center"
    style="height: 100%"
  >
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate />
    </div>
  </v-container>
</template>

<script setup>
import { onBeforeMount, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useTransferStore } from "@/stores/transfer";
import { viewTransferHeaders } from "@/helpers/tableHeaders";
import TransferActions from "@/components/TransferActions.vue";
import TransferDetails from "./TransferDetails.vue";

const transferStore = useTransferStore();
const router = useRouter();
const route = useRoute();
const indentId = route.params.id;

const record = ref({});
const loader = ref(false);
const loading = ref(false);
const panel = ref(0);

const tableHeaders = viewTransferHeaders;

const navigatePrevious = () => {
  router.push({ name: "transfers" });
};

onBeforeMount(async () => {
  loader.value = true;
  try {
    const result = await transferStore.fetchTransferById(indentId, "view");
    record.value = result;
  } catch (err) {
    console.error(err);
  } finally {
    loader.value = false;
  }
});
</script>

<style scoped>
.scrollable-content {
  overflow-y: auto;
  max-height: calc(100vh - 150px);
  padding-right: 16px;
}
</style>
