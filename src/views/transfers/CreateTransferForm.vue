<template>
  <v-container v-if="!loader" fluid>
    <v-form ref="form">
      <v-card-actions>
        <v-row>
          <v-col>
            <v-select
              v-model="record.transferType"
              :items="transferTypes"
              item-value="value"
              item-title="text"
              label="Transfer Type"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
              @update:model-value="onTransferTypeChange"
            ></v-select>
          </v-col>
          <v-col>
            <v-autocomplete
              v-model="record.requester"
              :items="locations"
              item-value="id"
              item-title="name"
              return-object
              label="Requester"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
              @update:model-value="onRequesterChange"
              clearable
              persistent-hint
              hint="*Location creating the request"
            ></v-autocomplete>
          </v-col>

          <v-col>
            <v-autocomplete
              v-model="record.issuer"
              :items="filteredLocations"
              item-value="id"
              item-title="name"
              return-object
              label="Issuer"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
              :disabled="!record.requester"
              clearable
              persistent-hint
              hint="*Location sending the items"
            ></v-autocomplete>
          </v-col>

          <v-col class="d-flex justify-end">
            <v-btn
              @click="navigatePrevious"
              variant="outlined"
              color="primary"
              class="mx-3"
            >
              <v-icon>mdi-close</v-icon>Close
            </v-btn>
            <v-btn
              :text="'Create'"
              @click="create"
              variant="flat"
              color="primary"
              :loading="loader"
              :disabled="loader"
            />
          </v-col>
        </v-row>
      </v-card-actions>

      <v-divider class="mx-2 my-3"></v-divider>

      <v-card-text class="pt-4" style="padding: 0.5rem !important">
        <v-row class="d-flex align-center">
          <v-col cols="12">
            <transfer-table
              v-model="ingredients"
              :inventoryList="filteredInventoryItems"
            ></transfer-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-form>
  </v-container>
  <v-container
    v-else
    class="d-flex justify-center align-center"
    style="height: 100%"
  >
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate></v-progress-circular>
    </div>
  </v-container>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { indentRequestRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useRouter } from "vue-router";
import rules from "@/helpers/rules";
import { useLocationStore } from "@/stores/location";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useTransferStore } from "@/stores/transfer";
import TransferTable from "@/components/TransferTable.vue";
import { useSnackbarStore } from "@/stores/snackBar";
import { getUser } from "@/helpers/auth";

const { showSnackbar } = useSnackbarStore();
const router = useRouter();

const locationStore = useLocationStore();
const inventoryStore = useInventoryItemStore();
const transferStore = useTransferStore();

const locations = computed(() => locationStore.getLocations);
const inventoryList = computed(() => inventoryStore.getInventoryItems);

const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const ingredients = ref([]);
const user = ref(getUser());

const form = ref(null);
const loader = ref(false);

// Transfer types
const transferTypes = [
  { value: 'internal', text: 'Internal Transfer' },
  { value: 'external', text: 'External Transfer' }
];

const filteredInventoryItems = computed(() => {
  const selectedIds = ingredients.value.map((i) => i.id);
  return inventoryList.value.filter((item) => !selectedIds.includes(item.id));
});

const filteredLocations = computed(() => {
  if (!record.value.requester) return [];
  if (record.value.transferType === 'internal') {
      return locations.value.filter((loc) => loc.id !== record.value.requester.id && loc.store.id === record.value.requester.store.id);
  } else if (record.value.transferType === 'external') {
      return locations.value.filter((loc) => loc.id !== record.value.requester.id && loc.store.id !== record.value.requester.store.id);
  }
});

const create = async () => {
  const { valid } = await form.value.validate();
  if (!valid) return;

  if (!ingredients.value.length) {
    showSnackbar("primary", "At least one item is required");
    return;
  }

  loader.value = true;
  try {
    const issuerStore = locations.value.find(
      (loc) => loc.id === record.value.issuer.id
    )?.store;
    const requesterStore = locations.value.find(
      (loc) => loc.id === record.value.requester.id
    )?.store;

    const payload = {
      issuer: {
        id: record.value.issuer.id,
        name: record.value.issuer.name,
        locationId: issuerStore.id,
        locationName: issuerStore.name,
      },
      requester: {
        id: record.value.requester.id,
        name: record.value.requester.name,
        locationId: requesterStore.id,
        locationName: requesterStore.name,
      },
      items: ingredients.value?.map((ing) => ({
        itemId: ing.id,
        itemName: ing.name,
        itemCode: ing.code,
        categoryId: ing.categoryId,
        subcategoryId: ing.subCategoryId,
        requestedQuantity: ing.quantity,
        countingUOM: ing.countingUnit.symbol,
      })),
      requestedBy: {
        name: user.value.userName,
        id: user.value.userId,
      },
    };

    await transferStore.createTransfer(payload);
    navigatePrevious();
  } catch (err) {
    console.error(err);
    showSnackbar("error", "Failed to create indent");
  } finally {
    loader.value = false;
  }
};

const navigatePrevious = () => {
  router.push({
    name: "transfers",
  });
};

onBeforeMount(async () => {
  loader.value = true;
  try {
    record.value.transferType = 'internal';
    await Promise.all([
      locationStore.fetchLocations(),
      inventoryStore.fetchInventoryItems(),
    ]);
    loader.value = false;
  } catch (err) {
    console.error(err);
  }
});

// Selection change handler
const onTransferTypeChange = (value) => {
  record.value.issuer = null;
};

const onRequesterChange = (value) => {
  record.value.issuer = null;
};

</script>
