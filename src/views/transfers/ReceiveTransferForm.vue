<template>
  <v-container v-if="!loader" fluid class="pt-2">
    <v-card-actions class="pb-4 px-1">

      <v-autocomplete
        v-model="selectedDispatch"
        :items="dispatchOptions"
        item-value="id"
        item-title="name"
        label="Dispatch Details"
        hide-details
        variant="outlined"
        density="compact"
        color="primary"
        class="mr-3"
        clearable
      ></v-autocomplete>

      <v-spacer />
      <v-btn @click="navigatePrevious" variant="outlined" color="primary">
        <v-icon>mdi-close</v-icon>Close
      </v-btn>

      <v-btn
        text="Print"
        @click="printReceive"
        variant="flat"
        :loading="loadingPrint"
        :disabled="loadingPrint"
        color="primary"
      />

      <v-btn
        v-if="selectedDispatchEntry?.status !== 'completed'"
        text="Receive"
        @click="receiveTransfer"
        variant="flat"
        color="primary"
        :disabled="!enableReceive || receiving"
        :loading="receiving"
      />
    </v-card-actions>

    <v-divider class="mb-3"/>

    <v-card-text class="pt-4 scrollable-content">
      <v-form>
        <v-row class="d-flex">

          <transfer-details 
            :record="record" 
            :showDetails="true" 
            :dispatchedBy="selectedDispatchEntry?.dispatchedBy.name" 
            :dispatchedAt="selectedDispatchEntry?.dispatchedBy.time" 
          />

          <v-divider class="mt-3 mb-3" />

          <v-col>
            <v-row>
              <v-card class="parent-cont ma-0" width="100%">
                <v-container fluid class="pa-0">
                  <v-data-table
                    :headers="receiveIndentHeaders"
                    :items="filteredItems"
                    class="custom-table"
                    :loading="loading"
                    :hide-default-footer="filteredItems.length < 11"
                    :no-data-text="loading ? 'Loading indent data request...' : 'No indent data found'"
                  >
                    <template #item.receivedQuantity="{ item }"  v-if="selectedDispatchEntry?.status !== 'completed'">
                        <v-text-field
                            v-model.number="item.receivedQuantity"
                            type="number"
                            density="compact"
                            variant="outlined"
                            hide-details
                            color="primary"
                            :min="0"
                            :max="item.dispatchedQuantity"
                            @focus="onFocus(item, 'receivedQuantity')"
                            @blur="onBlur(item, 'receivedQuantity')"
                            @update:model-value="
                              (val) => {
                                if (val > item.dispatchedQuantity)
                                  item.receivedQuantity = item.dispatchedQuantity;
                                if (val < 0) item.receivedQuantity = 0;
                                item.shortageQuantity =
                                  item.dispatchedQuantity - item.receivedQuantity;

                                if (item.shortageQuantity === 0) {
                                  item.reason = '';
                                }
                              }
                            "
                          >
                            <template v-slot:append-inner>
                              {{ item.countingUOM }}
                            </template>
                          </v-text-field>
                    </template>
                    <template #item.reason="{ item }"  v-if="selectedDispatchEntry?.status !== 'completed'">
                        <v-textarea
                            v-model="item.reason"
                            label="Reason"
                            color="primary"
                            variant="outlined"
                            density="compact"
                            :rows="1"
                            :max-rows="6"
                            hide-details="auto"
                            :disabled="!(item.shortageQuantity > 0)"
                        />
                    </template>
                  </v-data-table>
                </v-container>
              </v-card>
            </v-row>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-container>

  <v-container v-else class="d-flex justify-center align-center" style="height: 100%">
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate />
    </div>
  </v-container>
</template>

<script setup>
import { onBeforeMount, ref, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useTransferStore } from "@/stores/transfer";
import { receiveIndentHeaders } from "@/helpers/tableHeaders";
import { useSnackbarStore } from "@/stores/snackBar";
import TransferDetails from "./TransferDetails.vue";
import { usePrintStore } from "@/stores/print";
import { dateTimeFormat } from "@/helpers/date";
import { getUser } from "@/helpers/auth";

const transferStore = useTransferStore();
const printStore = usePrintStore();
const { showSnackbar } = useSnackbarStore();
const user = ref(getUser());

const router = useRouter();
const route = useRoute();
const transferId = route.params.id;

const record = ref({});
const loader = ref(false);
const loading = ref(false);

const selectedDispatch = ref(null);
const filteredItems = ref([]);

const receiving = ref(false);
const loadingPrint = ref(false);

const navigatePrevious = () => {
  router.push({ name: "transfers" });
};

const onFocus = (item, value) => {
  if (Number(item[value]) === 0) {
    item[value] = null;
  }
};

const onBlur = (item, value) => {
  if (item[value] === null || item[value] === "") {
    item[value] = 0;
  }
};

const enableReceive = computed(() =>
  (filteredItems.value).some((item) => (item.receivedQuantity) > 0)
);

const dispatchOptions = computed(() => {
  if (!record.value.timeLine) return [];
  return record.value.timeLine.map((entry) => ({
    id: entry.dispatchNo,
    name: `${entry.dispatchNo} | ${entry.dispatchedBy.name} | ${dateTimeFormat(entry.dispatchedBy.time)} | ${entry.status}`.toUpperCase()
  }));
});

const selectedDispatchEntry = computed(() => {
  if (!record.value.timeLine) return null;
  return record.value.timeLine.find(
    (entry) => entry.dispatchNo === selectedDispatch.value
  );
});

watch(selectedDispatch, (val) => {
  if (!val || !record.value.timeLine || !record.value.items) {
    filteredItems.value = [];
    return;
  }

  const dispatchEntry = record.value.timeLine.find(
    (entry) => entry.dispatchNo === val
  );

  if (!dispatchEntry) {
    filteredItems.value = [];
    return;
  }

  filteredItems.value = dispatchEntry.items.map((dispatchItem) => {
    const originalItem = record.value.items.find(
      (i) => i.itemId === dispatchItem.itemId
    );

    return {
      ...dispatchItem,
      itemName: originalItem.itemName,
      itemCode: originalItem.itemCode,
      dispatchedQuantity: dispatchItem.dispatchedQuantity,
      receivedQuantity: dispatchItem.dispatchedQuantity,
      shortageQuantity: dispatchItem.shortageQuantity,
    };
  });
});

const receiveTransfer = async () => {
  receiving.value = true;
  try {
    const hasShortage = filteredItems.value.some(
      (item) => item.shortageQuantity > 0 && !item.reason?.trim()
    );

    if (hasShortage) {
      return showSnackbar("primary", "Provide a valid reason for the shortage");
    }

    const updatedTimeline = record.value.timeLine.map((entry) => {
      if (entry.dispatchNo !== selectedDispatch.value) return entry;

      return {
        ...entry,
        items: entry.items.map((item) => {
          const updated = filteredItems.value.find(
            (i) => i.itemId === item.itemId
          );
          return updated
            ? {
                ...item,
                receivedQuantity: updated.receivedQuantity,
                shortageQuantity: updated.shortageQuantity,
                reason: updated.reason || "",
              }
            : item;
        }),
        // status: "completed",
        receivedBy: {
          name: user.value.userName,
          id: user.value.userId,
        },
      };
    });

    const payload = {
      ...record.value,
      timeLine: updatedTimeline,
    };

    const dispatchNumber = selectedDispatchEntry.value.dispatchNo
    await transferStore.receiveTransfer(payload,dispatchNumber);
    navigatePrevious();
  } catch (err) {
    console.error(err);
    showSnackbar("error", "Failed to receive indent");
  } finally {
    receiving.value = false;
  }
};

const printReceive = async () => {  
  try {
    loadingPrint.value = true;
    const transferNumber = record.value.transferNumber;    
    const dispatchNumber = selectedDispatchEntry.value.dispatchNo
    await printStore.print('receive',transferNumber,dispatchNumber);
  } finally {
    loadingPrint.value = false;
  }
};

watch(dispatchOptions, (options) => {
  if (options.length && !selectedDispatch.value) {
    selectedDispatch.value = options[0].id;
  }
}, { immediate: true });

onBeforeMount(async () => {
  loader.value = true;
  try {
    const result = await transferStore.fetchTransferById(transferId, "receive");
    record.value = result;
  } catch (err) {
    console.error(err);
  } finally {
    loader.value = false;
  }
});
</script>

<style scoped>
</style>
