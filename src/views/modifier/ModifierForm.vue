<template>
  <MenuItemTable
    :record="record"
    :flattenedItems="flattenedItems"
    :inventoryList="inventoryList"
    :recipeList="recipeList"
    :saveHandler="save"
    :loader="loader"
    routeName="Modifiers"
  />
</template>

<script setup>
import { ref, computed, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMenuItemStore } from '@/stores/menuItem';
import { useModifierStore } from '@/stores/modifier';
import { useInventoryItemStore } from '@/stores/inventoryItem';
import { useReceipeStore } from '@/stores/receipe';
import { menuItemRecord as DEFAULT_RECORD } from '@/helpers/defaultRecords';
import MenuItemTable from '@/components/MenuItemTable.vue';

const route = useRoute();
const router = useRouter();
const menuItemStore = useMenuItemStore();
const modifierStore = useModifierStore();
const inventoryItemStore = useInventoryItemStore();
const receipeStore = useReceipeStore();

const modifierId = route.params.id;
const accountId = route.query.accountId;
const isEdit = !!modifierId && !!accountId;

const loader = ref(false);
const record = ref({ ...DEFAULT_RECORD });
const flattenedItems = ref([]);

const inventoryList = computed(() => inventoryItemStore.getInventoryItems || []);
const recipeList = computed(() => receipeStore.getReceipes || []);

onBeforeMount(async () => {
  loader.value = true;
  try {
    await Promise.all([
      inventoryItemStore.fetchInventoryItems(),
      receipeStore.fetchReceipes(),
    ]);

    if (isEdit) {
      const result = await modifierStore.fetchModifierById(modifierId,accountId);
      record.value = { ...result };

      const type = route.query.createdItemType;
      const id = route.query.createdItemId;

      if (type === 'Inventory' && id) {
        const exists = inventoryList.value.find(i => i.id === id);
        if (!exists) {
          const newItem = await inventoryItemStore.fetchInventoryItemById(id);
          inventoryList.value.push(newItem);
        }
      } else if (type === 'Recipe' && id) {
        const exists = recipeList.value.find(r => r.id === id);
        if (!exists) {
          const newRecipe = await receipeStore.fetchReceipeById(id);
          recipeList.value.push(newRecipe);
        }
      }

      flattenedItems.value = [{
        itemName: result.itemName,
        itemType: result.itemType,
        selectedItemType: type,
        selectedItemName: id,
        servingLevels: result.servingLevels?.map(l => ({
          servingSizeId: l.servingSizeId,
          servingSizeName: l.servingSizeName,
        })) || [],
      }];
      if (type && id) {
        menuItemStore.updateRecipeUnit(flattenedItems.value[0], inventoryList.value, recipeList.value);
      }
    }
  } catch (err) {
    console.error(err);
  } finally {
    loader.value = false;
  }
});

const save = async () => {
  const item = flattenedItems.value[0];
  let code = '', name = '';

  if (item.selectedItemType === 'Inventory') {
    const i = inventoryList.value.find(it => it.id === item.selectedItemName);
    code = i?.itemCode;
    name = i?.itemName;
  } else if (item.selectedItemType === 'Recipe') {
    const r = recipeList.value.find(it => it.id === item.selectedItemName);
    code = r?.code;
    name = r?.name;
  }

  const payload = {
    id: record.value.id,
    activeStatus: record.value.activeStatus,
    account: record.value.account,
    itemName: record.value.itemName,
    itemCode: record.value.itemCode,
    item: name,
    code: code,
    itemType: item.selectedItemType,
    servingLevels: item.servingLevels?.map(v => ({
      servingSizeId: v.servingSizeId,
      servingSizeName: v.servingSizeName,
      qty: v.qty || '',
      recipeUnit: v.recipeUnit || '',
    })),
  };

  if (isEdit) await modifierStore.updateModifier(payload);
  router.push({ name: 'Modifiers' });
};
</script>
