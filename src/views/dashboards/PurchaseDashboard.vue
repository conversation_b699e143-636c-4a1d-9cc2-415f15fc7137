<template>
  <div>
    <!-- Report Header -->
    <ReportHeader
      :selectedDateFormat="selectedDateFormat"
      :report="report"
      :date="date"
      :submitLoader="submitLoader"
      :smAndDown="smAndDown"
      :lgAndUp="lgAndUp"
      :tabs="filterTabs"
      :filters="filters"
      :firstEmitDone="firstEmitDone"
      @store-change="onStoreChange"
      @update-date-format="onDateFormatChange"
      @date-change="onDateChange"
      @search="onSearch"
    />

    <v-container fluid>
    </v-container>

    <!-- Example report content -->
    <div class="mt-6 px-6">
      <p>Selected Store: {{ selectedStore }}</p>
      <p>Selected Date Format: {{ selectedDateFormat.title }}</p>
      <p>Selected Date: {{ date }}</p>
      <p>Applied Filters: {{ appliedFilters }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import ReportHeader from './ReportHeader.vue'

// -------------------------
// State for report header
// -------------------------
const selectedStore = ref(null)
const selectedDateFormat = ref({ title: 'Daily' })

const report = reactive({ dt: 1 })
const date = ref(new Date())
const submitLoader = ref(false)

// For responsive layout
const smAndDown = ref(false)
const lgAndUp = ref(true)

// Filter drawer data
const filterTabs = ref([
  { name: 'Category' },
  { name: 'Vendor' }
])
const filters = ref([
  { id: 1, name: 'Electronics', checked: false },
  { id: 2, name: 'Grocery', checked: false }
])
const firstEmitDone = ref(false)
const appliedFilters = ref([])

// -------------------------
// Event handlers
// -------------------------
function onStoreChange(store) {
  selectedStore.value = store
}

function onDateFormatChange(format) {
  selectedDateFormat.value = format
}

function onDateChange(newDate) {
  date.value = newDate
}

function onToggleFilter() {
  console.log('Filter toggle clicked')
}

function onSearch() {
  console.log('Search clicked')
  submitLoader.value = true
  setTimeout(() => submitLoader.value = false, 1000)
}

function onApplyFilter(filtersApplied) {
  appliedFilters.value = filtersApplied
  console.log('Filters applied:', filtersApplied)
}
</script>
