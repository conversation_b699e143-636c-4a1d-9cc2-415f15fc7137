<template>
  <div>
    <div v-if="single">
      {{ displayKey ? item[displayKey] : item.title || item.display }}
    </div>
    <div v-else>
      <span v-if="index === 0">
        {{ displayKey ? item[displayKey] : item.title || item.display }}
      </span>
      <span v-if="index === 1" class="text-grey text-caption">
        (+{{ dataLength - 1 }} others)
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  item: Object,
  index: Number,
  data: [Array, Number],
  single: Boolean,
  displayKey: String,
});

const dataLength = computed(() =>
  Array.isArray(props.data) ? props.data.length : props.data
);
</script>
