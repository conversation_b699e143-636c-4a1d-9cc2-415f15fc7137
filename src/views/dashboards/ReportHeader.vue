<template>
  <div class="dashboard-header">
    <!-- Top App Bar for the report header -->
    <v-app-bar height="80" scroll-behavior="elevate" scroll-threshold="150" v-if="!smAndDown">
      <v-container fluid>
        <v-row justify="center" align="center">
          <!-- Store/Outlet Selector -->
          <!-- Allows selection of one or multiple stores -->
          <v-col cols="auto">
            <location-select multiple @location-change="$emit('location-change', $event)" />
          </v-col>

          <!-- Date Picker -->
          <v-col cols="3">
            <v-date-input v-model="localDate" label="Date" variant="outlined" density="compact" prepend-icon=""
              prepend-inner-icon="$calendar" multiple="range" color="primary" :max="new Date()" hide-details
              @update:model-value="$emit('date-change', $event)" />
          </v-col>

          <!-- Filter Button -->
          <!-- Opens filter drawer -->
          <v-col cols="auto">
            <v-btn  variant="tonal" color="primary" @click="toggleFilterDrawer">
          <v-icon class="mr-2" icon="mdi-filter-variant"></v-icon>
          <span>Filters</span>
        </v-btn>
          </v-col>

          <!-- Search Button -->
          <!-- Emits search event with loading state -->
          <v-col cols="auto">
            <v-btn color="primary" variant="flat" @click="$emit('search', true)" :loading="submitLoader">
              <span>Search</span>
              <v-icon icon="mdi-magnify" class="ml-2"></v-icon>
            </v-btn>
          </v-col>
        </v-row>
      </v-container>
    </v-app-bar>

    <!-- Divider below the app bar -->
    <v-divider v-if="!smAndDown"></v-divider>

    <v-fab v-if="smAndDown" color="primary" app extended prepend-icon="mdi-filter-variant" text="Filters"
      location="bottom center" @click="toggleFilterDrawer"></v-fab>

    <!-- Filter Drawer -->
    <!-- <filter-nav-drawer ref="filterDrawer" :tabs="tabs" :filters-data="filters"
      @apply-filter="$emit('apply-filter', $event)" /> -->

    <filter-navigation-drawer hide-default-activator ref="advancedFilter" v-model="filtersState"  />
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { useDisplay } from "vuetify";
import LocationSelect from "./LocationSelect.vue";
import FilterNavDrawer from "@/components/filters/navDrawer.vue";
import FilterNavigationDrawer from "@/components/filterSortPanel/FilterSortPanel.vue";
import ToggleFilter from "@/components/base/drawer/filters/ToggleFilter.vue";
import AutoComplete from "@/components/filters/Autocomplete.vue";

const props = defineProps({
  selectedDateFormat: { type: Object, required: true },
  dateFormatList: { type: Array, required: true },
  report: { type: Object, required: true },
  date: { type: [String, Date, Array], required: true },
  submitLoader: { type: Boolean, default: false },
});

const emit = defineEmits([
  "location-change",
  "update-date-format",
  "date-change",
  "toggle-filter",
  "search",
  "apply-filter"
]);

const { smAndDown, lgAndUp } = useDisplay();

// Local state for props to avoid direct mutation
const localDate = ref(props.date);
const localDateFormat = ref(props.selectedDateFormat);
const advancedFilter = ref(false);

// Keep local values in sync with props
watch(() => props.date, (val) => (localDate.value = val));
watch(() => props.selectedDateFormat, (val) => (localDateFormat.value = val));

// Filter drawer state
function toggleFilterDrawer() {
  advancedFilter.value.toggleFilter();
}



// Update date format and notify parent
function updateDateFormat(item) {
  localDateFormat.value = item;
  emit("update-date-format", item);
}
</script>
