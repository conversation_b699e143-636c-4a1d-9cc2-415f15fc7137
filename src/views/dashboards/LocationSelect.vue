<template>
  <!-- src/views/dashboards/LocationsSelect.vue -->
  <v-autocomplete
    v-model="locationIds"
    :clearable="!multiple"
    label="Locations"
    :items="locationList"
    item-title="name"
    item-value="id"
    hide-details
    density="compact"
    color="primary"
    variant="outlined"
    :multiple="multiple"
  >
    <!-- Selection chip / label -->
    <template v-slot:selection="{ item, index }">
      <selection-view
        :item="item"
        :index="index"
        :data="locationIds"
        :single="!multiple"
      />
    </template>

    <!-- Select All / Clear option -->
    <template v-slot:prepend-item>
      <v-list-item
        v-if="multiple"
        ripple
        @click="toggleSelectAll"
      >
        <div class="d-flex align-center px-2">
          <v-icon :icon="icon"></v-icon>
          <v-list-item-title class="mx-2">Select All</v-list-item-title>
        </div>
        <v-divider class="mt-2"></v-divider>
      </v-list-item>
    </template>
  </v-autocomplete>
</template>

<script setup>
import { computed, ref, onMounted, watch } from "vue";
import { useStoreStore } from "@/stores/store";
import SelectionView from "./SelectionView.vue";

const props = defineProps({
  multiple: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["locationChange"]);

const locationStore = useStoreStore();
const locationList = computed(() => locationStore.getStores || []);

// Selected location IDs
const locationIds = ref([]);

// State for select all
const allSelected = computed(
  () => locationIds.value.length === locationList.value.length
);
const partiallySelected = computed(
  () => locationIds.value.length > 0 && !allSelected.value
);

const icon = computed(() => {
  if (allSelected.value) return "mdi-close"; // clear
  if (partiallySelected.value) return "mdi-minus-box-outline"; // partial
  return "mdi-checkbox-blank-outline"; // none
});

// Toggle between select all / clear
const toggleSelectAll = () => {
  if (allSelected.value) {
    locationIds.value = [];
  } else {
    locationIds.value = locationList.value.map((item) => item.id);
  }
};

// Emit changes upward
watch(locationIds, (v) => {
  emit("locationChange", v);
});

// Default: select all if multiple mode, else none
onMounted(() => {
  if (props.multiple && locationList.value.length) {
    locationIds.value = locationList.value.map((item) => item.id);
  }
});
</script>
