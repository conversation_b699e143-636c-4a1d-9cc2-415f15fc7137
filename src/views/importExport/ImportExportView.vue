<template>
  <div class="import-export-list">
    <!-- List Actions Bar -->
    <list-actions-bar hide-add hide-filter @search="handleSearch" @refresh="refresh" @add="add" />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card class="border rounded-lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="importExportLogHeaders"
            :items="filteredLogs"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredLogs.length < 11"
            no-data-text="No logs found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.logs="{ item }">
              <span
                v-if="String(item.importStatus).toLowerCase() !== 'success'"
                class="cursor-pointer text-primary"
                @click="showErrors(item)"
              >Logs</span>
            </template>
            <template #item.completedAt="{ item }">
              {{
              item.completedAt
              ? formatDateTime(item.completedAt)
              : item.uploadedAt
              ? formatDateTime(item.uploadedAt)
              : "-"
              }}
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <Dialog v-model="dialog" title="Import Logs" fullscreen>
      <template #default>
        <div
          v-if="taskId || type"
          class="d-flex align-center justify-space-between mb-2 py-2 bg-primary-lighten-4 rounded"
        >
          <div>
            <strong>Task ID:</strong>
            {{ taskId ?? "-" }}
          </div>
          <div>
            <strong>Type:</strong>
            {{ type ?? "-" }}
          </div>
        </div>

        <div v-for="(mod, idx) in normalizedModules" :key="mod.moduleName + idx" class="mb-4">
          <div
            class="d-flex align-center justify-space-between mb-2 bg-secondary px-2 py-1 cursor-pointer rounded result-elem"
            @click="collapsedModules[mod.moduleName] = !collapsedModules[mod.moduleName]"
          >
            <strong>{{ mod.moduleName }}</strong>
            <div class="d-flex align-center" style="gap: 8px">
              <span>Total: {{ mod.total ?? "-" }}</span>
              <span>Success: {{ mod.success ?? "-" }}</span>
              <span>Failure: {{ mod.failure ?? "-" }}</span>
              <v-icon>{{ collapsedModules[mod.moduleName] ? 'mdi-chevron-down' : 'mdi-chevron-right' }}</v-icon>
            </div>
          </div>

          <div v-show="collapsedModules[mod.moduleName]">
            <div v-if="mod.errors && mod.errors.length">
              <table
                class="error-table"
                border="1"
                cellspacing="0"
                cellpadding="6"
                style="width: 100%; border-collapse: collapse;"
              >
                <thead>
                  <tr>
                    <th style="text-align: left;">Row Number</th>
                    <th style="text-align: left;">Row Name</th>
                    <th style="text-align: left;">Errors</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(err, errIdx) in mod.errors" :key="errIdx">
                    <td>{{ err.rowNumber ?? "-" }}</td>
                    <td>{{ err.rowName ?? "-" }}</td>
                    <td>
                      <ul style="margin: 0; padding-left: 16px;">
                        <li v-for="(msg, mIdx) in err.messages" :key="mIdx">{{ msg }}</li>
                      </ul>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else>No errors found.</div>
          </div>
        </div>
      </template>
      <template #footer></template>
    </Dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from "vue";
import { fetchImportExportLogs } from "@/services/exportService";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";

import { importExportLogHeaders } from "@/helpers/tableHeaders";
import Dialog from "@/components/base/Dialog.vue";
import sample from "@/constants/errorSample.json";

// Loading states
const loading = ref(false);
const dialog = ref(false);
const taskId = ref("");
const type = ref("");
const collapsedModules = reactive({});

// Table data
const sortBy = ref([{ key: "completedAt", order: "desc" }]);
const logs = ref([]);
const search = ref("");

const formatDateTime = isoString => {
  if (!isoString) return "-";
  const date = new Date(isoString);
  return date.toLocaleString("en-GB", {
    day: "2-digit",
    month: "short",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true
  });
};

const filteredLogs = computed(() => {
  let filtered = logs.value;
  // if (search.value && search.value.length > 2) {
  //   const lowerCaseSearch = search.value.toLowerCase();
  //   filtered = filtered.filter((log) =>
  //     log.options.toLowerCase().includes(lowerCaseSearch)
  //   );
  // }
  return filtered;
});

// Fetch logs on component mount
onMounted(() => {
  refresh();
});

// Load logs from API
// const loadLogs = async () => {
//   loading.value = true;
//   try {
//     logs.value = await fetchImportExportLogs();
//     logs.value = data.map((log) => ({
//       taskId: log.taskId,
//       accountId: log.tenantId,
//       type: log.type.charAt(0).toUpperCase() + log.type.slice(1),
//       options: Array.isArray(log.options)
//         ? log.options.join(", ")
//         : log.options,
//       // Prefer completedAt, then datetime, updatedAt, createdAt
//       datetime: formatDateTime(
//         log.completedAt || log.datetime || log.updatedAt || log.createdAt
//       ),
//       errors: log.errors || [],
//       status: log.status
//         ? typeof log.status === "string"
//           ? log.status.charAt(0).toUpperCase() + log.status.slice(1)
//           : String(log.status)
//         : "-",
//     }));
//   } catch (error) {
//     console.error("Failed to load logs:", error);
//     logs.value = [];
//   } finally {
//     loading.value = false;
//   }
// };

const handleSearch = value => {
  search.value = value;
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    logs.value = await fetchImportExportLogs();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const errorModal = ref(false);
const currentErrors = ref([]);

const showErrors = row => {
  currentErrors.value = row.result?.logs || [];
  errorModal.value = true;
  dialog.value = true;
  taskId.value = row.id;
  type.value = row.type;
};

const normalizedModules = computed(() => {
  const src = [...(currentErrors.value || sample)];

  return src.map(log => ({
    moduleName: log.sheetName || "Sheet",
    total: log.totalRecordsProcessed ?? 0,
    success: log.successCount ?? 0,
    failure: log.failureCount ?? 0,
    errors: (log.errors || []).map(row => ({
      rowNumber: row.rowNumber ?? "-",
      rowName: row.fields?.[0]?.name ?? "-",
      messages: (row.fields || []).flatMap(f => f.errors || [])
    }))
  }));
});

watch(
  normalizedModules,
  modules => {
    modules.forEach(mod => {
      if (!(mod.moduleName in collapsedModules)) {
        collapsedModules[mod.moduleName] = true; // expanded by default
      }
    });
  },
  { immediate: true } // run immediately on component mount
);

onMounted(() => {
  normalizedModules.value.forEach(mod => {
    collapsedModules[mod.moduleName] = true;
  });
});
</script>

<style scoped>
.custom-table {
  border-radius: 8px;
  overflow: hidden;
}

.result-elem {
  transition: background-color 0.2s ease;
}

.result-elem:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.parent-cont {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dense-checkbox {
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.error-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.error-table td,
.error-table th {
  border: 1px solid #ddd;
  padding: 8px;
}
</style>
