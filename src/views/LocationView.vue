<template>
  <div class="locations-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search locations"
      add-label="Location"
      hide-import-export
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
      @apply-filters="applyFilters"
    />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card class="border rounded-lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No locations found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span class="text-decoration-underline cursor-pointer" @click="edit(item.id)">
                {{
                item.locationType.toUpperCase() === "OUTLET"
                ? `${item.account.name || ""} - ${item.name}`
                : item.name
                }}
              </span>
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Location"
                :name="item.name"
                @toggle="toggleActivate(item)"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
            <template #item.actions="{ item }">
              <v-menu>
                <template #activator="{ props }">
                  <v-btn v-bind="props" variant="text" density="compact" size="medium">
                    <v-icon size="20">mdi-dots-vertical</v-icon>
                  </v-btn>
                </template>

                <v-list density="compact">
                  <v-list-item @click="viewWorkArea(item)">
                    <v-list-item-title>View Storage/Workarea</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <store-dialog
      v-model="dialog"
      :isEdit="isEdit"
      :storeData="editStoreData"
      @createStore="handleCreateStore"
      @updateStore="handleUpdateStore"
    />

    <v-dialog v-model="locationViewDialog" fullscreen>
      <v-card>
        <v-toolbar color="white">
          <v-toolbar-title
            class="ms-4"
            style="font-size: 20px; font-weight: 500"
          >{{ selectedStoreName.toUpperCase() }} Work/Storage Area</v-toolbar-title>
          <v-spacer></v-spacer>

          <v-btn
            variant="text"
            icon="mdi-close"
            color="error"
            class="mr-3"
            @click="locationViewDialog = false"
          />
        </v-toolbar>

        <v-divider />

        <v-card-text style="padding: 0 !important">
          <location-view :storeId="selectedStoreId" :fromStore="true" />
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { tableHeaders } from "@/helpers/tableHeaders";
import { useStoreStore } from "@/stores/store";
import { filterData } from "@/helpers/searchFilter";
import StoreDialog from "@/components/StoreDialog.vue";
import LocationView from "@/views/WorkArea.vue";

const storeStore = useStoreStore();

const search = ref("");
const loading = ref(false);
const headers = ref(tableHeaders["locations"]);
const sortBy = ref([{ key: "name", order: "asc" }]);

const dialog = ref(false);
const isEdit = ref(false);

const selectedStatus = ref(null);
const selectedLocationType = ref(null);

const selectStatus = status => {
  selectedStatus.value = status ?? null;
};

const selectLocationType = locationType => {
  selectedLocationType.value = locationType ?? null;
};

const items = computed(() => storeStore.getStores || []);

const selectedStoreName = computed(() => {
  const store = items.value.find(s => s.id === selectedStoreId.value);
  if (!store) return "";

  return store.locationType?.toUpperCase() === "OUTLET"
    ? `${store.account?.name || ""} - ${store.name}`
    : store.name;
});

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  if (selectedStatus.value !== null) {
    result = result.filter(item => item.activeStatus === selectedStatus.value);
  }
  if (selectedLocationType.value !== null) {
    result = result.filter(
      item => item.locationType === selectedLocationType.value
    );
  }
  return result;
});

const applyFilters = filters => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);
  if (filters.locationType !== undefined)
    selectLocationType(filters.locationType ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders["setLocations"](filters.headers);
  }
};

const filteredHeaders = computed(() => headers.value.filter(h => h.default));

const handleSearch = query => {
  search.value = query;
};

const editStoreData = ref(null);

const add = () => {
  dialog.value = true;
  isEdit.value = false;
  editStoreData.value = null;
};

const edit = async id => {
  try {
    const store = await storeStore.fetchStoreById(id);
    editStoreData.value = store;
    dialog.value = true;
    isEdit.value = true;
  } catch (error) {
    console.error(error);
  }
};

const handleCreateStore = async (store, done) => {
  try {
    await storeStore.createStore(store);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const handleUpdateStore = async (store, done) => {
  try {
    await storeStore.updateStore(store);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const toggleActivate = async ({ id }) => {
  try {
    await storeStore.updateStoreActiveStatus(id);
  } catch (error) {
    console.error(error);
  }
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await storeStore.fetchStores();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

const locationViewDialog = ref(false);
const selectedStoreId = ref(null);

const viewWorkArea = item => {
  selectedStoreId.value = item.id;
  locationViewDialog.value = true;
};

onBeforeMount(refresh);
</script>

<style scoped>
</style>
