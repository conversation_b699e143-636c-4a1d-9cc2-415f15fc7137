<template>
  <div class="vendors-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      v-model="filterState"
      search-label="Search Vendors"
      add-label="Vendor"
      sheets="Vendors"
      @refresh="refresh"
      @add="add"
      :filters="filters"
      @apply-filters="applyFilters"
    />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card class="border rounded-lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No vendors found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.name="{ item }">
              <span
                class="text-decoration-underline cursor-pointer"
                @click="edit(item.id)"
              >{{ item.name }}</span>
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="Vendor"
                :name="item.name"
                @toggle="toggleActivate(item)"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import { useRouter } from "vue-router";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { useVendorStore } from "@/stores/vendor";
import { tableHeaders } from "@/helpers/tableHeaders";
import { vendorFilters } from "@/helpers/filterConfig";
import { filterData } from "@/helpers/searchFilter";

const vendorStore = useVendorStore();
const router = useRouter();

const loading = ref(false);
const headers = ref(tableHeaders["vendors"]);
const sortBy = ref([{ key: "name", order: "asc" }]);
const filters = vendorFilters;
const filterState = ref(null);

const items = computed(() => vendorStore.getVendors || []);

const filteredItems = computed(() => {
  const query = filterState.value.search?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  const status = filterState.value.filters.status;
  if (status !== null) {
    result = result.filter(item => item.activeStatus === status);
  }
  return result;
});

const applyFilters = ({ columns }) => {
  if (columns !== undefined) {
    // to update headers using setter
    tableHeaders["setVendors"](columns);
  }
};

const filteredHeaders = computed(() => headers.value.filter(h => h.default));

const add = () => {
  router.push({ name: "Create Vendor" });
};

const edit = id => {
  router.push({ name: "Edit Vendor", params: { id } });
};

const toggleActivate = async ({ id }) => {
  try {
    await vendorStore.updateVendorActiveStatus(id);
  } catch (error) {
    console.error(error);
  }
};

const resetFilter = () => {
  filterState.value = {
    search: null,
    filters: {
      status: null
    },
    columns: headers,
    options: {}
  };
};

const refresh = async () => {
  try {
    loading.value = true;
    resetFilter();
    await vendorStore.fetchVendors();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(() => {
  refresh();
});
</script>

<style scoped>
</style>
