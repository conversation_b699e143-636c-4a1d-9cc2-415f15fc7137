<template>
  <v-container fluid>
    <!-- Row #1: Search Bar -->
    <v-row class="justify-center ma-6">
      <v-col cols="12" sm="8" md="6">
        <v-text-field
          v-model="searchQuery"
          placeholder="Search inventory, PR, PO, Transfer..."
          color="primary"
          variant="outlined"
          density="compact"
          hide-details
          clearable
          autofocus
          prepend-inner-icon="mdi-magnify"
          @keyup.enter="search"
        />
      </v-col>
    </v-row>

    <!-- Row #2: Quick Navigation Tiles -->
    <v-row class="justify-center" dense>
      <v-col cols="auto" v-for="(item, i) in quickLinks" :key="i">
        <v-btn
          stacked
          rounded="xl"
          elevation="2"
          variant="tonal"
          class="d-flex flex-column align-center justify-center text-center text-secondary"
          style="width: 100px; height: 100px"
          @click="navigate(item.path)"
        >
          <v-icon size="32" color="secondary">{{ item.icon }}</v-icon>
          <span class="mt-2 text-body-2 font-weight-medium">
            {{ item.name }}
          </span>
        </v-btn>
      </v-col>
    </v-row>

    <v-divider class="mt-5"></v-divider>

    <!-- Row #3: General Components -->
    <v-row class="justify-start mt-4">
      <v-col v-for="(comp, id) in components" :key="id" cols="12" sm="6" md="4">
        <activity-card
          :title="comp.title"
          :items="comp.items"
          :loading="comp.loading"
          :actionIcon="comp.actionIcon"
          @action="() => onAction({ componentId: id, path: comp.actionPath })"
          @viewMore="() => onViewMore(comp.viewMorePath)"
          @navigate="() => onNavigate(comp.navigatePath)"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import ActivityCard from "./ActivityCard.vue";
import { getTenantId } from "@/helpers/auth";

import axiosInstance from "@/plugin/Axios";
const axios = axiosInstance();

const env = import.meta.env;

const router = useRouter();

import { useSnackbarStore } from "@/stores/snackBar";
const snackbarStore = useSnackbarStore();

// State
const searchQuery = ref(null);

const quickLinks = [
  { icon: "mdi-file-plus", name: "Create PR", path: "/purchaseRequest/create" },
  { icon: "mdi-file-document", name: "PO's", path: "/purchaseOrder" },
  { icon: "mdi-receipt-text", name: "GRN", path: "/grn" },
  { icon: "mdi-format-list-bulleted", name: "Transfers", path: "/transfers" },
  { icon: "mdi-plus-box", name: "Transfers", path: "/transfer/create" },
  {
    icon: "mdi-database-import",
    name: "Import/Export",
    path: "/import-export",
  },
];

const components = reactive({
  poWaiting: {
    id: "poWaiting",
    title: "GRN",
    subTitle: "POs Awaiting GRN",
    items: [],
    loading: true,
    actionIcon: "mdi-clipboard-plus",
    actionPath: "/purchaseOrder",
    viewMorePath: "/purchaseOrder",
    navigatePath: "/purchaseOrder",
  },
  toDispatch: {
    id: "toDispatch",
    title: "Dispatch",
    subTitle: "Transfers to Dispatch",
    items: [],
    loading: true,
    actionIcon: "mdi-truck",
    actionPath: "/indent/create",
    viewMorePath: "/indent",
    navigatePath: "/indent/:id",
  },
  toReceive: {
    id: "toReceive",
    title: "Receive",
    subTitle: "Transfers to Receive",
    items: [],
    loading: true,
    actionIcon: "mdi-package-variant-closed-check",
    actionPath: "/indent/:id?action=receive",
    viewMorePath: "/indent",
    navigatePath: "/indent/:id",
  },
  parLevels: {
    id: "parLevels",
    title: "Par Level",
    subTitle: "Par Level Alerts",
    items: [],
    loading: true,
    actionIcon: "mdi-alert-circle",
    actionPath: "/par-level",
    viewMorePath: "/par-level",
    navigatePath: "/par-level",
  },
});

// Router helpers
const navigate = (path) => {
  if (path) router.push(path);
};

const onAction = ({ componentId, path }) => {
  console.log("Action triggered:", componentId, path);
  if (path) router.push(path);
};

const onViewMore = (path) => {
  if (path) router.push(path);
};

const onNavigate = (path) => {
  if (path) router.push(path);
};

// Streaming logic
const initStream = () => {
  const tenantId = getTenantId;
  const url = `${env.VITE_APP_URL}/tenants/${tenantId.value}/action-center`;

  let es = new EventSource(url);

  es.addEventListener("component", (ev) => {
    try {
      const msg = JSON.parse(ev.data); // { componentId, data }
      console.log({ msg });
      if (msg && msg.componentId && components[msg.componentId]) {
        let items = [];
        msg.data.forEach((d, i) => {
          items.push(normalizeItem(msg.componentId, d));
        });
        components[msg.componentId].items = items;
        components[msg.componentId].loading = false;
      }
    } catch (err) {
      console.error("invalid component event", err, ev.data);
    }
  });

  es.addEventListener("done", (ev) => {
    try {
      const info = JSON.parse(ev.data);
      console.log("stream done", info);
    } catch (_) {}
    try {
      es.close();
    } catch (e) {}
    es = null;
  });

  es.addEventListener("error", (err) => {
    console.error("stream error", err);
  });
};

// Normalize items for UI
const normalizeItem = (componentId, raw) => {
  switch (componentId) {
    case "toDispatch":
    case "toReceive":
      const subtitle = raw.from ? raw.from : raw.issuer ? raw.issuer : "";
      return {
        appendIcon: components[componentId].actionIcon,
        subtitle: `Requester: <span class="text-primary">${subtitle}</span> &mdash; Issuer: <span class="text-primary">${raw.to}</span>`,
        id: raw.id || raw.transNumber || Math.random().toString(36).slice(2),
        title: raw.transNumber || raw.id,
        subtitle: raw.from
          ? `${raw.from} → ${raw.to || ""}`
          : raw.issuer
          ? `Issued by: ${raw.issuer}`
          : "",
        meta: raw,
        actionPath: `/transfer/${raw.id}`,
        query: componentId === "toDispatch" ? "dispatch" : "receive",
      };
    case "parLevels":
      return {
        appendIcon: components[componentId].actionIcon,
        id: raw.id,
        title: `${raw.name || raw.itemName || raw.label}${
          raw.plu ? ` (${raw.plu})` : ""
        }`,
        subtitle: `Current: ${raw.currentQty ?? "-"} • Reorder: ${
          raw.reorderQty ?? raw.reorder ?? "-"
        }`,
        meta: raw,
      };
    case "poWaiting":
      return {
        appendIcon: components[componentId].actionIcon,
        id: raw.id,
        title: raw.poNumber || raw.id,
        subtitle: `Requester: ${raw.requester || "-"}`,
        meta: raw,
        actionPath: `/purchaseOrder/${raw.id}`,
      };
    default:
      return {
        appendIcon: components[componentId].actionIcon,
        id: raw.id || Math.random().toString(36).slice(2),
        title: raw.id,
        subtitle: JSON.stringify(raw),
        meta: raw,
      };
  }
};

const search = async () => {
  try {
    const response = await axios.get(
      `action-center/search?number=${searchQuery.value}`
    );

    const result = response.data.result;
    if (!result) {
      snackbarStore.showSnackbar("error", "No results found");
      return;
    }

    switch (response.data.type) {
      case "PR":
        router.push({ path: `/purchaseRequest/${result.id}` });
        break;
      case "PO":
        router.push({ path: `/purchaseOrder/${result.id}` });
        break;
      case "T":
        router.push({ path: `/transfer/${result.id}` });
        break;
      case "GRN":
        router.push({ path: `/grn/${result.id}` });
        break;
      default:
        snackbarStore.showSnackbar("error", "invalid search type");
    }
  } catch (response) {
    snackbarStore.showSnackbar("error", response.data.message);
  }
};

// Lifecycle
onMounted(() => {
  initStream();
});
</script>
