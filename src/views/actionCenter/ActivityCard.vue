<template>
  <v-card
    class="mx-auto"
    :prepend-icon="actionIcon"
    :title="title"
    :subtitle="subTitle"
  >
    <!-- Top bar -->
    <template v-slot:title>
      <div
        class="d-flex align-center justify-space-between w-100 text-secondary"
      >
        <!-- Title -->
        <div class="text-h6">
          {{ searchActive ? " " : title }}
        </div>

        <!-- Search field -->
        <v-text-field
          v-model="search"
          color="primary"
          variant="outlined"
          density="compact"
          :placeholder="searchActive ? 'Search...' : ''"
          hide-details
          clearable
          autofocus
          class="transition-width"
          :class="{ expanded: searchActive }"
          @blur="shrinkSearch"
          @click:clear="shrinkSearch"
          @focus="searchActive = true"
        >
          <template v-slot:append-inner>
            <v-icon icon="mdi-magnify" />
          </template>
        </v-text-field>
      </div>
    </template>

    <!-- Divider -->
    <v-divider />

    <!-- Body -->
    <v-card-text class="bg-surface-light pa-1">
      <v-list density="compact">
        <div v-for="(item, index) in filteredItems" :key="index">
          <v-list-item
            :title="item.title"
            :subtitle="item.subtitle"
            @click="onAction(item)"
          >
            <template v-slot:append>
              <v-icon :icon="actionIcon" size="small" color="primary" />
            </template>
          </v-list-item>

          <!-- Divider between items -->
          <v-divider v-if="index < filteredItems.length - 1" />
        </div>
      </v-list>
    </v-card-text>

    <!-- Footer Action -->
    <!-- <template v-slot:actions>
      <v-btn
        append-icon="mdi-chevron-right"
        color="primary"
        :text="footerLabel"
        block
        @click="onViewMore"
      />
    </template> -->
  </v-card>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";

const props = defineProps({
  title: String,
  subTitle: String,
  items: { type: Array, default: () => [] },
  loading: Boolean,
  actionIcon: String,
  path: { type: [String, Object], default: null },
  componentId: String,
});

const router = useRouter();

const searchActive = ref(false);
const search = ref("");
const footerLabel = `View ${props.title}`;
const expandSearch = () => {
  searchActive.value = true;
};

const shrinkSearch = () => {
  if (!search.value) searchActive.value = false;
};

const filteredItems = computed(() => {
  if (!search.value) return props.items;
  return props.items.filter((i) =>
    (i.title || "").toLowerCase().includes(search.value.toLowerCase())
  );
});

const onAction = (item) => {
  if (!item.actionPath) return;
  router.push({
    path: item.actionPath,
    query: { type: item.query },
  });
};

const onViewMore = () => {
  if (props.path) {
    router.push({
      path: props.path,
      query: { componentId: props.componentId },
    });
  }
};
</script>

<style scoped>
.transition-width {
  transition: all 0.3s ease;
  width: 0;
  max-width: 50px;
  overflow: hidden;
}
.transition-width.expanded {
  width: 100%;
  max-width: 200px;
}
</style>
