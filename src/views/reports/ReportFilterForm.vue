<template>
  <v-dialog v-model="dialog" max-width="1000" persistent scrollable>
    <v-card style="height: 95vh; overflow-y: auto">
      <v-card-title>
        <v-row align="center" justify="space-between">
          <v-col cols="auto mx-6 mt-2">
            <span class="">Report Filters</span>
          </v-col>
          <v-col cols="auto">
            <v-btn
              icon="mdi-close"
              variant="plain"
              @click="dialog = false"
              color="primary"
            ></v-btn>
          </v-col>
        </v-row>
      </v-card-title>

      <v-card-text>
        <v-form ref="form">
          <v-row class="mx-2">
            <v-col cols="12" class="pa-2">
              <v-autocomplete
                v-model="selectedColumns"
                :items="columnOptions"
                label="Columns"
                multiple
                chips
                closable-chips
                color="primary"
                hide-details="auto"
                variant="outlined"
                density="compact"
                :menu-props="{ contentClass: 'custom-select-menu' }"
                clearable
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-divider></v-divider>

      <v-card-actions class="px-4">
        <v-spacer></v-spacer>
        <v-btn text="Save" @click="save" variant="flat" color="primary"></v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { reportColumns } from "@/constants/list";

const dialog = defineModel(); // Enables v-model

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  title: {
    type: String,
    required: true,
  },
  unitConversions: {
    type: Array,
    default: () => [],
  },
  tenantId: {
    type: String,
    required: true,
  },
});

const selectedColumns = ref([]);

const columnOptions = computed(() => {
  const match = reportColumns.find(
    (r) => r.reportType === props.record.reportType
  );
  return match ? match.options : [];
});

watch(
  () => props.record.reportType,
  (newType) => {
    const match = reportColumns.find((r) => r.reportType === newType);
    selectedColumns.value = match ? [...match.options] : [];
  },
  { immediate: true }
);
</script>

<style scoped>
/* Remove background and text color styling for selected list items */
.custom-select-menu .v-list-item--active,
.custom-select-menu .v-list-item--active:hover {
  background-color: transparent !important;
  color: inherit !important;
}

</style>
