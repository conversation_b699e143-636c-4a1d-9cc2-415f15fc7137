<template>
  <div class="users-list">
    <!-- List Actions Bar -->
    <list-actions-bar
      search-label="Search users"
      add-label="Invite user"
      hide-import-export
      @search="handleSearch"
      @refresh="refresh"
      @add="add"
      hide-filter
      @apply-filters="applyFilters"
    />

    <!-- Main content -->
    <v-container fluid>
      <v-row no-gutters>
        <v-card class="border rounded-lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="filteredHeaders"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No users found"
          >
            <!-- Loading Skeleton -->
            <template #loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template #loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->
            <template #item.roleName="{ item }">
              <span v-if="item.isAdmin">
                <v-chip label size="small" color="primary">Admin</v-chip>
              </span>

              <span v-else class="text-decoration cursor-pointer">
                {{ item.roleName }}
                <v-btn icon variant="flat" @click="edit(item.id)">
                  <v-icon icon="mdi-pencil-box-outline" />
                  <v-tooltip activator="parent" location="top">Edit role</v-tooltip>
                </v-btn>
              </span>
            </template>
            <template #item.verified="{ item }">
              <v-chip
                label
                size="small"
                :color="item.verified ? 'green' : 'error'"
              >{{item.verified ? "Verified" : "Not Verified" }}</v-chip>
              <v-btn v-if="!item.verified" icon variant="flat" @click="resendInvite(item)">
                <v-icon icon="mdi-email-sync-outline" />
                <v-tooltip activator="parent" location="top">Resend invite</v-tooltip>
              </v-btn>
            </template>
            <template #item.activeStatus="{ item, column }">
              <StatusToggle
                :status="item.activeStatus"
                entity="User"
                :name="item.name"
                @toggle="toggleActivate(item)"
                :class="`d-flex justify-${column.align}`"
              />
            </template>
          </v-data-table>
        </v-card>
      </v-row>
    </v-container>

    <user-dialog
      v-model="dialog"
      :isEdit="isEdit"
      :userData="editUserData"
      :stores="stores"
      :locations="locations"
      @createUser="handleCreateUser"
      @updateUser="handleUpdateUser"
    />
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import StatusToggle from "@/components/utils/StatusToggle.vue";

import { useUserStore } from "@/stores/user";
import { tableHeaders } from "@/helpers/tableHeaders";
import { filterData } from "@/helpers/searchFilter";
import UserDialog from "@/components/UserDialog.vue";
import { useSnackbarStore } from "../stores/snackBar";
import axiosInstance from "@/plugin/Axios";
import globalData from "@/composables/global";
import { useLocationStore } from "@/stores/location";
import { useStoreStore } from "@/stores/store";

const axios = axiosInstance();
const snackbarStore = useSnackbarStore();

const userStore = useUserStore();
const storeStore = useStoreStore();
const locationStore = useLocationStore();

const stores = computed(() => storeStore.getStores);
const locations = computed(() => locationStore.getLocations);

const dialog = ref(false);
const search = ref(null);
const loading = ref(false);
const headers = ref(tableHeaders["users"]);
const sortBy = ref([{ key: "emailId", order: "asc" }]);

const isEdit = ref(false);

const selectedStatus = ref(null);

const selectStatus = status => {
  selectedStatus.value = status ?? null;
};

const items = computed(() => userStore.getUsers || []);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  if (selectedStatus.value !== null) {
    result = result.filter(item => item.activeStatus === selectedStatus.value);
  }
  return result;
});

const applyFilters = filters => {
  if (filters.status !== undefined) selectStatus(filters.status ?? null);

  if (filters.headers !== undefined) {
    // to update headers using setter
    tableHeaders["setUsers"](filters.headers);
  }
};

const filteredHeaders = computed(() => headers.value.filter(h => h.default));

const editUserData = ref(null);

const add = () => {
  dialog.value = true;
  isEdit.value = false;
  editUserData.value = null;
};

const edit = async id => {
  try {
    const user = await userStore.fetchUserById(id);
    editUserData.value = user;
    dialog.value = true;
    isEdit.value = true;
  } catch (error) {
    console.error(error);
  }
};

const handleCreateUser = async (user, done) => {
  try {
    await userStore.createUser(user);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const handleUpdateUser = async (user, done) => {
  try {
    await userStore.updateUser(user);
    await refresh();
    done(true);
  } catch (error) {
    console.error(error);
    done(false);
  }
};

const toggleActivate = async ({ id }) => {
  try {
    await userStore.updateUserActiveStatus(id);
  } catch (error) {
    console.error(error);
  }
};

const resendInvite = async item => {
  // call auth api post "sso/invite-user"
  try {
    await axios.post(
      `/sso/invite-user`,
      {
        email: item.emailId,
        userId: item.id,
        clientId: "inventory",
        tenantId: item.tenantId,
        tenantName: item.tenantName
      },
      { baseURL: globalData.$authServerUrl }
    );
    snackbarStore.showSnackbar(
      "green",
      "Invitation sent successfully. Please check your email."
    );
  } catch (error) {
    console.error(error);
    snackbarStore.showSnackbar(
      "error",
      error.message || "Failed to send invitation."
    );
  }
};

const handleSearch = v => {
  search.value = v;
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await userStore.fetchUsers();
    await storeStore.fetchStores();
    await locationStore.fetchLocations();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(async () => {
  refresh();
});
</script>

<style scoped></style>
