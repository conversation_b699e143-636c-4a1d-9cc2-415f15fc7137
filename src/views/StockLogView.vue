<template>
  <div>
    <div class="mx-4 mt-4">
      <div class="d-flex align-end">
        <v-autocomplete
          v-model="selectedStore"
          :items="stores"
          item-value="id"
          item-title="name"
          label="Location"
          hide-details
          variant="outlined"
          density="compact"
          color="primary"
          class="mr-3"
          @update:model-value="handleStoreChange"
          clearable
        ></v-autocomplete>

        <v-autocomplete
          v-model="selectedLocation"
          :items="locations"
          item-value="id"
          item-title="name"
          label="Work/Storage Area"
          hide-details
          variant="outlined"
          density="compact"
          color="primary"
          class="mr-3"
          return-object
          clearable
        ></v-autocomplete>

        <v-autocomplete
          v-model="selectedItem"
          :items="itemsData"
          item-value="id"
          item-title="itemName"
          label="Item Name"
          hide-details
          variant="outlined"
          density="compact"
          color="primary"
          class="mr-3"
          clearable
        ></v-autocomplete>

        <v-autocomplete
          v-model="selectedType"
          :items="logType"
          label="Type"
          color="primary"
          hide-details
          variant="outlined"
          density="compact"
          class="mr-3"
          :disabled="typeDisabled"
          clearable
        ></v-autocomplete>

        <v-spacer></v-spacer>

        <v-btn
          variant="tonal"
          color="primary"
          @click="searchStock"
          :disabled="!selectedStore || !selectedLocation"
        >
          <v-icon icon="mdi-magnify" size="20"></v-icon>SEARCH
        </v-btn>
      </div>
    </div>
    <v-divider class="mt-4"></v-divider>

    <v-container fluid v-if="showTable">
      <v-row>
        <v-card class="parent-cont ma-3 mb-0" width="100%">
          <v-container fluid class="pa-0 parent-cont">
            <v-data-table
              :headers="stockLogHeaders"
              :items="loading ? [] : filteredItems"
              hide-default-footer
              :loading="loading"
              :no-data-text="
                loading ? 'Loading stock logs...' : 'No stock logs found'
              "
              :items-per-page="20"
            >
              <template #item.unit="{ item }">
                <span v-if="item.inventoryItem.countingUnit"
                  >{{ item.inventoryItem.countingUnit.symbol }}
                </span>
                <span v-else>{{ item.inventoryItem.uom }} </span>
              </template>

              <template #item.createdAt="{ item }">
                {{ formatTimestamp(item.createdAt) }}
              </template>
            </v-data-table>
          </v-container>
          <div class="text-center pt-2">
            <v-pagination
              v-model="page"
              :length="pageCount"
              @update:modelValue="onPageChange"
              active-color="primary"
            ></v-pagination>
          </div>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed, watch } from "vue";
import { useStockStore } from "@/stores/stock";
import { useLocationStore } from "@/stores/location";
import { useStoreStore } from "@/stores/store";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { stockLogHeaders } from "@/helpers/tableHeaders";

const stockStore = useStockStore();
const locationStore = useLocationStore();
const storeStore = useStoreStore();
const inventoryStore = useInventoryItemStore();

const selectedStore = ref(null);
const selectedLocation = ref(null);
const selectedItem = ref(null);
const selectedType = ref(null);

const items = ref([]);
const logType = ref([]);
const loading = ref(false);
const showTable = ref(false);
const typeDisabled = ref(true);

const page = ref(1);
const rowsPerPage = ref(20);
const totalItems = ref(0);

const stores = computed(() => storeStore.getStores || []);
const locations = computed(() => locationStore.getLocations || []);
const itemsData = computed(() => inventoryStore.getInventoryItems || []);

function formatTimestamp(timestamp) {
  if (!timestamp || !timestamp._seconds) return "-";
  const date = new Date(timestamp._seconds * 1000);
  return new Intl.DateTimeFormat("en-IN", {
    year: "numeric",
    month: "short",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  }).format(date);
}

const fetchLogs = async () => {
  loading.value = true;
  try {
    await stockStore.fetchStockLogs({
      locationId: selectedLocation.value.id,
      itemId: selectedItem.value,
      page: page.value,
      limit: rowsPerPage.value,
    });
    const data  = stockStore.getStockLogs ?? {}
    items.value = data;
    // totalItems.value = totalLength;
    const types = [...new Set(data.map((l) => l.logType))];
    logType.value = ["All", ...types];
    selectedType.value = selectedType.value ?? "All";
    typeDisabled.value = !types.length;
  } catch (e) {
    console.error(e);
  } finally {
    loading.value = false;
  }
};

const searchStock = async () => {
  if (!selectedStore.value || !selectedLocation.value) return;
  page.value = 1;
  await fetchLogs();
  showTable.value = true;
};

const onPageChange = async () => {
  await fetchLogs();
};

const pageCount = computed(() => {
  return Math.ceil(totalItems.value / rowsPerPage.value);
});

const filteredItems = computed(() =>
  selectedType.value === "All" || !selectedType.value
    ? items.value
    : items.value.filter((log) => log.logType === selectedType.value)
);

const handleStoreChange = async (storeId) => {
  await locationStore.fetchLocationsByStore(storeId);

  selectedLocation.value = locations.value[0] || null;

  typeDisabled.value = true;
  logType.value = [];
  selectedType.value = null;
  showTable.value = false;
  if (selectedLocation.value) {
    page.value = 1;
    await fetchLogs();
    showTable.value = true;
  }
};

watch([selectedStore, selectedLocation, selectedItem], () => {
  typeDisabled.value = true;
  selectedType.value = null;
  logType.value = [];
});

onBeforeMount(async () => {
  try {
    loading.value = true;
    await Promise.all([
      storeStore.fetchStores(),
      inventoryStore.fetchInventoryItems(),
    ]);
    selectedStore.value = stores.value[0]?.id;
    await handleStoreChange(selectedStore.value);
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
});
</script>
