<template>
  <v-container v-if="!loader" max-width="1000">
    <v-card-text>
      <v-form ref="form">
        <v-row>
          <v-col cols="4">
            <v-text-field
              v-model.trim="record.name"
              label="Recipe Name*"
              variant="outlined"
              density="compact"
              hide-details="auto"
              color="primary"
              @blur="cleanName"
              :rules="[rules.require, rules.maxLength(100)]"
            />
          </v-col>
          <v-col cols="4">
            <v-text-field
              v-model.trim="record.recipeCode"
              label="Recipe Code"
              variant="outlined"
              density="compact"
              hide-details="auto"
              color="primary"
              :rules="[rules.minLength(4), rules.maxLength(20)]"
            />
          </v-col>
          <v-col cols="4">
            <v-text-field
              v-model.number="record.quantity"
              label="Quantity*"
              type="number"
              variant="outlined"
              density="compact"
              hide-details="auto"
              color="primary"
              @keydown.up.prevent
              @keydown.down.prevent
              :rules="[rules.require, rules.quantity, rules.positive]"
            />
          </v-col>
          <v-col cols="4">
            <v-autocomplete
              v-model="record.recipeUnit"
              label="Recipe Unit*"
              color="primary"
              hide-details="auto"
              variant="outlined"
              density="compact"
              :items="purchaseUnits"
              item-title="name"
              item-value="symbol"
              :rules="[rules.require, rules.unit]"
              clearable
            >
              <template v-slot:item="{ props, item }">
                <v-list-item
                  v-bind="props"
                  :title="`${item.raw.name} (${item.raw.symbol})`"
                >
                </v-list-item>
              </template>
              <template #selection="{ item }">
                {{ item.raw.name }} ({{ item.raw.symbol }})
              </template>
            </v-autocomplete>
          </v-col>
          <v-col cols="4">
            <v-autocomplete
              v-model="record.recipeType"
              :items="recipeTypes"
              label="Recipe Type*"
              variant="outlined"
              density="compact"
              hide-details="auto"
              color="primary"
              item-title="name"
              item-value="id"
              :rules="[rules.require]"
              clearable
            />
          </v-col>

          <v-col cols="4">
            <v-autocomplete
              v-model="record.tags"
              label="Tags"
              color="primary"
              hide-details="auto"
              variant="outlined"
              density="compact"
              :items="tags"
              multiple
              item-title="name"
              item-value="id"
              return-object
              clearable
            >
              <template v-slot:selection="{ item, index }">
                <selection-view
                  :item="item"
                  :index="index"
                  :data="record.tags"
                  :single="false"
                ></selection-view>
              </template>
              <template v-slot:prepend-item>
                <v-list-item ripple @click="toggleTags">
                  <template v-slot:default>
                    <div class="d-flex align-center px-2">
                      <v-icon :icon="icon"></v-icon>
                      <v-list-item-title class="mx-2">
                        Select All
                      </v-list-item-title>
                    </div>
                    <v-divider class="mt-2"></v-divider>
                  </template>
                </v-list-item>
              </template>
            </v-autocomplete>
          </v-col>

          <v-col cols="4">
            <currency-input
              v-model="record.cost"
              label="Cost"
              @valueChange="(v) => (record.cost = v)"
              readonly
            ></currency-input>
          </v-col>

          <v-col cols="12">
            <h3 class="mb-4">
              <v-icon icon="mdi-food"></v-icon>
              <span class="mx-2">Ingredients</span>
            </h3>
            <ingredient-table
              v-model="ingredients"
              :inventoryList="filteredInventoryItems"
            ></ingredient-table>
          </v-col>
          <!-- <v-col cols="12">
            <v-row>
              <v-col cols="7">
                <v-table density="compact" class="border">
                  <thead>
                    <tr>
                      <th class="text-left" style="width: 5%">#</th>
                      <th class="text-center" style="width: 75%">Step</th>
                      <th class="text-end" style="width: 20%">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(step, index) in record.cookingProcedure"
                      :key="index"
                    >
                      <td>{{ index + 1 }}</td>
                      <td>
                        <v-text-field
                          v-model="record.cookingProcedure[index]"
                          density="compact"
                          hide-details
                          variant="underlined"
                          color="primary"
                          class="mt-0 pt-0"
                        />
                      </td>
                      <td>
                        <div class="text-end">
                          <v-btn
                            icon="mdi-delete"
                            color="red"
                            variant="text"
                            @click="removeProcedure(index)"
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </v-table>
              </v-col>

              <v-col cols="5">
                <v-textarea
                  v-model="newStep"
                  label="New Step"
                  auto-grow
                  variant="outlined"
                  density="compact"
                  hide-details="auto"
                  color="primary"
                  @keydown.enter="addProcedure"
                />
                <v-btn
                  text="Add Step"
                  class="mt-2"
                  color="primary"
                  block
                  :disabled="!newStep.trim()"
                  @click="addProcedure"
                />
              </v-col>
            </v-row>
          </v-col> -->
        </v-row>
      </v-form>
    </v-card-text>
    <v-divider class="mx-4" />

    <v-card-actions class="px-4 pt-4">
      <p class="text-primary">* Indicates a Required Field.</p>
      <v-spacer />
      <v-btn @click="navigatePrevious" variant="outlined" color="primary"
        ><v-icon icon="mdi-close"></v-icon>Close</v-btn
      >

      <v-btn
        text="Save"
        @click="save"
        :loading="isLoading"
        :disabled="isLoading"
        variant="flat"
        color="primary"
      />
    </v-card-actions>
  </v-container>
  <v-container
    v-else
    class="d-flex justify-center align-center"
    style="height: 100%"
  >
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate></v-progress-circular>
    </div>
  </v-container>
</template>

<script setup>
import { ref, onBeforeMount, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";
import { recipeRecord as DEFAULT_RECORD } from "@/helpers/defaultRecords";
import { useReceipeStore } from "@/stores/receipe";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { recipeTypes } from "@/constants/list";
import IngredientTable from "@/components/recipeIngredientTable.vue";
import { useHouseUnitStore } from "@/stores/houseUnit";
import { formatName } from "@/helpers/formatter";
import CurrencyInput from "@/components/CurrencyInput.vue";
import { calculateRecipeCost } from "@/helpers/cost";
import { useTagStore } from "@/stores/tag";
import SelectionView from "@/components/base/SelectionView.vue";

const route = useRoute();
const router = useRouter();
const receipeStore = useReceipeStore();
const inventoryItemStore = useInventoryItemStore();
const houseUnitStore = useHouseUnitStore();
const receipeId = route.params.id;
const isEdit = receipeId !== undefined;
const tagStore = useTagStore();

const form = ref(null);
const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));
const ingredients = ref([]);
const inventoryList = computed(() => inventoryItemStore.getInventoryItems);
const receipeList = computed(() => receipeStore.getReceipes || []);

const filteredInventoryItems = computed(() => {
  const selectedIds = ingredients.value.map((i) => i.id);

  // Take only subReceipe type
  const subReceipes = receipeList.value
    .filter((r) => r.recipeType === "subRecipe" && r.id !== record.value.id)
    .map((i) => {
      return { ...i, itemName: i.name };
    });

  // Combine inventory + subReceipes
  const combined = [...inventoryList.value, ...subReceipes];

  return combined.filter((item) => !selectedIds.includes(item.id));
});

const purchaseUnits = computed(() => houseUnitStore.getHouseUnits);

const loader = ref(false);
const isLoading = ref(false);

const selectAllStores = computed(
  () => (record.value.tags?.length || 0) === tags.value.length
);

const selectSomeStore = computed(
  () => (record.value.tags?.length || 0) > 0 && !selectAllStores.value
);

const icon = computed(() => {
  const tagsLength = record.value.tags?.length || 0;
  const allTagsLength = tags.value.length;

  if (tagsLength === allTagsLength) return "mdi-close";
  if (tagsLength > 0) return "mdi-minus-box-outline";
  return "mdi-checkbox-blank-outline";
});

const toggleTags = () => {
  if (selectAllStores.value) return (record.value.tags = []);
  selectAllTags();
};

const selectAllTags = () => {
  record.value.tags = [];

  if (tags.value.length) {
    tags.value.forEach((item) => {
      record.value.tags.push(item);
    });
  }
};

const tags = computed(() =>
  tagStore.getTags
    .filter((tag) => tag.activeStatus)
    .map((tag) => ({ id: tag.id, name: tag.name }))
);

// const addProcedure = () => {
//   if (newStep.value.trim()) {
//     record.value.cookingProcedure.push(newStep.value.trim());
//     newStep.value = "";
//   }
// };

// const removeProcedure = (index) => {
//   record.value.cookingProcedure.splice(index, 1);
// };

onBeforeMount(async () => {
  loader.value = true;

  try {
    await tagStore.fetchTags();
    const inventoryItemPromise = inventoryItemStore.fetchInventoryItems();
    const houseUnitPromise = houseUnitStore.fetchHouseUnitsBytenant();
    const receipePromise = receipeStore.fetchReceipes();

    await Promise.all([houseUnitPromise, inventoryItemPromise, receipePromise]);

    if (isEdit) {
      const result = await receipeStore.fetchReceipeById(receipeId);
      record.value = result;
    }
    ingredients.value = record.value.ingredients?.map((v) => ({
      ...v,
      name: v.itemName,
      code: v.itemCode,
      id: v.itemId,
    }));

    loader.value = false;
  } catch (error) {
    console.log(error);
  }
});

const save = async () => {
  if (isLoading.value) return;
  isLoading.value = true;

  try {
    const { valid } = await form.value.validate();
    if (!valid) {
      isLoading.value = false;
      return;
    }

    const payload = {
      ...record.value,
      tags: record.value.tags?.map((t) => ({ id: t.id, name: t.name })) || [],
      ingredients: ingredients.value?.map((v) => ({
        itemId: v.id,
        itemName: v.name,
        itemCode: v.code,
        quantity: v.quantity,
        purchaseUnit: v.purchaseUnit,
        countingUnit: v.countingUnit,
        recipeUnit: v.recipeUnit,
        unitCost: v.unitCost,
      })),
    };

    let itemId;

    if (isEdit) {
      await receipeStore.updateReceipe(payload);
    } else {
      const createdItem = await receipeStore.createReceipe(payload);
      itemId = createdItem?.id;
    }

    const redirect = (name, id, type, accountId) => {
      router.push({
        name,
        params: { id },
        query: {
          createdItemId: itemId,
          createdItemType: type,
          accountId: accountId,
        },
      });
    };

    const { menuItemId, modifierId, menuItemType, modifierType, accountId } =
      route.query;

    if (menuItemId) {
      return redirect("Edit Menu Item", menuItemId, menuItemType, accountId);
    }

    if (modifierId) {
      return redirect("Edit Modifier", modifierId, modifierType, accountId);
    }

    navigatePrevious();
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
};

const navigatePrevious = () => {
  const { menuItemId, modifierId, accountId } = route.query;

  if (menuItemId) {
    return router.push({
      name: "Edit Menu Item",
      params: { id: menuItemId },
      query: { accountId },
    });
  }

  if (modifierId) {
    return router.push({
      name: "Edit Modifier",
      params: { id: modifierId },
      query: { accountId },
    });
  }

  router.push({ name: "recipes" });
};

const cleanName = () => {
  record.value.name = formatName(record.value.name);
};

const totalCost = computed(() => {
  if (!ingredients.value.length) return 0;
  return ingredients.value.reduce((acc, item) => {
    if (item.recipeType && item.recipeType == "subReceipe") {
      return (
        acc +
        calculateRecipeCost(
          {
            unitCost: item.unitCost,
            recipeUnit: item.recipeUnit,
          },
          item.quantity
        )
      );
    }
    return acc + calculateRecipeCost(item, item.quantity);
  }, 0);
});

watch(totalCost, (v) => {
  // if (!ingredients.value.length) return;
  record.value.cost = v;
});
</script>

<style></style>
