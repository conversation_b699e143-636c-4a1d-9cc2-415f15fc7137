<template>
  <div class="inventory-list">
    <!-- List Actions Bar -->
     <list-actions-bar
      search-label="Search Items"
      add-label="WorkArea"
      sheets="Inventory Items"
      @search="handleSearch"
      @refresh="refresh"
      @apply-filters="applyFilters"
      hide-add
    >
      <template v-slot:filter>
        <v-autocomplete
          min-width="250"
          v-model="selectedLocation"
          :items="locations"
          item-value="id"
          item-title="name"
          label="WorkArea"
          hide-details
          variant="outlined"
          density="compact"
          color="primary"
          clearable
        ></v-autocomplete>
      </template>
    </list-actions-bar>

    <v-container fluid>
      <v-row no-gutters>
        <v-card class="border rounded-lg" width="100%">
          <v-data-table
            class="table-bordered"
            :headers="headers"
            :items="filteredItems"
            :loading="loading"
            :sort-by="sortBy"
            :hide-default-footer="filteredItems.length < 11"
            no-data-text="No inventory items found"
          >
            <!-- Loading Skeleton -->
            <template v-slot:loading>
              <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
            </template>
            <!-- Loader -->
            <template v-slot:loader>
              <v-progress-linear :height="2" indeterminate color="primary"></v-progress-linear>
            </template>

            <!-- content -->     
            <template #item.countingUnit="{ item }">{{ item.countingUnit.name }}</template>
            <template #item.category="{ item }">{{ item.category.name }}</template>
            <template #item.subCategory="{ item }">{{ item.subCategory.name }}</template>

            <template #item.quantity="{ item }">
              <v-text-field
                  v-model.number="item.quantity"
                  type="number"
                  density="compact"
                  variant="outlined"
                  hide-details
                  color="primary"
                  :min= 0
                  @focus="onFocus(item, 'quantity')"
                  @blur="onBlur(item, 'quantity')"
                  @keypress="preventKeys"
                >
                <template v-slot:append-inner>
                  {{ item.countingUnit.symbol }}
                </template>
              </v-text-field>
            </template>

          </v-data-table>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script setup>
import { onBeforeMount, ref, computed, watch } from "vue";
import { useRouter } from "vue-router";

import ListActionsBar from "@/components/utils/ListActionsBar.vue";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { useLocationStore } from "@/stores/location";
import { tableHeaders } from "@/helpers/tableHeaders";
import { filterData } from "@/helpers/searchFilter";

const inventoryItemStore = useInventoryItemStore();
const locationStore = useLocationStore();
const router = useRouter();

const search = ref(null);
const selectedLocation = ref(null);
const loading = ref(false);
const headers = ref(tableHeaders["closingHeaders"]);
const sortBy = ref([{ key: "itemName", order: "asc" }]);

const items = computed(() => inventoryItemStore.getInventoryItems);
const locations = computed(() => locationStore.getLocations);

const filteredItems = computed(() => {
  const query = search.value?.toLowerCase() || "";
  let result = query ? filterData(items.value, query) : items.value;
  return result;
});

const preventKeys = (event) => {
  if (event.key === '-' || event.key === '+' || event.key === 'e') {
    event.preventDefault();
  }
};

const onFocus = (item, value) => {
  if (Number(item[value]) === 0) {
    item[value] = null;
  }
};

const onBlur = (item, value) => {
  if (item[value] === null || item[value] === "") {
    item[value] = 0;
  }
};

const applyFilters = filters => {
};

const handleSearch = v => {
  search.value = v;
};

const refresh = async () => {
  try {
    loading.value = true;
    search.value = null;
    await inventoryItemStore.fetchInventoryItems();
    await locationStore.fetchLocations();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => items.value,
  (newItems) => {
    newItems.forEach(item => {
      if (!('quantity' in item)) item.quantity = 0;
    });
  },
  { immediate: true }
);

onBeforeMount(() => {
  refresh();
});
</script>

<style scoped>
</style>
