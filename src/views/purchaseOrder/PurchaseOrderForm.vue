<template>
  <v-container v-if="!loader" fluid class="pt-2">
    <v-card-actions class="px-4 pb-4">
      <p>
        PO No: <span class="font-weight-bold">{{ poDetails[0].value }}</span>
      </p>
      <v-spacer />
      <v-btn @click="navigatePrevious" variant="outlined" color="primary"
        ><v-icon>mdi-close</v-icon>Close</v-btn
      >

      <v-btn
        text="Print"
        @click="printPO"
        variant="flat"
        :loading="loadingPrint"
        :disabled="loadingPrint"
        color="primary"
      />

      <v-btn
        text="Send PO Copy"
        @click="() => sendEmail()"
        variant="flat"
        :loading="loadingEmail"
        :disabled="loadingEmail"
        color="primary"
      />

      <v-btn
        v-if="!isApproved && !isClosed"
        :text="`Approve PO`"
        @click="submitApproval"
        variant="flat"
        color="primary"
      />

      <v-btn
        v-if="!isApproved && !isClosed"
        :text="`Reject PO`"
        variant="flat"
        color="primary"
        @click="reasonDialog = true"
      />

      <v-btn
        v-if="isApproved && !isClosed"
        text="Create GRN"
        :loading="loadingGrn"
        :disabled="loadingGrn"
        @click="checkSubmit"
        variant="flat"
        color="primary"
      />
    </v-card-actions>
    <v-divider class="mx-4" />

    <v-card-text class="pt-4 scrollable-content">
      <v-form ref="form">
        <v-row class="d-flex">
          <!-- Vendor Details Card -->
          <v-col cols="6">
            <v-expansion-panels>
              <v-expansion-panel>
                <v-expansion-panel-title class="text-primary font-weight-bold">
                  Vendor Details ({{ vendorFields.Name }})
                </v-expansion-panel-title>
                <v-expansion-panel-text>
                  <v-row
                    v-for="(value, label) in vendorFields"
                    :key="label"
                    justify="center"
                    align="baseline"
                    class="pa-1"
                  >
                    <!-- Label -->
                    <v-col
                      cols="4"
                      class="text-body-2 grey--text text--darken-1 px-2 py-0"
                    >
                      {{ label }}
                    </v-col>

                    <!-- Value -->
                    <v-col
                      cols="8"
                      class="text-body-1 font-weight-medium text-black pa-0"
                    >
                      {{ value ? value : "-" }}
                    </v-col>
                  </v-row>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-col>

          <v-col cols="6">
            <v-expansion-panels>
              <v-expansion-panel>
                <v-expansion-panel-title class="text-primary font-weight-bold">
                  PO Details ({{ poDetails[0].value }})
                </v-expansion-panel-title>
                <v-expansion-panel-text>
                  <v-row
                    v-for="(item, label) in poDetails"
                    :key="label"
                    justify="center"
                    align="baseline"
                    class="pa-1"
                  >
                    <!-- Label -->
                    <v-col
                      cols="4"
                      class="text-body-2 grey--text text--darken-1 px-2 py-0"
                    >
                      {{ item.label }}
                    </v-col>

                    <!-- Value -->
                    <v-col
                      cols="8"
                      class="text-body-1 font-weight-medium text-black pa-0"
                    >
                      {{ item.value ? item.value : "-" }}
                    </v-col>
                  </v-row>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-col>

          <v-col cols="12" sm="6" md="3" v-if="isApproved">
            <v-date-input
              v-model="record.goodsReceivedDate"
              label="GRN Date"
              color="primary"
              :readonly="isClosed"
              :min="today"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon=""
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>

          <v-col cols="12" sm="6" md="3" v-if="isApproved">
            <v-date-input
              v-model="record.vendorInvoiceDate"
              label="Vendor Invoice Date"
              color="primary"
              :readonly="isClosed"
              :min="today"
              variant="outlined"
              density="compact"
              hide-details="auto"
              prepend-icon=""
              prepend-inner-icon="$calendar"
              :rules="[rules.require]"
              hide-actions
            ></v-date-input>
          </v-col>

          <v-col cols="12" sm="6" md="3" v-if="isApproved">
            <v-text-field
              v-model="record.vendorInvoiceNumber"
              label="Invoice No"
              hide-details="auto"
              variant="outlined"
              density="compact"
              color="primary"
              :rules="[rules.require]"
              :readonly="isClosed"
            ></v-text-field>
          </v-col>

          <v-divider
            class="mx-3 mb-3"
            :class="{ 'mt-3': isClosed }"
          ></v-divider>
          <v-col cols="12" v-if="!isClosed">
            <order-item-table
              v-model="record.items"
              :headers="poHeaders"
              :default-row="DEFAULT_PURCHASE_ITEM"
              :inventory-list="filteredInventoryItems"
              :view-only="true"
            ></order-item-table>
          </v-col>
          <v-col v-else>
            <ClosedPurchaseItemTable
              :items="record.items"
              :headers="closedOrderItemHeaders"
            ></ClosedPurchaseItemTable>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-container>
  <v-container
    v-else
    class="d-flex justify-center align-center"
    style="height: 100%"
  >
    <div class="text-center">
      <v-progress-circular color="primary" indeterminate></v-progress-circular>
    </div>
  </v-container>
  <ReasonDialog
    v-if="reasonDialog"
    v-model="reasonDialog"
    @reject="reject"
  ></ReasonDialog>
  <submit-dialog
    v-if="dialog"
    v-model="dialog"
    @selectOption="submit"
  ></submit-dialog>
  <grn-dialog
    v-model="grnDialog"
    :grn="createdGrn || {}"
    :items="record.items"
    :headers="poHeaders"
    :default-row="DEFAULT_PURCHASE_ITEM"
    :inventory-list="filteredInventoryItems"
    :view-only="true"
  />
</template>

<script setup>
import { onBeforeMount, ref, computed } from "vue";
import {
  purchaseOrderItemHeaders,
  closedOrderItemHeaders,
} from "@/helpers/tableHeaders";
import {
  purchaseOrderItemRecord as DEFAULT_PURCHASE_ITEM,
  purchaseRequestRecord as DEFAULT_RECORD,
} from "@/helpers/defaultRecords";
import orderItemTable from "@/components/purchase/orderItemTable.vue";
import { useRoute, useRouter } from "vue-router";
import rules from "@/helpers/rules";

import { useLocationStore } from "@/stores/location";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import { usePurchaseOrderStore } from "@/stores/purchaseOrder";
import { useGrnStore } from "@/stores/goodsReceivedNote";
import { usePrintStore } from "@/stores/print";
import { ConvertIOStoDateTime } from "@/helpers/date";
import ReasonDialog from "@/components/base/ReasonDialog.vue";
import SubmitDialog from "./SubmitDialog.vue";
import ClosedPurchaseItemTable from "@/components/purchase/closedPurchaseItemTable.vue";
import GrnDialog from "@/components/GrnDialog.vue";

const locationStore = useLocationStore();
const inventoryStore = useInventoryItemStore();
const purchaseOrderStore = usePurchaseOrderStore();
const grnStore = useGrnStore();
const printStore = usePrintStore();

const router = useRouter();
const route = useRoute();
const form = ref(null);
const dialog = ref(false);
const reasonDialog = ref(false);
const grnDialog = ref(false);
const createdGrn = ref(null);

const record = ref(JSON.parse(JSON.stringify(DEFAULT_RECORD)));

const purchaseOrderId = route.params.id;
const isEdit = purchaseOrderId !== undefined;
const loader = ref(false);
const today = new Date().toISOString().split("T")[0];

const loadingPrint = ref(false);
const loadingEmail = ref(false);
const loadingGrn = ref(false);

const inventoryItems = computed(() => inventoryStore.getInventoryItems || []);

const filteredInventoryItems = computed(() => {
  const selectedIds = record.value.items.map((i) => i.itemId);
  return inventoryItems.value.filter((item) => !selectedIds.includes(item.id));
});

record.value.location = computed(() => locationStore.getSelectedLocation);

const isApproved = computed(() =>
  record.value.statusTimeline?.some((item) => item.name === "approved")
);

const isClosed = computed(() =>
  record.value.statusTimeline?.some(
    (item) => item.name == "completed" || item.name === "rejected"
  )
);

const poHeaders = computed(() => {
  if (!isApproved.value || record.status == "completed")
    return purchaseOrderItemHeaders;
  const headersList = [...purchaseOrderItemHeaders];
  headersList.splice(3, 0, {
    title: "Received Quantity",
    key: "receivedQty",
    width: "12%",
    align: "start",
  });
  return headersList;
});

// const isClosed = computed(() => record.value.status == "completed");

const vendorFields = ref({});
const poDetails = ref([]);
const navigatePrevious = () => {
  router.push({ name: "Purchase Order (PO)" });
};

const handleClear = () => {
  if (!isClosed) record.vendorInvoiceDate = null;
};
const submit = async (type) => {
  const {
    id,
    goodsReceivedDate,
    vendorInvoiceDate,
    vendorInvoiceNumber,
    requestedBy,
    items,
  } = record.value;

  const payload = {
    poId: id,
    grnDate: goodsReceivedDate,
    invoiceDate: vendorInvoiceDate,
    invoiceNumber: vendorInvoiceNumber,
    receivedById: requestedBy.id,
    receivedByName: requestedBy.name,
    grnItems: items.map((item) => ({
      receivedQty: item.receivedQty,
      unitCost: item.unitCost,
      purchaseUOM: item.purchaseUOM,
      expiryDate: item.expiryDate,
      remarks: item.remarks,
      itemId: item.itemId,
    })),
    poOption: type,
  };

  const response = await grnStore.createGrn(payload);

  createdGrn.value = response?.result;
  grnDialog.value = true;
};

const submitApproval = async () => {
  await purchaseOrderStore.approvePurchaseOrder(record.value.id);
  navigatePrevious();
};

const printPO = async () => {
  try {
    loadingPrint.value = true;
    const poNumber = record.value.poNumber;
    await printStore.print("po", poNumber);
  } finally {
    loadingPrint.value = false;
  }
};

const sendEmail = async () => {
  try {
    loadingEmail.value = true;
    const poNumber = record.value.poNumber;
    await printStore.sendEmail("po", poNumber);
  } finally {
    loadingEmail.value = false;
  }
};

const checkSubmit = async () => {
  try {
    if (loadingGrn.value) return;

    const { valid } = await form.value.validate();
    if (!valid) return;

    const isAllQuantityMatched = record.value.items.every(
      (item) => item.quantity <= item.receivedQty
    );

    loadingGrn.value = true;

    if (isAllQuantityMatched) {
      await submit(3);
    } else {
      dialog.value = true;
    }
  } finally {
    loadingGrn.value = false;
  }
};

const reject = async (reason) => {
  await purchaseOrderStore.rejectPurchaseOrder(record.value.id, {
    rejectedReason: reason,
  });
  navigatePrevious();
};

onBeforeMount(async () => {
  loader.value = true;

  try {
    await Promise.all([
      locationStore.fetchLocations(),
      inventoryStore.fetchInventoryItems(),
    ]);

    if (isEdit) {
      const result = await purchaseOrderStore.fetchPurchaseOrderById(
        purchaseOrderId
      );
      record.value = { ...result };
      record.value.goodsReceivedDate = new Date(result.goodsReceivedDate);
      record.value.vendorInvoiceDate = new Date(result.vendorInvoiceDate);

      const {
        name: Name,
        contactNo: Contact,
        contactEmailId: Email,
        panNo: PAN,
        gstNo: GST,
        address,
      } = record.value.vendor;

      vendorFields.value = {
        Name,
        Contact,
        Email,
        PAN,
        GST,
        Address: `${address.address}, ${address.city} - ${address.pincode}`,
      };
      poDetails.value = [
        {
          label: "PO No",
          value: record.value.poNumber,
        },
        {
          label: "Date",
          value: ConvertIOStoDateTime(record.value.statusTimeline[0].time),
        },
        {
          label: "PR No",
          value: record.value.prNumber,
        },
      ];
    }

    loader.value = false;
  } catch (err) {
    console.error(err);
  } finally {
  }
});
</script>

<style>
.scrollable-content {
  overflow-y: auto;
  max-height: calc(100vh - 150px); /* Adjust if needed */
  padding-right: 16px; /* avoid scrollbar overlap */
}
</style>
