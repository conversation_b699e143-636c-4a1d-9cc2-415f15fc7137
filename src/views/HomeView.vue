<template>
  <v-app class="bg-white">
    <!-- Side Navigation -->
    <nav-drawer v-model="toggleNav"></nav-drawer>

    <!--    Top Navigation -->
    <ToolBar>
      <v-app-bar-nav-icon variant="text" @click.stop="toggleNav = !toggleNav"></v-app-bar-nav-icon>
    </ToolBar>

    <!-- Main Content -->
    <v-main>
      <router-view></router-view>
    </v-main>
  </v-app>
</template>

<script setup>
import ToolBar from "@/components/base/ToolBar.vue";
import NavDrawer from "@/components/base/NavDrawer.vue";
import { ref } from "vue";

const toggleNav = ref(true);

</script>