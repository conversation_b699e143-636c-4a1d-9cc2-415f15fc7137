<template>
  <div class="role-page">
    <v-card-actions class="fixed-actions px-4 pb-4">
      <v-row>
        <v-col cols="8">
          <v-text-field
            v-model="roleName"
            label="Role Name"
            color="primary"
            hide-details="auto"
            variant="outlined"
            density="compact"
            :rules="[rules.require]"
            class="text-left"
          />
        </v-col>
      </v-row>
      <v-spacer />
      <v-btn @click="navigatePrevious" variant="outlined" color="primary">
        <v-icon>mdi-close</v-icon>Close
      </v-btn>
      <v-btn
        :disabled="!roleName.trim()"
        text="Save"
        @click="onSave"
        variant="flat"
        color="primary"
      />
    </v-card-actions>

    <v-divider />

    <v-container fluid class="scrollable-content">
      <v-row>
        <v-card class="parent-cont ma-3 mb-0" width="100%">
          <v-container fluid grid-list-lg class="pa-0">
            <v-data-table
              :headers="rolePrivilegeHeaders"
              :items="privileges"
              item-value="name"
              :expanded="expandedRows"
              hide-default-footer
              :loading="loading"
              :no-data-text="loading ? 'Loading privileges...' : 'No privileges found'"
            >
              <template v-slot:item="{ item }">
                <tr class="bg-red-lighten-5">
                  <td>
                    <v-icon
                      size="small"
                      class="expand-icon mr-2 cursor-pointer"
                      @click.stop="toggleExpand(item.name)"
                    >
                      {{
                        expandedRows.includes(item.name)
                          ? "mdi-chevron-down"
                          : "mdi-chevron-right"
                      }}
                    </v-icon>
                  </td>
                  <td class="pl-8 d-flex align-center">
                    <span
                      class="cursor-pointer font-weight-bold text-subtitle-2"
                      @click="toggleExpand(item.name)"
                    >
                      {{ item.name }}
                    </span>
                  </td>
                  <td>All</td>

                  <td class="text-end">
                    <div class="d-inline-flex">
                      <v-switch
                        class="small-switch"
                        v-model="item.checked"
                        :color="item.checked ? 'green' : 'grey'"
                        inset
                        density="compact"
                        hide-details
                        @update:model-value="onGroupToggle(item)"
                      />
                    </div>
                  </td>
                </tr>

                <tr
                  v-for="(priv, index) in item.privileges"
                  :key="`${item.name}-${index}`"
                  v-if="expandedRows.includes(item.name)"
                  class="subcategory-item"
                >
                <td></td>
                  <td class="pl-12">{{ priv.label }}</td>

                  <td>{{ priv.description }}</td>

                  <td class="text-end">
                    <div class="d-inline-flex">
                      <v-switch
                        class="small-switch"
                        v-model="priv.checked"
                        :color="priv.checked ? 'green' : 'grey'"
                        inset
                        density="compact"
                        hide-details
                        @update:model-value="onPrivilegeToggle(priv, item)"
                      />
                    </div>
                  </td>
                </tr>

              </template>
            </v-data-table>
          </v-container>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>


<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import rules from "@/helpers/rules";
import {
  getPrivileges,
  getRoleById,
  updateRole,
  createRole,
} from "@/services/roleService";
import { useSnackbarStore } from "@/stores/snackBar";
import { rolePrivilegeHeaders } from "@/helpers/tableHeaders";

const roleName = ref("");
const privileges = ref([]);
const loading = ref(false);
const expandedRows = ref([]);

const router = useRouter();
const route = useRoute();
const isEdit = computed(() => route.name === "Edit Role" && !!route.params.id);
const snackbarStore = useSnackbarStore();

const allRow = (group) => ({
  code: "ALL",
  label: `All ${group.name} Access`,
  description: `Grants all permissions under ${group.name}.`,
  checked: group.checked,
});

const fetchPrivileges = async () => {
  try {
    loading.value = true;
    const res = await getPrivileges();
    if (res.status === "success" && res.payload) {
      privileges.value = Object.entries(res.payload).map(([name, privs]) => ({
        name,
        checked: false,
        privileges: privs.map((p) => ({ ...p, checked: false })),
      }));

      expandedRows.value = privileges.value.map(g => g.name);
    }
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

const fetchRole = async (roleId) => {
  try {
    loading.value = true;
    const res = await getRoleById(roleId);
    if (res.status === "success" && res.payload) {
      roleName.value = res.payload.name;

      privileges.value.forEach(group => {
        group.checked = group.privileges.every(p => res.payload.privileges.includes(p.code));
        group.privileges.forEach(p => {
          p.checked = res.payload.privileges.includes(p.code);
        });
      });

      expandedRows.value = privileges.value.map(g => g.name);
    }
  } catch (err) {
    console.error(err);
  } finally {
    loading.value = false;
  }
};

const toggleExpand = (groupName) => {
  if (expandedRows.value.includes(groupName)) {
    expandedRows.value = expandedRows.value.filter((n) => n !== groupName);
  } else {
    expandedRows.value.push(groupName);
  }
};

const onGroupToggle = (group) => {
  group.privileges.forEach((p) => (p.checked = group.checked));
};

const onPrivilegeToggle = (priv, group) => {
  if (priv.code === "ALL") {
    group.checked = priv.checked;
    group.privileges.forEach((p) => (p.checked = priv.checked));
    return;
  }
  group.checked = group.privileges.every((p) => p.checked);
};

const navigatePrevious = () => {
  router.back();
};

const onSave = async () => {
  loading.value = true;
  const selectedPrivileges = privileges.value.flatMap((g) =>
    g.privileges.filter((p) => p.checked).map((p) => p.code)
  );

  try {
    const payload = {
      name: roleName.value,
      tenantId: localStorage.getItem("_tenantId"),
      privileges: selectedPrivileges,
    };

    const res = isEdit.value
      ? await updateRole(route.params.id, payload)
      : await createRole(payload);

    snackbarStore.showSnackbar(
      res.status === "success" ? "success" : "error",
      res.message || (isEdit.value ? "Role update failed" : "Role creation failed")
    );

    if (res.status === "success") router.push({ name: "roles" });
  } catch (err) {
    snackbarStore.showSnackbar("error", err.message || "Something went wrong");
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  await fetchPrivileges();

  if (isEdit.value) {
    await fetchRole(route.params.id);
  } else {
    expandedRows.value = privileges.value.map(g => g.name);
  }
});

</script>

<style scoped>
.small-switch {
  transform: scale(0.8);
  transform-origin: left end;
}

.role-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.fixed-actions {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  padding-top: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ddd;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding-top: 16px;
}

.subcategory-item {
  border-bottom: 1px solid #eee;
  padding: 4px 0;
}
</style>
