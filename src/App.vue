<template>
  <main>
    <v-snackbar
      v-model="store.snackbar.show"
      :color="store.snackbar.color"
      :timeout="store.snackbar.timeout"
      location="top right"
      transition="scroll-y-transition"
    >
      {{ store.snackbar.message }}
      <template v-slot:actions>
        <v-btn flat @click="store.hideSnackbar" icon="mdi-close"> </v-btn>
      </template>
    </v-snackbar>
    <loader></loader>

    <!-- Global Confirm Dialog -->
    <ConfirmDialog ref="confirmRef" />

    <router-view></router-view>
  </main>
</template>

<script setup>
import { ref, getCurrentInstance, nextTick } from "vue";
import ConfirmDialog from "@/components/utils/ConfirmDialog.vue";

import { useSnackbarStore } from "./stores/snackBar";
import loader from "./components/base/loader.vue";

const store = useSnackbarStore();

const confirmRef = ref(null);

const app = getCurrentInstance().appContext.app;

nextTick(() => {
  if (confirmRef.value) {
    // Expose globally
    app.config.globalProperties.$confirm = confirmRef.value.open;
    app.provide("confirm", confirmRef.value.open);
  } else {
    console.error("ConfirmDialog ref is null after nextTick");
  }
});
</script>

<style>
/* For Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}
</style>
