// import your tab components
import FiltersTab from "@/components/base/drawer/tabs/FiltersTab.vue";
import SplitByTab from "@/components/base/drawer/tabs/SplitByTab.vue";
import ColumnsTab from "@/components/base/drawer/tabs/ColumnsTab.vue";
import SortOptionsTab from "@/components/base/drawer/tabs/SortOptionsTab.vue";

//filters
import LocationFilter from "@/components/base/drawer/filters/LocationFilter.vue";
import CategoryFilter from "@/components/base/drawer/filters/CategoryFilter.vue";
import InventoryItemFilter from "@/components/base/drawer/filters/InventoryItemFilter.vue";
import RadioGroupFilter from "@/components/base/drawer/filters/RadioGroupFilter.vue";
import ToggleFilter from "@/components/base/drawer/filters/ToggleFilter.vue";
import WorkAreaFilter from "@/components/base/drawer/filters/WorkAreaFilter.vue";

const filters = {
  status: {
    component: RadioGroupFilter,
    title: "Status",
    key: "status",
    items: [
      { name: "All", value: null },
      { name: "Active", value: true },
      { name: "In-active", value: false },
    ],
  },
  statusWithDate: {
    component: ToggleFilter,
    title: "Status",
    key: "status",
    items: [
      { name: "In progress", value: "pending" },
      { name: "Completed", value: "completed" },
    ],
  },
  issuerFilter: {
    component: WorkAreaFilter,
    title: "Issuer",
    key: "issuer",
    items: [], // to be populated dynamically
  },
  receiveFilter: {
    component: WorkAreaFilter,
    title: "Requester",
    key: "requester",
    items: [], // to be populated dynamically
  },
  locationType: {
    component: RadioGroupFilter,
    title: "Location Type",
    key: "locationType",
    items: [
      { name: "All", value: null },
      { name: "Outlet", value: "OUTLET" },
      { name: "Central Kitchen", value: "CENTRAL KITCHEN" },
      { name: "Central Warehouse", value: "CENTRAL WAREHOUSE" },
    ],
  },
  unitType: {
    component: RadioGroupFilter,
    title: "Unit Type",
    key: "unitType",
    items: [
      { name: "All", value: null },
      { name: "Mass", value: "Mass" },
      { name: "Volume", value: "Volume" },
    ],
  },
  itemType: {
    component: RadioGroupFilter,
    title: "Item Type",
    key: "itemType",
    items: [
      { name: "All", value: null },
      { name: "Bought", value: "bought" },
      { name: "Made", value: "made" },
    ],
  },
  recipeType: {
    component: RadioGroupFilter,
    title: "Recipe Type",
    key: "recipeType",
    items: [
      { name: "All", value: null },
      { name: "Recipe", value: "recipe" },
      { name: "SubRecipe", value: "subRecipe" },
    ],
  },
};

export const tabConfig = {
  // key = route name (or screen name)
  dashboard: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [
        { component: LocationFilter, ...LocationFilter },
        { component: CategoryFilter, ...CategoryFilter },
        InventoryItemFilter,
      ],
    },
    { value: 2, label: "Split by", component: SplitByTab },
    { value: 3, label: "Sort options", component: SortOptionsTab },
  ],
  locations: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [filters.status, filters.locationType],
    },
    { value: 2, label: "columns", component: ColumnsTab },
  ],
  workArea: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [filters.status],
    },
    { value: 2, label: "columns", component: ColumnsTab },
  ],
  vendors: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [filters.status],
    },
    { value: 2, label: "columns", component: ColumnsTab },
  ],
  categories: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [filters.status],
    },
    { value: 2, label: "columns", component: ColumnsTab },
  ],
  houseUnits: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [filters.status, filters.unitType],
    },
    { value: 2, label: "columns", component: ColumnsTab },
  ],
  inventoryItems: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [filters.status, filters.itemType],
    },
    { value: 2, label: "columns", component: ColumnsTab },
  ],
  recipes: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [filters.status, filters.recipeType],
    },
    { value: 2, label: "columns", component: ColumnsTab },
  ],
  users: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [filters.status],
    },
    { value: 2, label: "columns", component: ColumnsTab },
  ],
  roles: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [filters.status],
    },
    { value: 2, label: "columns", component: ColumnsTab },
  ],
  transfers: [
    {
      value: 1,
      label: "filters",
      component: FiltersTab,
      filters: [
        filters.statusWithDate,
        filters.issuerFilter,
        filters.receiveFilter,
      ],
    },
  ],
};
