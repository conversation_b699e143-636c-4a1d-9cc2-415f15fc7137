import { ref } from "vue";

export const tableHeaders = {
  vendors: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      default: true,
    },
    {
      title: "Vendor Id",
      key: "vendorId",
      align: "start",
      sortable: true,
      mandatory: false,
      default: true,
    },
    {
      title: "Contact Name",
      key: "contactName",
      align: "start",
      sortable: true,
      mandatory: false,
      default: true,
    },
    {
      title: "Contact No",
      key: "contactNo",
      align: "start",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Email",
      key: "contactEmailId",
      align: "start",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "CIN",
      key: "cinNo",
      align: "start",
      sortable: false,
      class: "text-center",
      mandatory: false,
      default: false,
    },
    {
      title: "GST",
      key: "gstNo",
      align: "start",
      sortable: false,
      mandatory: false,
      default: false,
    },
    {
      title: "PAN",
      key: "panNo",
      align: "start",
      sortable: false,
      mandatory: false,
      default: false,
    },
    {
      title: "TIN",
      key: "tinNo",
      align: "start",
      sortable: false,
      mandatory: false,
      default: false,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
  ]),
  setVendors(newHeaders) {
    this.vendors.value = newHeaders;
  },
  categories: ref([
    {
      title: "Category Name",
      key: "name",
      align: "start",
      mandatory: true,
      default: true,
    },
    {
      key: "data-table-expand", // optional, to keep it as short as possible
      align: "end",
      mandatory: true,
      default: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
  ]),
  setCategories(newHeaders) {
    this.categories.value = newHeaders;
  },
  houseUnits: ref([
    {
      title: "Unit Name",
      key: "name",
      align: "left",
      sortable: true,
      mandatory: true,
      default: true,
    },
    {
      title: "Unit Symbol",
      key: "symbol",
      align: "center",
      sortable: false,
      mandatory: true,
      default: true,
    },
    {
      title: "Unit Type",
      key: "unitType",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Conversions",
      key: "conversions",
      align: "center",
      sortable: false,
      mandatory: true,
      default: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
  ]),
  setHouseUnits(newHeaders) {
    this.houseUnits.value = newHeaders;
  },
  inventoryItems: ref([
    {
      title: "Item Name",
      key: "itemName",
      align: "start",
      sortable: true,
      mandatory: true,
      default: true,
    },
    {
      title: "Item Code",
      key: "itemCode",
      align: "center",
      sortable: false,
      mandatory: true,
      default: true,
    },
    {
      title: "Item Type",
      key: "itemType",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Category",
      key: "category",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Sub Category",
      key: "subCategory",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Vendors",
      key: "vendors",
      align: "center",
      sortable: false,
      mandatory: false,
      default: false,
    },
    {
      title: "Purchase Unit",
      key: "purchaseUnit",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Counting Unit",
      key: "countingUnit",
      align: "center",
      sortable: false,
      mandatory: false,
      default: false,
    },
    {
      title: "Recipe Unit",
      key: "recipeUnit",
      align: "center",
      sortable: false,
      mandatory: false,
      default: false,
    },
    {
      title: "Unit Cost",
      key: "unitCost",
      align: "center",
      sortable: false,
      mandatory: false,
      default: false,
    },
    {
      title: "Par Level",
      key: "parLevel",
      align: "center",
      sortable: false,
      mandatory: false,
      default: false,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
    // {
    //   title: "Actions",
    //   key: "actions",
    //   align: "end",
    //   sortable: false,
    //   mandatory: false,
    //   default: true,
    // },
  ]),
  setInventoryItems(newHeaders) {
    this.inventoryItems.value = newHeaders;
  },
  recipes: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      default: true,
    },
    {
      title: "Recipe Code",
      key: "recipeCode",
      align: "center",
      sortable: false,
      mandatory: true,
      default: true,
    },
    {
      title: "Type",
      key: "recipeType",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Unit",
      key: "recipeUnit",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Cost",
      key: "cost",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
  ]),
  setRecipes(newHeaders) {
    this.recipes.value = newHeaders;
  },
  locations: ref([
    {
      title: "",
      key: "actions",
      align: "center",
      sortable: true,
      mandatory: true,
      default: true,
    },
    {
      title: "Display Name",
      key: "name",
      align: "start",
      sortable: false,
      mandatory: true,
      default: true,
    },
    {
      title: "Location Type",
      key: "locationType",
      align: "center",
      sortable: false,
      mandatory: true,
      default: true,
    },
    {
      title: "Pos Id",
      key: "posId",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "PAN No.",
      key: "panNo",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "GST No.",
      key: "gstNo",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Billing Address",
      key: "billTo",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Shipping Address",
      key: "shipTo",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
  ]),
  setLocations(newHeaders) {
    this.locations.value = newHeaders;
  },
  workArea: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      default: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
  ]),
  setWorkArea(newHeaders) {
    this.workArea.value = newHeaders;
  },
  tags: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      default: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
  ]),
  setTags(newHeaders) {
    this.tags.value = newHeaders;
  },
  users: ref([
    {
      title: "Email",
      key: "emailId",
      align: "start",
      sortable: true,
      mandatory: false,
      default: true,
    },
    {
      title: "Name",
      key: "name",
      align: "center",
      mandatory: true,
      default: true,
    },
    {
      title: "Role",
      key: "roleName",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Verified",
      key: "verified",
      align: "center",
      mandatory: true,
      default: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
  ]),
  setUsers(newHeaders) {
    this.users.value = newHeaders;
  },
  roles: ref([
    {
      title: "Name",
      key: "name",
      align: "start",
      sortable: true,
      mandatory: true,
      default: true,
    },
    {
      title: "Privileges",
      key: "privileges",
      align: "center",
      sortable: false,
      mandatory: true,
      default: true,
    },
    {
      title: "Status",
      key: "activeStatus",
      align: "end",
      sortable: false,
      mandatory: true,
      default: true,
    },
  ]),
  setRoles(newHeaders) {
    this.roles.value = newHeaders;
  },

  closingHeaders: ref([
    {
      title: "Item Name",
      key: "itemName",
      align: "start",
      sortable: true,
      mandatory: true,
      default: true,
    },
    {
      title: "Item Code",
      key: "itemCode",
      align: "center",
      sortable: false,
      mandatory: true,
      default: true,
    },
    {
      title: "Item Type",
      key: "itemType",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Category",
      key: "category",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Sub Category",
      key: "subCategory",
      align: "center",
      sortable: false,
      mandatory: false,
      default: true,
    },
    {
      title: "Counting Unit",
      key: "countingUnit",
      align: "center",
      sortable: false,
      mandatory: false,
      default: false,
    },
    {
      title: "Quantity",
      key: "quantity",
      align: "center",
      sortable: false,
      mandatory: false,
      default: false,
    },
  ]),
  setClosingHeaders(newHeaders) {
    this.closingHeaders.value = newHeaders;
  },
};

export const rolePrivilegeHeaders = [
  {
    title: "",
    key: "dropdown",
    align: "start",
    sortable: false,
  },
  {
    title: "Label",
    key: "label",
    align: "start",
    sortable: true,
  },
  {
    title: "Description",
    key: "description",
    align: "start",
    sortable: false,
  },
  {
    title: "Access",
    key: "actions",
    align: "end",
    sortable: false,
  },
];

export const importExportLogHeaders = [
  {
    title: "Task Id",
    key: "id",
    align: "start",
    sortable: false,
  },
  {
    title: "Date & Time",
    key: "completedAt",
    align: "center",
    sortable: true,
  },
  {
    title: "Type",
    key: "type",
    align: "center",
    sortable: false,
  },
  {
    title: "File Name",
    key: "fileName",
    align: "center",
    sortable: true,
  },
  {
    title: "Uploaded By",
    key: "uploadedByName",
    align: "center",
    sortable: true,
  },
  {
    title: "Request Status",
    key: "requestStatus",
    align: "center",
    sortable: false,
  },
  {
    title: "Import Status",
    key: "importStatus",
    align: "center",
    sortable: false,
  },
  {
    title: "Errors",
    key: "logs",
    align: "center",
    sortable: false,
  },
];

export const conversionHeaders = [
  {
    title: "Quantity",
    key: "quantity",
    width: "40%",
    align: "left",
    type: "number",
    unit: true,
  },
  {
    title: "Unit Name",
    key: "name",
    width: "40%",
    align: "start",
    type: "text",
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "10%",
  },
];

export const packageHeaders = [
  { title: "Name", key: "name", width: "40%", align: "start" },
  {
    title: "Code",
    key: "packageCode",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "15%",
    align: "center",
    sortable: false,
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    width: "15%",
    align: "center",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "10%",
  },
];

export const ingredientHeaders = [
  {
    title: "Name",
    key: "name",
    width: "40%",
    align: "start",
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "10%",
  },
];

export const recipeIngredientHeaders = [
  {
    title: "Name",
    key: "name",
    width: "40%",
    align: "start",
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Rate",
    key: "rate",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "10%",
  },
];

export const stockHeaders = [
  { title: "Item Name", key: "itemName", align: "start" },
  { title: "Item Code", key: "itemCode", align: "start" },
  { title: "Quantity", key: "qty", align: "end" },
  { title: "Unit", key: "uom", align: "start" },
  { title: "Location", key: "locationName", align: "start" },
  { title: "Work/Storage Area", key: "inventoryLocationName", align: "end" },
];

export const stockLogHeaders = [
  { title: "Location", key: "locationName", align: "start" },
  { title: "Work/Storage Area", key: "inventoryLocationName", align: "start" },
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
  },
  { title: "Type", key: "type", align: "center", sortable: false },
  { title: "Quantity", key: "qty", align: "end", sortable: false },
  {
    title: "Unit",
    key: "uom",
    align: "start",
    sortable: false,
  },
  { title: "Created At", key: "createdAt", align: "center", sortable: false },
  { title: "Summary", key: "summary", align: "center", sortable: false },
];

export const prepareHeaders = [
  { title: "Item Name", key: "name", align: "start", sortable: false },
  { title: "Required", key: "required", align: "center", sortable: false },
  {
    title: "Available Quantity",
    key: "availableQuantity",
    align: "center",
    sortable: false,
  },
  { title: "Reason", key: "reason", align: "center", sortable: false },
];

export const purchaseRequestHeaders = [
  {
    title: "PR No",
    key: "prNumber",
    align: "start",
    sortable: true,
  },
  {
    title: "Vendor",
    key: "vendor",
    align: "start",
    sortable: false,
  },

  {
    title: "Created At",
    key: "createdAt",
    align: "start",
    sortable: false,
  },

  {
    title: "Delivery Date",
    key: "deliveryDate",
    align: "start",
    sortable: false,
  },
  {
    title: "Total Price",
    key: "totalAmount",
    align: "end",
    sortable: false,
  },
  { title: "Status", key: "status", align: "end", sortable: false },
];

export const purchaseOrderHeaders = [
  {
    title: "PO No",
    key: "poNumber",
    align: "start",
    sortable: true,
  },
  {
    title: "Vendor",
    key: "vendor",
    align: "start",
    sortable: false,
  },
  {
    title: "Created At",
    key: "createdAt",
    align: "start",
    sortable: false,
  },
  {
    title: "Total Price",
    key: "totalAmount",
    align: "end",
    sortable: false,
  },
  { title: "Status", key: "status", align: "end", sortable: false },
];

export const purchaseRequestItemHeaders = [
  {
    title: "",
    key: "index",
    sortable: false,
    width: "5%",
    align: "center",
  },
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: true,
  },
  {
    title: "Vendor",
    key: "vendor.name",
    align: "start",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    align: "end",
    width: "10%",
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    align: "end",
    width: "10%",
  },
  {
    title: "Discount Amt",
    key: "discount",
    align: "end",
    width: "10%",
  },
  {
    title: "Tax Rate",
    key: "taxRate",
    align: "end",
    width: "10%",
  },
  {
    title: "Tax Amt",
    key: "taxAmount",
    align: "end",
    width: "10%",
  },
  {
    title: "Total Price",
    key: "totalPrice",
    align: "end",
    width: "10%",
  },
  {
    key: "actions",
    sortable: false,
    align: "center",
    width: "5%",
  },
];

export const purchaseOrderItemHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    width: "12%",
    align: "start",
  },
  {
    title: "Item Code",
    key: "itemCode",
    width: "12%",
    align: "start",
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "12%",
    align: "start",
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    width: "12%",
    align: "start",
  },
  {
    title: "Tax Rate",
    key: "taxRate",
    width: "12%",
    align: "start",
  },
  {
    title: "Total Price",
    key: "totalPrice",
    width: "12%",
    align: "start",
  },
  {
    title: "Actions",
    key: "actions",
    sortable: false,
    align: "center",
    width: "10%",
  },
];

export const GRNHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    width: "40%",
    align: "start",
    sortable: false,
  },
  {
    title: "Item Code",
    key: "itemCode",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "receivedQuantity",
    width: "20%",
    align: "end",
    sortable: false,
  },
  {
    title: "Total Price",
    key: "totalPrice",
    width: "20%",
    align: "end",
    sortable: false,
  },
];

export const menuItemHeaders = [
  { title: "Item Name", key: "itemName", align: "start", sortable: false },
  { title: "Item Code", key: "itemCode", align: "center", sortable: false },
  { title: "Status", key: "activeStatus", align: "center", sortable: false },
  {
    title: "Linking Status",
    key: "linkingStatus",
    align: "center",
    sortable: false,
  },
  { title: "Actions", key: "actions", align: "end", sortable: false },
];

export const menuItemIngredientHeaders = [
  { title: "Item Name", key: "itemName", align: "start", sortable: false },
  {
    title: "Item Type",
    key: "selectedItemType",
    align: "center",
    sortable: false,
  },
  { title: "Items", key: "selectedItemName", align: "center", sortable: false },
  {
    title: "Serving Quantity",
    key: "servingSizeName",
    align: "center",
    sortable: false,
  },
];

export const grnListHeaders = [
  {
    title: "GRN No",
    key: "grnNumber",
    align: "start",
    sortable: false,
  },
  {
    title: "Invoice ID",
    key: "invoiceNumber",
    align: "start",
    sortable: false,
  },

  {
    title: "Vendor",
    key: "vendorName",
    align: "start",
    sortable: false,
  },

  {
    title: "GRN Date",
    key: "grnDate",
    align: "start",
    sortable: false,
  },
  {
    title: "Invoice Date",
    key: "invoiceDate",
    align: "start",
    sortable: false,
  },
  {
    title: "Total Price",
    key: "totalValue",
    align: "end",
    sortable: false,
  },
];

export const closedRequestItemHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    width: "28%",
    align: "start",
    sortable: false,
  },

  {
    title: "Item Code",
    key: "itemCode",
    width: "12%",
    align: "center",
    sortable: false,
  },
  {
    title: "Vendor",
    key: "vendor",
    width: "20%",
    align: "center",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "10%",
    align: "end",
    sortable: false,
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    width: "10%",
    align: "center",
    sortable: false,
  },
  {
    title: "Tax Rate",
    key: "taxRate",
    width: "10%",
    align: "center",
    sortable: false,
  },
  {
    title: "Total Price",
    key: "totalPrice",
    width: "10%",
    align: "center",
    sortable: false,
  },
];

export const closedOrderItemHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    width: "28%",
    align: "start",
    sortable: false,
  },
  {
    title: "Item Code",
    key: "itemCode",
    width: "12%",
    align: "center",
    sortable: false,
  },
  {
    title: "Quantity",
    key: "quantity",
    width: "12%",
    align: "end",
    sortable: false,
  },

  {
    title: "Received Quantity",
    key: "receivedQuantity",
    width: "12%",
    align: "end",
    sortable: false,
  },
  {
    title: "Unit Cost",
    key: "unitCost",
    width: "12%",
    align: "center",
    sortable: false,
  },
  {
    title: "Tax Rate",
    key: "taxRate",
    width: "12%",
    align: "center",
    sortable: false,
  },
  {
    title: "Total Price",
    key: "totalPrice",
    width: "12%",
    align: "center",
    sortable: false,
  },
];

export const indentHeaders = ref([
  {
    title: "Transfer No.",
    key: "transferNumber",
    align: "start",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Requester",
    key: "requester",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Issuer",
    key: "issuer",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Requested At",
    key: "requestedDate",
    align: "center",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Dispatch Status",
    key: "dispatchStatus",
    align: "center",
    sortable: false,
    mandatory: true,
    default: true,
  },
  {
    title: "Receive Status",
    key: "receiveStatus",
    align: "center",
    sortable: false,
    mandatory: true,
    default: true,
  },
  {
    title: "",
    key: "action",
    align: "end",
    sortable: false,
    mandatory: true,
    default: true,
  },
]);

export const dispatchIndentHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Item Code",
    key: "itemCode",
    align: "center",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Available Qty",
    key: "availableQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Requested Qty",
    key: "requestedQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Pending Qty",
    key: "disPendingQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Dispatch Qty",
    key: "dispatchedQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
];

export const receiveIndentHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Item Code",
    key: "itemCode",
    align: "center",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Dispatched Qty",
    key: "dispatchedQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Received Qty",
    key: "receivedQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Shortage Qty",
    key: "shortageQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Shortage Reason",
    key: "reason",
    align: "center",
    mandatory: true,
    default: true,
  },
];

export const viewTransferHeaders = [
  {
    title: "Item Name",
    key: "itemName",
    align: "start",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Item Code",
    key: "itemCode",
    align: "center",
    sortable: false,
    mandatory: false,
    default: true,
  },
  {
    title: "Requested Qty",
    key: "requestedQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Dispatched Qty",
    key: "dispatchedQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
  {
    title: "Received Qty",
    key: "receivedQuantity",
    align: "center",
    mandatory: true,
    default: true,
  },
];

export const setIndentHeaders = (newHeaders) => {
  indentHeaders.value = newHeaders;
};
