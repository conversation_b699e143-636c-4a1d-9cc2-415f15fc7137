export function filterMenuByPrivileges(items, privileges) {
  return items
    .map((item) => {
      if (item.privilege_code && !privileges.includes(item.privilege_code)) {
        return null;
      }

      if (item.subItems) {
        const allowedSubItems = filterMenuByPrivileges(
          item.subItems,
          privileges
        );
        if (allowedSubItems.length > 0) {
          return { ...item, subItems: allowedSubItems };
        }
        return null;
      }

      return item;
    })
    .filter(Boolean);
}

// export function hasPrivilege(privilegeCode) {
//   const privileges = JSON.parse(sessionStorage.getItem("privileges") || "[]");
//   return privileges.includes(privilegeCode);
// }
