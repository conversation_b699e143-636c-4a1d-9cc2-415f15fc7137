export const vendorRecord = {
  vendorId: "",
  name: "",
  contactEmailId: "",
  contactName: "",
  contactNo: "",
  address: { address: "", city: "", state: "", pincode: "" },
  cinNo: "",
  gstNo: "",
  panNo: "",
  tinNo: "",
  poTerms: "",
  paymentTerms: "",
};

export const categoryRecord = {
  name: "",
  tenantId: "",
  subCategories: [],
};

export const locationRecord = {
  storeId: "",
  name: "",
};

export const importExportLogRecord = {
  accountId: "",
  type: "",
  options: "",
  datetime: "",
};

export const houseUnitRecord = {
  name: "",
  symbol: "",
  unitType: "Mass",
  conversions: [],
};

export const conversionRecord = {
  quantity: "",
  name: "",
};

export const inventoryRecord = {
  itemType: "bought", // bought/madeItem
  itemName: "",
  itemCode: "",
  tags: [],
  category: null,
  subCategory: null,
  purchaseUnit: null,
  countingUnit: { symbol: null, conversion: "" },
  recipeUnit: { symbol: null, conversion: "" },
  parLevel: 0,
  trackExpiry: false,
  packages: [],
  vendors: [],
  activeStatus: true,
  unitCost: 0,
};

export const packageRecord = {
  name: "",
  packageCode: "",
  quantity: null,
  unitCost: 0,
};

export const ingredientRecord = {
  id: "",
  name: "",
  quantity: null,
  recipeUnit: null,
  countingUnit: null,
  purchaseUnit: null,
};

export const recipeRecord = {
  name: "",
  quantity: 0,
  cost: 0,
  activeStatus: true,
  cookingProcedure: [],
  ingredients: [],
  recipeCode: "",
};

export const storeRecord = {
  name: "",
  posId: "",
  locationType: "CENTRAL KITCHEN",
  accountId: "",
  accountName: "",
  tenantId: "",
  billTo: "",
  shipTo: "",
  panNo: "",
  gstNo: "",
};

export const purchaseRequestRecord = {
  location: null,
  inventoryLocation: null,
  items: [],
  prNumber: null,
  vendorType: 1,
  deliveryDate: null,
  vendor: null,
};

export const purchaseOrderRecord = {
  location: null,
  items: [],
  goodsReceivedDate: null,
  vendorInvoiceDate: null,
  vendorInvoiceNumber: null,
  vendor: null,
};

export const purchaseRequestItemRecord = {
  itemId: null,
  itemName: null,
  itemCode: null,
  vendor: null,
  quantity: 1,
  purchaseUOM: null,
  unitCost: 0,
  discount: 0,
  taxRate: 0,
  totalPrice: 0,
  categoryId: null,
  subcategoryId: null,
  discount: 0,
  pkg: null,
};

export const purchaseOrderItemRecord = {
  itemId: "",
  itemName: "",
  itemCode: "",
  quantity: null,
  receivedQuantity: 0,
  purchaseUOM: null,
  unitCost: 0,
  taxRate: 0,
  totalPrice: 0,
};

export const menuItemRecord = {
  id: "",
  tenantId: "",
  itemName: "",
  itemCode: "",
  itemType: "",
  activeStatus: true,
  // groups: [],
  account: { name: "", id: "", posId: "" },
  posId: "",
  servingLevels: [],
};

export const indentRequestRecord = {
  requester: null,
  issuer: null,
  location: null,
  selectedLocation: null,
  items: [],
  requestedBy: null,
};

export const indentIssueRecord = {
  location: null,
  selectedLocation: null,
  items: [],
  requestedBy: null,
};
