import { computed } from "vue";
// Define constants for the keys
const AUTH_TOKEN = "_token";
const TENANT_ID = "_tenantId";
const Expiry_Time = "_expiryTime";
const TENANTS = "tenants";
const PRIVILEGES = "privileges";
const USER_ID = "user_id";
const USER_NAME = "user_name";

let expiryTimer = null;

const getAuthorizationToken = () => {
  return sessionStorage.getItem(AUTH_TOKEN);
};

const setAuthorization = (data) => {
  setAuthorizationToken(data.token);
  setTenantId(data.tenantId);
  setTenants(data.tenants);
  setUser(data.tenants[0]);

  // setPrivileges(data.tenants[0]?.privileges);
};

const setUser = ({ userId, userName }) => {
  localStorage.setItem(USER_ID, userId);
  localStorage.setItem(USER_NAME, userName);
  sessionStorage.setItem(USER_ID, userId);
  sessionStorage.setItem(USER_NAME, userName);
};

const getUser = () => {
  return {
    userId: localStorage.getItem(USER_ID),
    userName: localStorage.getItem(USER_NAME),
  };
};

const setTenants = (tenants) => {
  sessionStorage.setItem(TENANTS, JSON.stringify(tenants));
  localStorage.setItem(TENANTS, JSON.stringify(tenants));
};

const setPrivileges = (privileges) => {
  sessionStorage.setItem(PRIVILEGES, JSON.stringify(privileges));
  localStorage.setItem(PRIVILEGES, JSON.stringify(privileges));
};

const getPrivileges = () => {
  const data = sessionStorage.getItem(PRIVILEGES);
  return data ? JSON.parse(data) : [];
};

const setAuthorizationToken = (token) => {
  sessionStorage.setItem(AUTH_TOKEN, token);
  localStorage.setItem(AUTH_TOKEN, token);
};

const setTenantId = (tenantId) => {
  sessionStorage.setItem(TENANT_ID, tenantId);
  localStorage.setItem(TENANT_ID, tenantId);
};

const getTenantId = computed(() => {
  return sessionStorage.getItem(TENANT_ID);
});

const setExpiryTime = (time) => {
  sessionStorage.setItem(Expiry_Time, time);
  localStorage.setItem(Expiry_Time, time);
};

const getTenants = () => {
  return JSON.parse(sessionStorage.getItem(TENANTS));
};

const getExpiryTime = () => {
  return sessionStorage.getItem(Expiry_Time);
};

const logout = () => {
  sessionStorage.removeItem(AUTH_TOKEN);
  sessionStorage.removeItem(TENANT_ID);
  localStorage.removeItem(AUTH_TOKEN);
  localStorage.removeItem(TENANT_ID);
  sessionStorage.removeItem(TENANTS);
  localStorage.removeItem(TENANTS);
  sessionStorage.removeItem(PRIVILEGES);
  localStorage.removeItem(PRIVILEGES);
  if (expiryTimer) {
    clearTimeout(expiryTimer);
    expiryTimer = null;
  }
};

// const isTokenValid = () => {
//   const expiryTimeStr = sessionStorage.getItem("Expiry_Time");
//   if (!expiryTimeStr) return false;

//   const expiryTime = Number(expiryTimeStr);
//   const currentTime = Date.now();

//   console.log(expiryTime, currentTime, "isTokenValid");
//   return currentTime < expiryTime; // true if still valid
// };

const startExpiryWatcher = (router) => {
  if (expiryTimer) clearTimeout(expiryTimer);

  const expiryTime = getExpiryTime();
  if (!expiryTime) return;

  const timeLeft = expiryTime - Date.now();

  if (timeLeft > 0) {
    expiryTimer = setTimeout(() => {
      logout();
      router.replace("/login");
    }, timeLeft);
  } else {
    logout();
    router.replace("/login");
  }
};

export {
  getAuthorizationToken,
  setAuthorization,
  setTenantId,
  setExpiryTime,
  setAuthorizationToken,
  logout,
  // isTokenValid,
  getExpiryTime,
  setTenants,
  getTenants,
  getTenantId,
  getPrivileges,
  setPrivileges,
  startExpiryWatcher,
  setUser,
  getUser,
};
