import { nextTick } from "vue";

export const handleGlobalTab = (e) => {
    if (
        e.key === "Tab" &&
        document.activeElement === document.body
    ) {
        e.preventDefault();
        focusVendorType();
    }
};

export function useTabFlow({ selector = ".custom-table", startIndex = 1 } = {}) {
  const getTabIndex = (rowIndex, colIndex, totalCols) => {
    return startIndex + rowIndex * totalCols + colIndex;
  };

  const focusElementByTabIndex = (tabIndex) => {
    const container = document.querySelector(selector);
    const inputs = container?.querySelectorAll("input, button, [tabindex]");
    const el = [...inputs].find(
      (el) => parseInt(el.getAttribute("tabindex") || -1) === tabIndex
    );
    el?.focus();
  };

  const focusNextRow = (rowIndex, totalCols) => {
    const nextIndex = rowIndex + 1;
    focusElementByTabIndex(getTabIndex(nextIndex, 0, totalCols));
  };

  const focusFirstElement = async () => {
    await nextTick();
    const firstEl = document.querySelector(`${selector} [tabindex="${startIndex}"]`);
    firstEl?.focus();
  };

  const handleTabNavigation = (event, { isLastRow, rowIndex, totalCols }) => {
    event.preventDefault();
    if (isLastRow) {
      focusFirstElement();
    } else {
      focusNextRow(rowIndex, totalCols);
    }
  };

  return {
    getTabIndex,
    focusNextRow,
    focusFirstElement,
    handleTabNavigation,
  };
}
