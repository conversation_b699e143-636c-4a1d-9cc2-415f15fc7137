import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";

const axios = axiosInstance();

export const useStockStore = defineStore("stock", () => {
  const { showSnackbar } = useSnackbarStore();
  const Stocks = ref([]);
  const stockLogs = ref([]);

  const getStocks = computed(() => Stocks.value);
  const getStockLogs = computed(() => stockLogs.value);

  const setStocks = (data) => {
    Stocks.value = data;
  };

  const fetchStocks = async (filters) => {
    try {
      const response = await axios.post("reports/stocks", filters);
      setStocks(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchStockLogs = async ({
    locationId: inventoryLocationId,
    storeId: locationId,
    itemId,
  }) => {
    try {
      const response = await axios.post(`reports/stock-ledgers`, {
        locationId,
        inventoryLocationId,
        itemId,
      });
      stockLogs.value = response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const purchaseStocks = async (data) => {
    try {
      await axios.post(`stocks/purchase`, data);
      await fetchStocks(data.inventoryLocation.id);
      showSnackbar("success", "Stock credited successfully");
    } catch ({ response }) {
      console.log(response);
    }
  };

  const consumeStocks = async (data) => {
    try {
      await axios.post(`stocks/consume`, data);
      await fetchStocks(data.inventoryLocation.id);
      showSnackbar("success", "Stock debited successfully");
    } catch ({ response }) {
      console.log(response);
    }
  };

  const prepareStocks = async (data) => {
    try {
      await axios.post(`stocks/prepare`, data);
      await fetchStocks(data.location.id);
      showSnackbar("success", "Prepared successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw response.data;
    }
  };

  return {
    getStocks,
    getStockLogs,
    fetchStocks,
    fetchStockLogs,
    purchaseStocks,
    consumeStocks,
    prepareStocks,
  };
});
