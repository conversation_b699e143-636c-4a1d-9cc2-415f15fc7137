import { setActive<PERSON>inia, createPinia } from "pinia";
import { describe, beforeEach, it, expect, vi } from "vitest";
import { useReceipeStore } from "@/stores/receipe";
import axiosInstance from "@/plugin/Axios";

// Mock snackbar
const showSnackbarMock = vi.fn();

vi.mock("@/plugin/Axios", () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

vi.mock("@/stores/snackBar", () => ({
  useSnackbarStore: () => ({
    showSnackbar: showSnackbarMock,
  }),
}));

function mockAxiosError(message) {
  return {
    response: {
      data: { message },
    },
  };
}

describe("Receipe Store", () => {
  let receipeStore;

  beforeEach(() => {
    setActivePinia(createPinia());
    receipeStore = useReceipeStore();
    vi.clearAllMocks();
  });

  describe("fetchReceipes", () => {
    it("should fetch and set receipes", async () => {
      const mockData = [{ id: 1, name: "Test Receipe" }];
      axiosInstance.get.mockResolvedValueOnce({ data: mockData });

      await receipeStore.fetchReceipes();

      expect(receipeStore.getReceipes).toEqual(mockData);
      expect(axiosInstance.get).toHaveBeenCalledWith(
        expect.stringContaining("/receipes")
      );
    });

    it("should handle fetch error", async () => {
      const errorMessage = "Fetch error";
      axiosInstance.get.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.fetchReceipes()).rejects.toThrow(errorMessage);
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("fetchReceipeById", () => {
    it("should return single receipe data", async () => {
      const id = 1;
      const mockReceipe = { id, name: "Sample" };
      axiosInstance.get.mockResolvedValueOnce({ data: mockReceipe });

      const result = await receipeStore.fetchReceipeById(id);

      expect(result).toEqual(mockReceipe);
      expect(axiosInstance.get).toHaveBeenCalledWith(
        expect.stringContaining(`/receipes/${id}`)
      );
    });

    it("should handle error on fetching by ID", async () => {
      const errorMessage = "Fetch by ID failed";
      axiosInstance.get.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.fetchReceipeById(999)).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("createReceipe", () => {
    it("should create receipe and fetch updated list", async () => {
      const newReceipe = { name: "New Receipe" };
      axiosInstance.post.mockResolvedValueOnce({});
      axiosInstance.get.mockResolvedValueOnce({ data: [newReceipe] });

      await receipeStore.createReceipe(newReceipe);

      expect(axiosInstance.post).toHaveBeenCalledWith(
        expect.stringContaining("/receipes"),
        newReceipe
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Receipe created successfully"
      );
    });

    it("should handle creation error", async () => {
      const errorMessage = "Creation failed";
      axiosInstance.post.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.createReceipe({})).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("updateReceipe", () => {
    it("should update receipe and fetch updated list", async () => {
      const updatedReceipe = { id: 1, name: "Updated" };
      axiosInstance.put.mockResolvedValueOnce({});
      axiosInstance.get.mockResolvedValueOnce({ data: [updatedReceipe] });

      await receipeStore.updateReceipe(updatedReceipe);

      expect(axiosInstance.put).toHaveBeenCalledWith(
        expect.stringContaining(`/receipes/${updatedReceipe.id}`),
        updatedReceipe
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Receipe updated successfully"
      );
    });

    it("should handle update error", async () => {
      const errorMessage = "Update failed";
      axiosInstance.put.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.updateReceipe({ id: 2 })).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("deleteReceipe", () => {
    it("should delete receipe and fetch updated list", async () => {
      const id = 7;
      axiosInstance.delete.mockResolvedValueOnce({});
      axiosInstance.get.mockResolvedValueOnce({ data: [] });

      await receipeStore.deleteReceipe(id);

      expect(axiosInstance.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/receipes/${id}`)
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Receipe deleted successfully"
      );
    });

    it("should handle delete error", async () => {
      const errorMessage = "Delete failed";
      axiosInstance.delete.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(receipeStore.deleteReceipe(99)).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });
});
