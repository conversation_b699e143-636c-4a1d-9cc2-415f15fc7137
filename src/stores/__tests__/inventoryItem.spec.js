import { setActivePinia, createPinia } from "pinia";
import { describe, beforeEach, it, vi, expect } from "vitest";
import { useInventoryItemStore } from "@/stores/inventoryItem";
import axiosInstance from "@/plugin/Axios";

// Mock snackbar
const showSnackbarMock = vi.fn();

vi.mock("@/plugin/Axios", () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

vi.mock("@/stores/snackBar", () => ({
  useSnackbarStore: () => ({
    showSnackbar: showSnackbarMock,
  }),
}));

function mockAxiosError(message) {
  return {
    response: {
      data: { message },
    },
  };
}

describe("Inventory Item Store", () => {
  let inventoryItemStore;

  beforeEach(() => {
    setActivePinia(createPinia());
    inventoryItemStore = useInventoryItemStore();

    axiosInstance.get.mockReset();
    axiosInstance.post.mockReset();
    axiosInstance.put.mockReset();
    axiosInstance.delete.mockReset();
    showSnackbarMock.mockReset();
  });

  describe("fetchInventoryItems", () => {
    it("should fetch all inventory items", async () => {
      const mockData = [{ id: 1, name: "Item A" }];
      axiosInstance.get.mockResolvedValueOnce({ data: mockData });

      await inventoryItemStore.fetchInventoryItems();

      expect(inventoryItemStore.getInventoryItems).toEqual(mockData);
      expect(axiosInstance.get).toHaveBeenCalledWith(
        expect.stringContaining("/inventory-items")
      );
    });

    it("should show error if fetching fails", async () => {
      const errorMessage = "Fetch failed";
      axiosInstance.get.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(inventoryItemStore.fetchInventoryItems()).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("fetchInventoryItemById", () => {
    it("should return item by ID", async () => {
      const item = { id: 10, name: "Item X" };
      axiosInstance.get.mockResolvedValueOnce({ data: item });

      const result = await inventoryItemStore.fetchInventoryItemById(item.id);

      expect(result).toEqual(item);
      expect(axiosInstance.get).toHaveBeenCalledWith(
        expect.stringContaining(`/inventory-items/${item.id}`)
      );
    });

    it("should handle error when fetching by ID fails", async () => {
      const errorMessage = "Item not found";
      axiosInstance.get.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(
        inventoryItemStore.fetchInventoryItemById(1)
      ).rejects.toThrow(errorMessage);
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("createInventoryItem", () => {
    it("should create an inventory item", async () => {
      const newItem = { name: "New Item" };
      axiosInstance.post.mockResolvedValueOnce({});
      axiosInstance.get.mockResolvedValueOnce({ data: [newItem] });

      await inventoryItemStore.createInventoryItem(newItem);

      expect(axiosInstance.post).toHaveBeenCalledWith(
        expect.stringContaining("/inventory-items"),
        newItem
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Inventory Item created successfully"
      );
    });

    it("should handle error during creation", async () => {
      const newItem = { name: "Broken Item" };
      const errorMessage = "Create failed";
      axiosInstance.post.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(
        inventoryItemStore.createInventoryItem(newItem)
      ).rejects.toThrow(errorMessage);
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("updateInventoryItem", () => {
    it("should update an inventory item", async () => {
      const item = { id: 5, name: "Updated Item" };
      axiosInstance.put.mockResolvedValueOnce({});
      axiosInstance.get.mockResolvedValueOnce({ data: [item] });

      await inventoryItemStore.updateInventoryItem(item);

      expect(axiosInstance.put).toHaveBeenCalledWith(
        expect.stringContaining(`/inventory-items/${item.id}`),
        item
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Inventory Item updated successfully"
      );
    });

    it("should handle update failure", async () => {
      const item = { id: 9, name: "Fail Update" };
      const errorMessage = "Update failed";
      axiosInstance.put.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(
        inventoryItemStore.updateInventoryItem(item)
      ).rejects.toThrow(errorMessage);
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("deleteInventoryItem", () => {
    it("should delete an inventory item", async () => {
      const id = 99;
      axiosInstance.delete.mockResolvedValueOnce({});
      axiosInstance.get.mockResolvedValueOnce({ data: [] });

      await inventoryItemStore.deleteInventoryItem(id);

      expect(axiosInstance.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/inventory-items/${id}`)
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Inventory Item deleted successfully"
      );
    });

    it("should handle delete failure", async () => {
      const id = 99;
      const errorMessage = "Delete failed";
      axiosInstance.delete.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(inventoryItemStore.deleteInventoryItem(id)).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });
});
