import { vi, describe, it, beforeEach, expect } from "vitest";
import { useCategoryStore } from "../category";
import axiosInstance from "@/plugin/Axios";
import { createPinia, setActivePinia } from "pinia";

const showSnackbarMock = vi.fn();

vi.mock("@/plugin/Axios", () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Snackbar store mock
vi.mock("@/stores/snackBar", () => ({
  useSnackbarStore: () => ({
    showSnackbar: showSnackbarMock,
  }),
}));

const mockAxiosError = (message) => ({
  response: {
    data: { message },
  },
});
describe("category store", () => {
  let categoryStore;
  beforeEach(() => {
    setActivePinia(createPinia());
    categoryStore = useCategoryStore();

    axiosInstance.get.mockReset();
    axiosInstance.post.mockReset();
    axiosInstance.put.mockReset();
    axiosInstance.delete.mockReset();
    showSnackbarMock.mockReset();

    vi.stubGlobal("localStorage", {
      getItem: vi.fn(() => "10057"),
    });
  });
  describe("fetchCategories", () => {
    describe("success", () => {
      it("should fetch all categories", async () => {
        const tenantId = "10057";
        const mockCategories = [{ id: 1, name: "Category 1" }];

        axiosInstance.get.mockResolvedValue({ data: mockCategories });
        await categoryStore.fetchCategories();

        expect(categoryStore.getCategories).toEqual(mockCategories);
        expect(axiosInstance.get).toHaveBeenCalledWith(
          expect.stringContaining("/categories"),
          { params: { tenantId } }
        );
      });
    });
    describe("failure", () => {
      it("should handle error when fetch categories fails", async () => {
        const errorMessage = "Something went wrong";
        axiosInstance.get.mockRejectedValue(mockAxiosError(errorMessage));
        await expect(categoryStore.fetchCategories()).rejects.toThrow(
          errorMessage
        );
        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("fetchCategoryById", () => {
    describe("success", () => {
      it("should fetch all categories", async () => {
        const tenantId = "10057";
        const categoryId = 1;
        const mockCategories = { id: 1, name: "Category 1", tenantId };

        axiosInstance.get.mockResolvedValue({ data: mockCategories });
        await categoryStore.fetchCategoryById(categoryId);

        expect(axiosInstance.get).toHaveBeenCalledWith(
          expect.stringContaining(`/categories/${categoryId}`),
          { params: { tenantId } }
        );
      });
    });
    describe("failure", () => {
      it("should handle error when fetch category by id fails", async () => {
        const errorMessage = "Something went wrong";
        const categoryId = 1;

        axiosInstance.get.mockRejectedValue(mockAxiosError(errorMessage));
        await expect(
          categoryStore.fetchCategoryById(categoryId)
        ).rejects.toThrow(errorMessage);
        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("createCategory", () => {
    describe("success", () => {
      it("should create category", async () => {
        const tenantId = "10057";
        const categoryData = { name: "Test Category", tenantId };

        axiosInstance.post.mockResolvedValueOnce({ status: 201 });
        axiosInstance.get.mockResolvedValueOnce({
          data: [{ id: 1, ...categoryData }],
        });

        await categoryStore.createCategory(categoryData);
        expect(axiosInstance.post).toHaveBeenCalledWith(
          expect.stringContaining("/categories"),
          categoryData
        );
        expect(axiosInstance.get).toHaveBeenCalledTimes(1);
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Category created successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when create category fails", async () => {
        const tenantId = "10057";
        const errorMessage = "Something went wrong";
        const categoryData = { name: "Test Category", tenantId };

        axiosInstance.post.mockRejectedValue(mockAxiosError(errorMessage));

        await expect(
          categoryStore.createCategory(categoryData)
        ).rejects.toThrow(errorMessage);

        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("updateCategory", () => {
    describe("success", () => {
      it("should update category", async () => {
        const tenantId = "10057";
        const categoryId = 1;
        const categoryData = { id: 1, name: "Test Category", tenantId };

        axiosInstance.put.mockResolvedValueOnce({ status: 200 });
        axiosInstance.get.mockResolvedValueOnce({
          data: [{ id: 1, ...categoryData }],
        });

        await categoryStore.updateCategory(categoryData);
        expect(axiosInstance.put).toHaveBeenCalledWith(
          expect.stringContaining(`/categories/${categoryId}`),
          categoryData
        );
        expect(axiosInstance.get).toHaveBeenCalledTimes(1);
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Category updated successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when update category fails", async () => {
        const errorMessage = "Something went wrong";
        const categoryData = {
          id: 1,
          name: "Test Category",
          tenantId: "10057",
        };

        axiosInstance.put.mockRejectedValue(mockAxiosError(errorMessage));

        await expect(
          categoryStore.updateCategory(categoryData)
        ).rejects.toThrow(errorMessage);

        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("updateSubCategory", () => {
    describe("success", () => {
      it("should update subcategory", async () => {
        const categoryData = {
          id: 1,
          name: "Test Category",
          tenantId: "10057",
          subCategories: [
            {
              name: "beer",
            },
          ],
        };

        axiosInstance.post.mockResolvedValueOnce({ status: 200 });

        await categoryStore.updateSubCategory(categoryData);
        expect(axiosInstance.post).toHaveBeenCalledWith(
          expect.stringContaining(`/categories/1/subcategories`),
          categoryData
        );
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Subcategory added successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when update subcategory fails", async () => {
        const errorMessage = "Something went wrong";
        const categoryData = {
          id: 1,
          name: "Test Category",
          tenantId: "10057",
          subCategories: [
            {
              name: "beer",
            },
          ],
        };

        axiosInstance.post.mockRejectedValue(mockAxiosError(errorMessage));

        await expect(
          categoryStore.updateSubCategory(categoryData)
        ).rejects.toThrow(errorMessage);

        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });

  describe("deleteCategory", () => {
    describe("success", () => {
      it("should delete category", async () => {
        const categoryId = 1;
        const mockCategories = [{ id: 1, name: "Category 1" }];

        axiosInstance.delete.mockResolvedValue({ status: 200 });
        axiosInstance.get.mockResolvedValue([]);
        await categoryStore.deleteCategory(categoryId);

        expect(axiosInstance.delete).toHaveBeenCalledWith(
          expect.stringContaining(`/categories/${categoryId}`)
        );
        expect(showSnackbarMock).toHaveBeenCalledWith(
          "success",
          "Category deleted successfully"
        );
      });
    });
    describe("failure", () => {
      it("should handle error when category delete fails", async () => {
        const errorMessage = "Something went wrong";
        const categoryId = 1;

        axiosInstance.delete.mockRejectedValue(mockAxiosError(errorMessage));
        await expect(categoryStore.deleteCategory(categoryId)).rejects.toThrow(
          errorMessage
        );
        expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
      });
    });
  });
});
