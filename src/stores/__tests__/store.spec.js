import { setActivePinia, create<PERSON><PERSON> } from "pinia";
import { describe, it, beforeEach, expect, vi } from "vitest";
import { useStoreStore } from "@/stores/store";
import axiosInstance from "@/plugin/Axios";

// Mock snackbar
const showSnackbarMock = vi.fn();

vi.mock("@/plugin/Axios", () => ({
  default: {
    get: vi.fn(),
  },
}));

vi.mock("@/stores/snackBar", () => ({
  useSnackbarStore: () => ({
    showSnackbar: showSnackbarMock,
  }),
}));

function mockAxiosError(message) {
  return {
    response: {
      data: { message },
    },
  };
}

describe("Store Store", () => {
  let store;

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useStoreStore();
    vi.clearAllMocks();
  });

  describe("fetchStores", () => {
    it("should fetch and set stores", async () => {
      const mockData = [{ id: 1, name: "Store A" }];
      axiosInstance.get.mockResolvedValueOnce({ data: mockData });

      await store.fetchStores();

      expect(store.getStores).toEqual(mockData);
      expect(axiosInstance.get).toHaveBeenCalledWith(
        expect.stringContaining("/stores")
      );
    });

    it("should handle fetch error", async () => {
      const errorMessage = "Failed to load stores";
      axiosInstance.get.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(store.fetchStores()).rejects.toThrow(errorMessage);
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });
});
