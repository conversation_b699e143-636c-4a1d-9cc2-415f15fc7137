import { setActivePinia, createPinia } from "pinia";
import { describe, it, beforeEach, expect, vi } from "vitest";
import { useLocationStore } from "@/stores/location";
import axiosInstance from "@/plugin/Axios";

// Mock snackbar store
const showSnackbarMock = vi.fn();

vi.mock("@/plugin/Axios", () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

vi.mock("@/stores/snackBar", () => ({
  useSnackbarStore: () => ({
    showSnackbar: showSnackbarMock,
  }),
}));

function mockAxiosError(message) {
  return {
    response: {
      data: { message },
    },
  };
}

describe("Location Store", () => {
  let locationStore;

  beforeEach(() => {
    setActivePinia(createPinia());
    locationStore = useLocationStore();
    vi.clearAllMocks();
  });

  describe("fetchLocations", () => {
    it("should fetch and set locations", async () => {
      const mockData = [{ id: 1, name: "Main Warehouse" }];
      axiosInstance.get.mockResolvedValueOnce({ data: mockData });

      await locationStore.fetchLocations();

      expect(locationStore.getLocations).toEqual(mockData);
      expect(axiosInstance.get).toHaveBeenCalledWith(
        expect.stringContaining("/inventory-locations")
      );
    });

    it("should handle fetch error", async () => {
      const errorMessage = "Fetch failed";
      axiosInstance.get.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(locationStore.fetchLocations()).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("createLocation", () => {
    it("should create location and refresh list", async () => {
      const payload = { name: "New Location" };
      axiosInstance.post.mockResolvedValueOnce({});
      axiosInstance.get.mockResolvedValueOnce({ data: [payload] });

      await locationStore.createLocation(payload);

      expect(axiosInstance.post).toHaveBeenCalledWith(
        expect.stringContaining("/inventory-locations"),
        payload
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Location created successfully"
      );
    });

    it("should handle creation error", async () => {
      const errorMessage = "Creation failed";
      axiosInstance.post.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(locationStore.createLocation({})).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("updateLocation", () => {
    it("should update location and refresh list", async () => {
      const updated = { id: 1, name: "Updated Location" };
      axiosInstance.put.mockResolvedValueOnce({});
      axiosInstance.get.mockResolvedValueOnce({ data: [updated] });

      await locationStore.updateLocation(updated);

      expect(axiosInstance.put).toHaveBeenCalledWith(
        expect.stringContaining(`/inventory-locations/${updated.id}`),
        updated
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Location updated successfully"
      );
    });

    it("should handle update error", async () => {
      const errorMessage = "Update failed";
      axiosInstance.put.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(locationStore.updateLocation({ id: 99 })).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });

  describe("deleteLocation", () => {
    it("should delete location and refresh list", async () => {
      const id = 42;
      axiosInstance.delete.mockResolvedValueOnce({});
      axiosInstance.get.mockResolvedValueOnce({ data: [] });

      await locationStore.deleteLocation(id);

      expect(axiosInstance.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/inventory-locations/${id}`)
      );
      expect(showSnackbarMock).toHaveBeenCalledWith(
        "success",
        "Location deleted successfully"
      );
    });

    it("should handle delete error", async () => {
      const errorMessage = "Delete failed";
      axiosInstance.delete.mockRejectedValueOnce(mockAxiosError(errorMessage));

      await expect(locationStore.deleteLocation(123)).rejects.toThrow(
        errorMessage
      );
      expect(showSnackbarMock).toHaveBeenCalledWith("error", errorMessage);
    });
  });
});
