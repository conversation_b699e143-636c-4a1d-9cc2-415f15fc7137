import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

const axios = axiosInstance();

export const usePurchaseOrderStore = defineStore("purchaseOrder", () => {
  // const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const purchaseOrder = ref([]);
  const getPurchaseOrder = computed(() => purchaseOrder.value);
  const currentTab = ref(1);

  const setPurchaseOrder = (data) => {
    purchaseOrder.value = data;
  };

  const setCurrentTab = (v) => {
    currentTab.value = v;
  };

  const getCurrentTab = computed(() => currentTab.value);

  const fetchPurchaseOrder = async (filters) => {
    try {
      const response = await axios.post(`purchase-orders`, filters);
      setPurchaseOrder(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchPurchaseOrderById = async (id) => {
    try {
      const response = await axios.get(`purchase-orders/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createPurchaseOrder = async (data) => {
    try {
      await axios.post("purchase-orders/create", data);
      showSnackbar("success", "Purchase order created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const approvePurchaseOrder = async (id) => {
    try {
      await axios.post(`purchase-orders/${id}/approve`);
      showSnackbar("success", "Purchase order has been approved successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const rejectPurchaseOrder = async (id, data) => {
    try {
      await axios.post(`purchase-orders/${id}/reject`, data);
      showSnackbar("success", "Purchase order has been rejected successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    purchaseOrder,
    getPurchaseOrder,
    fetchPurchaseOrder,
    fetchPurchaseOrderById,
    createPurchaseOrder,
    approvePurchaseOrder,
    rejectPurchaseOrder,
    getCurrentTab,
    setCurrentTab,
  };
});
