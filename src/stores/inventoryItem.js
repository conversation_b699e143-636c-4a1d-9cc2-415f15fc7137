import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

const axios = axiosInstance();

export const useInventoryItemStore = defineStore("inventoryItem", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const InventoryItems = ref([]);

  const getInventoryItems = computed(() => InventoryItems.value || []);

  const setInventoryItems = (data) => {
    InventoryItems.value = data;
  };

  const fetchInventoryItems = async () => {
    try {
      const response = await axios.get(`inventory-items`);
      setInventoryItems(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchInventoryItemById = async (id) => {
    try {
      const response = await axios.get(`inventory-items/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createInventoryItem = async (data) => {
    data.tenantId = tenantId.value;
    try {
      const response = await axios.post("inventory-items", data);
      showSnackbar("green", "Inventory Item Created Successfully");
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateInventoryItem = async (data) => {
    try {
      await axios.put(`inventory-items/${data.id}`, data);
      showSnackbar("green", "Inventory Item Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateInventoryItemActiveStatus = async (id) => {
    try {
      await axios.put(`inventory-items/${id}/activateStatus`);
      fetchInventoryItems();
      showSnackbar("green", "Inventory Item Status Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  // const exportItems = async () => {
  //   try {
  //     if (InventoryItems.value.length === 0) {
  //       showSnackbar("error", "No Inventory Items to Export");
  //       return;
  //     }
  //     const response = await axios.get("inventory-items/export/export-excel", {
  //       params: { tenantId: tenantId.value },
  //       responseType: "blob"
  //     });

  //     const blob = new Blob([response.data], {
  //       type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  //     });
  //     const downloadUrl = window.URL.createObjectURL(blob);
  //     const link = document.createElement("a");
  //     link.href = downloadUrl;
  //     link.download = "inventory-items.xlsx";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //     window.URL.revokeObjectURL(downloadUrl);
  //     showSnackbar("green", "Export Completed Successfully");
  //   } catch (error) {
  //     console.error("Export failed", error);
  //     showSnackbar("error", "Export Failed");
  //   }
  // };

  return {
    getInventoryItems,
    fetchInventoryItems,
    fetchInventoryItemById,
    createInventoryItem,
    updateInventoryItem,
    updateInventoryItemActiveStatus,
    // exportItems
  };
});
