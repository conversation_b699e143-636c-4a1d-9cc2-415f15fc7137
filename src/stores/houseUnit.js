import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

const axios = axiosInstance();

export const useHouseUnitStore = defineStore("houseUnit", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const HouseUnits = ref([]);

  const getHouseUnits = computed(() => HouseUnits.value);

  const setHouseUnits = (data) => {
    HouseUnits.value = data;
  };

  const fetchHouseUnits = async () => {
    try {
      const response = await axios.post(
        `house-units/get-house-units`
      );
      setHouseUnits(response.data);
    } catch ({ response }) {
      showSnackbar("error", response?.data?.message || "Error fetching data");
      throw new Error(response?.data?.message || "Error fetching data");
    }
  };

  const createHouseUnits = async (data) => {
    try {
      await axios.post("house-units", { ...data, tenantId: tenantId.value });
      await fetchHouseUnits();
      showSnackbar("green", "House Unit Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateHouseUnits = async (data) => {
    try {
      await axios.put(`house-units/${data.id}`, {
        ...data,
        tenantId: tenantId.value
      });
      await fetchHouseUnits();
      showSnackbar("green", "House Unit Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const deleteHouseUnits = async (id) => {
    try {
      await axios.delete(`house-units/${id}`);
      await fetchHouseUnits();
      showSnackbar("green", "House Unit deleted successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateHouseUnitsActiveStatus = async (id) => {
    try {
      await axios.put(`house-units/${id}/activateStatus`);
      await fetchHouseUnits();
      showSnackbar("green", "House Unit Status Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchHouseUnitsBytenant = async () => {
    try {
      const response = await axios.get(`house-units`);
      setHouseUnits(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchHouseUnitById = async (id) => {
    try {
      const response = await axios.get(`house-units/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  // Returns tenant house units without mutating the shared state
  const fetchHouseUnitsBytenantList = async () => {
    try {
      const response = await axios.get(`house-units`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response?.data?.message || "Error fetching data");
      throw new Error(response?.data?.message || "Error fetching data");
    }
  };

  // const exportHouseUnits = async () => {
  //   try {
  //     if (!HouseUnits.value || HouseUnits.value.length === 0) {
  //       showSnackbar("error", "No house units available to export");
  //       return;
  //     }

  //     if (!tenantId.value) {
  //       showSnackbar("error", "tenant ID is required for export");
  //       return;
  //     }

  //     const response = await axios.get("house-units/export/export-excel", {
  //       params: { tenantId: tenantId.value },
  //       responseType: "blob"
  //     });

  //     if (response.data.type === "application/json") {
  //       const reader = new FileReader();
  //       reader.onload = () => {
  //         try {
  //           const errorData = JSON.parse(reader.result);
  //           showSnackbar(
  //             "warning",
  //             errorData.message || "No house units found for export"
  //           );
  //         } catch (e) {
  //           showSnackbar("warning", "No house units found for export");
  //         }
  //       };
  //       reader.readAsText(response.data);
  //       return;
  //     }

  //     if (!response.data || response.data.size === 0) {
  //       showSnackbar("warning", "No data available to export");
  //       return;
  //     }

  //     const blob = new Blob([response.data], {
  //       type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  //     });
  //     const downloadUrl = window.URL.createObjectURL(blob);
  //     const link = document.createElement("a");
  //     link.href = downloadUrl;
  //     link.download = "houseunits.xlsx";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //     window.URL.revokeObjectURL(downloadUrl);
  //     showSnackbar("green", "Export completed successfully");
  //   } catch (error) {
  //     console.error("Export failed", error);
  //     if (error.response?.data?.message) {
  //       showSnackbar("warning", error.response.data.message);
  //     } else {
  //       showSnackbar("error", "Export failed");
  //     }
  //   }
  // };

  return {
    getHouseUnits,
    fetchHouseUnits,
    fetchHouseUnitsBytenant,
    createHouseUnits,
    updateHouseUnits,
    deleteHouseUnits,
    fetchHouseUnitById,
    // exportHouseUnits,
    updateHouseUnitsActiveStatus,
    fetchHouseUnitsBytenantList
  };
});
