import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

const axios = axiosInstance();

export const useReceipeStore = defineStore("recipe", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const receipes = ref([]);

  const getReceipes = computed(() => receipes.value);
  const setReceipes = (data) => {
    receipes.value = data;
  };

  const fetchReceipes = async () => {
    try {
      const response = await axios.get(`recipes`);
      setReceipes(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchReceipeById = async (id) => {
    try {
      const response = await axios.get(`recipes/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createReceipe = async (data) => {
    data.tenantId = tenantId.value;
    try {
      const response = await axios.post("recipes", data);
      showSnackbar("green", "Receipe Created Successfully");
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateReceipe = async (data) => {
    try {
      await axios.put(`recipes/${data.id}`, data);
      showSnackbar("green", "Recipe Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateRecipeActiveStatus = async (id) => {
    try {
      await axios.put(`recipes/${id}/activateStatus`);
      fetchReceipes();
      showSnackbar("green", "Recipe Status Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  // const exportReceipes = async () => {
  //   try {
  //     if (receipes.value.length === 0) {
  //       showSnackbar("error", "No Receipes to Export");
  //       return;
  //     }
  //     const response = await axios.get("receipes/export/export-excel", {
  //       params: { tenantId: tenantId.value },
  //       responseType: "blob"
  //     });

  //     const blob = new Blob([response.data], {
  //       type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  //     });
  //     const downloadUrl = window.URL.createObjectURL(blob);
  //     const link = document.createElement("a");
  //     link.href = downloadUrl;
  //     link.download = "categories.xlsx";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //     window.URL.revokeObjectURL(downloadUrl);
  //     showSnackbar("green", "Export Completed Successfully");
  //   } catch (error) {
  //     console.error("Export failed", error);
  //     showSnackbar("error", "Export Failed");
  //   }
  // };

  return {
    getReceipes,
    setReceipes,
    fetchReceipes,
    fetchReceipeById,
    createReceipe,
    updateReceipe,
    updateRecipeActiveStatus,
    // exportReceipes
  };
});
