import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

const axios = axiosInstance();

export const useGrnStore = defineStore("grnList", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();

  const grnList = ref([]);
  const getGrnList = computed(() => grnList.value);

  const setGrnList = (data) => {
    grnList.value = data;
  };

  const fetchGrnList = async (filters) => {
    try {
      const response = await axios.post("purchases/grns", filters);
      setGrnList(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchGrnById = async (id) => {
    try {
      const response = await axios.get(`purchases/grns/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createGrn = async (data) => {
    data.tenantId = tenantId.value;
    try {
      const res = await axios.post("purchases/create-grn", data);
      //showSnackbar("success", "GRN created successfully");
      return res.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    grnList,
    getGrnList,
    fetchGrnList,
    createGrn,
    fetchGrnById,
  };
});
