import { defineStore } from "pinia";
import { ref } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
const axios = axiosInstance();
import { getTenantId } from "@/helpers/auth";

export const usePrintStore = defineStore("print", () => {

  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();

  const print = async (type,id,dispatchNo = null) => {    
    try {
      const response = await axios.post("prints", { type,id,tenantId: tenantId.value,dispatchNo });      

      if (response.data.result === "success") {
        const { base64 } = response.data;

        // Convert base64 to Blob
        const pdfBuffer = Uint8Array.from(atob(base64), (c) => c.charCodeAt(0));
        const blob = new Blob([pdfBuffer], { type: "application/pdf" });
        const blobUrl = window.URL.createObjectURL(blob);

        // Open in new tab
        window.open(blobUrl, "_blank");

        showSnackbar("success", "Print Generated Successfully");
      } else {
        showSnackbar("error", response.data.message);
      }
    } catch (error) {
      showSnackbar("error", error.response?.data?.message || "Something went wrong"
      );
    }
  };

  const sendEmail = async (type,id) => {
    try {
      const { data } = await axios.post(`prints/sendEmail`, { type,id });
      showSnackbar("success", data.message);
    } catch ({ response }) {
      const errorMsg = response?.data?.message || "Something went wrong.";
      showSnackbar("error", errorMsg);
    }
  };

  return {
    print,
    sendEmail,
  };
});
