import { ref, computed } from "vue";
import { defineStore } from "pinia";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";

export const useIndexStore = defineStore("index", () => {
  const { showSnackbar } = useSnackbarStore();
  const baseUrl = ref(import.meta.env.VITE_APP_URL);
  const boUrl = ref(import.meta.env.VITE_APP_BO_URL);

  const loading = ref(false);
  const getLoadingState = computed(() => loading.value);
  const setLoadingState = (v) => (loading.value = v);

  const getCurrencyOption = computed(() => {
    return {
      prefix: "",
      suffix: "",
      thousands: ",",
      decimal: ".",
      precision: 2,
    };
  });

  const signIn = async (user) => {
    const url = `${baseUrl.value}/signIn`;
    try {
      const response = await axiosInstance.post(url, user);
      showSnackbar("success", "LoggedIn successfully");
      return response.data;
    } catch ({ response }) {
      console.log(response);
      showSnackbar("error", response.data.message);
    }
  };

  const stores = ref(null);
  const getStores = computed(
    () =>
      stores.value ||
      JSON.parse(localStorage.getItem("stores")) || [
        {
          id: 297,
          name: "Vallabh Demo Restaurant",
          type: "STORE",
          enableFeedback: false,
        },
        {
          id: 300,
          name: "Vallabh Demo Cafe",
          type: "STORE",
          enableFeedback: false,
        },
      ]
  );

  const fetchDashboardSummary = async (accId, report) => {
    const url = `${boUrl.value}/api/v2/accounts/${accId}/saleDashboard/summary`;
    try {
      const response = await axiosInstance.post(url, report, {
        headers: {
          Authorization: `Bearer ${boToken.value}`,
        },
      });
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
    }
  };

  const fetchDashboardSale = async (accId, report) => {
    const url = `${boUrl.value}/api/v1/accounts/${accId}/saleDashboard`;
    try {
      const response = await axiosInstance.post(url, report, {
        headers: {
          Authorization: `Bearer ${boToken.value}`,
        },
      });
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
    }
  };

  return {
    loading,
    signIn,
    getLoadingState,
    setLoadingState,
    getStores,
    getCurrencyOption,
    fetchDashboardSummary,
    fetchDashboardSale,
  };
});
