import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

const axios = axiosInstance();

export const useLocationStore = defineStore("location", () => {
  const { showSnackbar } = useSnackbarStore();
  const tenantId = ref(getTenantId);
  const Locations = ref([]);
  const selectedLocation = ref(null);
  const getSelectedLocation = computed(() => selectedLocation.value);
  const setSelectedLocation = (data) =>
    (selectedLocation.value = data || Locations.value[0]);


  const getLocations = computed(() => Locations.value);
  const setLocations = (data) => {
    Locations.value = data;
    if (!getSelectedLocation.value) setSelectedLocation(data[0]);
  };

  const fetchLocations = async () => {
    try {
      const response = await axios.get("inventory-locations");
      setLocations(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchLocationsByStore = async (storeId) => {
    try {
      const response = await axios.get(
        `inventory-locations/store-locations/${storeId}`
      );
      setLocations(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createLocation = async (data) => {
    data.tenantId = tenantId.value;
    try {
      await axios.post("inventory-locations", data);
      showSnackbar("green", "Work/Storage Area Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const getLocationById = async (id) => {
    try {
      const response = await axios.get(`inventory-locations/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateLocation = async (data) => {
    try {
      await axios.put(`inventory-locations/${data.id}`, data);
      showSnackbar("green", "Work/Storage Area updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateLocationActiveStatus = async (id, storeId) => {
    try {
      await axios.put(`inventory-locations/${id}/activateStatus`);
      await fetchLocationsByStore(storeId);
      showSnackbar("green", "Work/Storage Area Status Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  // const exportLocations = async () => {
  //   try {
  //     if (categories.value.length === 0) {
  //       showSnackbar("error", "No Work/Storage Area to Export");
  //       return;
  //     }
  //     const response = await axios.get(
  //       "inventory-locations/export/export-excel",
  //       {
  //         params: { tenantId: tenantId.value },
  //         responseType: "blob"
  //       }
  //     );

  //     const blob = new Blob([response.data], {
  //       type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  //     });
  //     const downloadUrl = window.URL.createObjectURL(blob);
  //     const link = document.createElement("a");
  //     link.href = downloadUrl;
  //     link.download = "locations.xlsx";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //     window.URL.revokeObjectURL(downloadUrl);
  //     showSnackbar("green", "Export Completed Successfully");
  //   } catch (error) {
  //     console.error("Export failed", error);
  //     showSnackbar("error", "Export Failed");
  //   }
  // };

  return {
    Locations,
    getLocations,
    fetchLocations,
    fetchLocationsByStore,
    getLocationById,
    createLocation,
    updateLocation,
    updateLocationActiveStatus,
    // exportLocations,
    getSelectedLocation,
    setSelectedLocation
  };
});
