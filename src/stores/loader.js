import { defineStore } from "pinia";
import { ref } from "vue";

export const useLoaderStore = defineStore("loader", () => {
  const loader = ref({
    show: false,
    text: "Loading, please wait...",
  });

  const showLoader = (text = "Loading, please wait...") => {
    loader.value = {
      show: true,
      text,
    };
  };

  const hideLoader = () => {
    loader.value = {
      show: false,
      text: "",
    };
  };

  return {
    loader,
    showLoader,
    hideLoader,
  };
});
