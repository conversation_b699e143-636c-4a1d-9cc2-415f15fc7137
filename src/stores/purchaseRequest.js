import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

const axios = axiosInstance();

export const usePurchaseRequestStore = defineStore("purchaseRequest", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const purchaseRequest = ref([]);
  const getPurchaseRequest = computed(() => purchaseRequest.value);
  const currentTab = ref(1);

  const setPurchaseRequest = (data) => {
    purchaseRequest.value = data;
  };

  const setCurrentTab = (v) => {
    currentTab.value = v;
  };

  const getCurrentTab = computed(() => currentTab.value);

  const fetchPurchaseRequest = async (filters) => {
    try {
      const response = await axios.post(`purchase-requests`, filters);
      setPurchaseRequest(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchPurchaseRequestById = async (id) => {
    try {
      const response = await axios.get(`purchase-requests/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };
  const createPurchaseRequest = async (data) => {
    try {
      await axios.post("purchase-requests/create", data);
      showSnackbar("success", "Purchase request created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updatePurchaseRequest = async (data) => {
    const payload = { ...data, tenantId: tenantId.value };
    try {
      await axios.put(`purchase-requests/${data.id}`, payload);
      showSnackbar("success", "Purchase request updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const approvePurchaseRequest = async (id) => {
    try {
      await axios.post(`purchase-requests/${id}/approve`);
      showSnackbar(
        "success",
        "Purchase request has been approved successfully"
      );
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const rejectPurchaseRequest = async (id, data) => {
    try {
      await axios.post(`purchase-requests/${id}/reject`, data);
      showSnackbar(
        "success",
        "Purchase request has been rejected successfully"
      );
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  return {
    purchaseRequest,
    getPurchaseRequest,
    fetchPurchaseRequest,
    createPurchaseRequest,
    fetchPurchaseRequestById,
    approvePurchaseRequest,
    rejectPurchaseRequest,
    updatePurchaseRequest,
    getCurrentTab,
    setCurrentTab,
  };
});
