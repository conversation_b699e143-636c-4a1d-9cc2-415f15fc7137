import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

const axios = axiosInstance();

export const useCategoryStore = defineStore("category", () => {
  const tenantId = ref(getTenantId);
  const { showSnackbar } = useSnackbarStore();
  const categories = ref([]);

  const getCategories = computed(() => categories.value);
  const setCategories = (data) => {
    categories.value = data;
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get("categories");
      setCategories(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchCategoryById = async (id) => {
    try {
      const response = await axios.get(`categories/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createCategory = async (data) => {
    data.tenantId = tenantId.value;

    try {
      await axios.post("categories", data);
      showSnackbar("green", "Category Created Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateCategory = async (data) => {
    try {
      await axios.put(`categories/${data.id}`, data);
      showSnackbar("green", "Category Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateSubCategory = async (data) => {
    try {
      await axios.post(`categories/${data.id}/subcategories`, data);
      showSnackbar("green", "Subcategory Added Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const deleteCategory = async (id) => {
    try {
      await axios.delete(`categories/${id}`);
      await fetchCategories();
      showSnackbar("green", "Category deleted successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };
  const updateCategoryActiveStatus = async (id) => {
    try {
      await axios.put(`categories/${id}/activateStatus`);
      fetchCategories();
      showSnackbar("green", "Category Status Updated Successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  // const exportCategories = async () => {
  //   try {
  //     if (categories.value.length === 0) {
  //       showSnackbar("error", "No categories to export");
  //       return;
  //     }
  //     const response = await axios.get("categories/export/export-excel", {
  //       params: { tenantId: tenantId.value },
  //       responseType: "blob",
  //     });

  //     const blob = new Blob([response.data], {
  //       type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  //     });
  //     const downloadUrl = window.URL.createObjectURL(blob);
  //     const link = document.createElement("a");
  //     link.href = downloadUrl;
  //     link.download = "categories.xlsx";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //     window.URL.revokeObjectURL(downloadUrl);
  //     showSnackbar("success", "Export Completed Successfully");
  //   } catch (error) {
  //     console.error("Export failed", error);
  //     showSnackbar("error", "Export failed");
  //   }
  // };

  return {
    getCategories,
    setCategories,
    fetchCategories,
    fetchCategoryById,
    createCategory,
    updateCategory,
    updateSubCategory,
    deleteCategory,
    // exportCategories,
    updateCategoryActiveStatus,
  };
});
