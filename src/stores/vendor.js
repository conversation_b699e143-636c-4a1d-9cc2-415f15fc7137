import { defineStore } from "pinia";
import { ref, computed } from "vue";
import axiosInstance from "@/plugin/Axios";
import { useSnackbarStore } from "./snackBar";
import { getTenantId } from "@/helpers/auth";

const axios = axiosInstance();

export const useVendorStore = defineStore("vendor", () => {
  const tenantId = ref(getTenantId);

  const { showSnackbar } = useSnackbarStore();
  const vendors = ref([]);

  const getVendors = computed(() => vendors.value || []);
  const setVendors = (data) => {
    vendors.value = data;
  };

  const fetchVendors = async () => {
    try {
      const response = await axios.get(`vendors`);
      setVendors(response.data);
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const fetchVendorById = async (id) => {
    try {
      const response = await axios.get(`vendors/${id}`);
      return response.data;
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const createVendor = async (data) => {
    try {
      await axios.post("vendors", { ...data, tenantId: tenantId.value });
      showSnackbar("green", "Vendor created successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateVendor = async (data) => {
    try {
      await axios.put(`vendors/${data.id}`, {
        ...data,
        tenantId: tenantId.value,
      });
      showSnackbar("green", "Vendor updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  const updateVendorActiveStatus = async (id) => {
    try {
      await axios.put(`vendors/${id}/activateStatus`);
      fetchVendors();
      showSnackbar("green", "Vendor status updated successfully");
    } catch ({ response }) {
      showSnackbar("error", response.data.message);
      throw Error(response.data.message);
    }
  };

  // const exportVendors = async () => {
  //   try {
  //     if (vendors.value.length === 0) {
  //       showSnackbar("error", "No vendors to export");
  //       return;
  //     }
  //     const response = await axios.get(`vendors/export/export-excel`, {
  //       responseType: "blob"
  //     });

  //     const blob = new Blob([response.data], {
  //       type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  //     });
  //     const downloadUrl = window.URL.createObjectURL(blob);
  //     const link = document.createElement("a");
  //     link.href = downloadUrl;
  //     link.download = "vendors.xlsx";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //     window.URL.revokeObjectURL(downloadUrl);
  //     showSnackbar("green", "Export completed successfully");
  //   } catch (error) {
  //     console.error("Export failed", error);
  //     showSnackbar("error", "Export failed");
  //   }
  // };

  return {
    getVendors,
    setVendors,
    fetchVendors,
    fetchVendorById,
    createVendor,
    updateVendor,
    // exportVendors,
    updateVendorActiveStatus,
  };
});
