import axios from "axios";
import router from "@/router";
import { getAuthorizationToken, getTenantId, logout } from "@/helpers/auth";
import globalData from "@/composables/global";

const tenantId = getTenantId;

// Create axios instance with base configuration
const axiosInstance = () => {
  const instance = axios.create({
    baseURL: globalData.$serverUrl,
    headers: {
      "Content-Type": "application/json",
      "x-tenant-id": tenantId.value
    }
  });

  instance.interceptors.request.use((config) => {
    if (!config.skipTenant) {
      const tenantId = getTenantId; // always fresh
      if (tenantId) {
        config.baseURL = `${globalData.$serverUrl}/tenants/${tenantId.value}`;
      }
    }
    const token = getAuthorizationToken();
    if (token) config.headers["Authorization"] = token;
    return config;
  });
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        //console.log("Unauthorized, redirecting to login...");
        logout();
        router.push("/login");
      } else if (error.response.status === 403) {
        router.push("/not-permitted");
      }

      return Promise.reject(error);
    }
  );
  return instance;
};

export default axiosInstance;
