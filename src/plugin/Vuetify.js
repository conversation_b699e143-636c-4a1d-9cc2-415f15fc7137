import "@mdi/font/css/materialdesignicons.css";
import "vuetify/styles";
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";
import { VDateInput } from "vuetify/labs/VDateInput";

const customTheme = {
  dark: false,
  colors: {
    primary: "#ff5a10",
    secondary: "#223345",
    error: "#B00020",
    success: "#4CAF50",
    info: "#2196F3",
    warning: "#FF9800",
    "white-smoke" : "#fafafa",
  },
};

export default createVuetify({
  components: {
    ...components,
    VDateInput,
  },
  directives,
  theme: {
    defaultTheme: "customTheme",
    themes: {
      customTheme,
    },
  },
});
